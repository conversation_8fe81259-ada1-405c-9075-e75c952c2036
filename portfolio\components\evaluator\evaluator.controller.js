const EvaluatorService = require('./evaluator.service');

const { getPaginationValues, checkIfTrue } = require('../../common/utils/common.util');
const StudentResponseService = require('../student-response/student-response.service');

const getEvaluatorAndApproverDashboard = async ({ query, headers: { user_id: userId } }) => {
    const { institutionCalendarId, type, chipType } = query;

    const result = await EvaluatorService.getEvaluatorAndApproverDashboard({
        institutionCalendarId,
        type,
        chipType,
        userId,
        ...getPaginationValues(query),
    });

    return { statusCode: 200, data: result };
};

const getListOfComponentsForDashboard = async ({
    query: { portfolioId, type },
    headers: { user_id: userId },
}) => {
    const result = await EvaluatorService.getListOfComponentsForDashboard({
        portfolioId,
        type,
        userId,
    });

    return { statusCode: 200, data: result };
};

const getApproverOrEvaluatorComponentsWithCount = async ({
    query: { portfolioId, type },
    headers: { user_id: userId },
}) => {
    const result = await EvaluatorService.getApproverOrEvaluatorComponentsWithCount({
        portfolioId,
        type,
        userId,
    });

    return { statusCode: 200, data: result };
};

const getComponentChildren = async ({
    query: { portfolioId, componentId, type },
    headers: { user_id: userId },
}) => {
    const result = await EvaluatorService.getComponentChildren({
        portfolioId,
        componentId,
        type,
        userId,
    });

    return { statusCode: 200, data: result };
};

const getApproverOrEvaluatorOverallCount = async ({
    query: { portfolioId, componentId, type },
    headers: { user_id: userId },
}) => {
    const result = await EvaluatorService.getApproverOrEvaluatorOverallCount({
        portfolioId,
        componentId,
        type,
        userId,
    });

    return { statusCode: 200, data: result };
};

const getScheduleForApproverOrEvaluator = async ({
    query: { portfolioId, componentId, type, chipType } = {},
    headers: { user_id: userId },
}) => {
    const result = await EvaluatorService.getScheduleForApproverOrEvaluator({
        portfolioId,
        componentId,
        type,
        chipType,
        userId,
    });

    return { statusCode: 200, data: result };
};

const getStudentsForSchedule = async ({ query = {}, headers: { user_id: userId } }) => {
    const {
        portfolioId,
        componentId,
        childrenId,
        scheduleId,
        deliveryTypeId,
        type,
        term,
        year,
        level,
        rotation,
        rotationCount,
    } = query;
    const { pageNo, limit } = getPaginationValues(query);

    const result = await EvaluatorService.getStudentsForSchedule({
        portfolioId,
        componentId,
        childrenId,
        scheduleId,
        deliveryTypeId,
        type,
        term,
        year,
        level,
        rotation,
        rotationCount,
        userId,
        pageNo,
        limit,
    });

    return { statusCode: 200, data: result };
};

const updateApproveOrRejectStatus = async ({
    body: {
        portfolioId,
        componentId,
        childrenId,
        scheduleIds = [],
        studentIds = [],
        type,
        session,
    },
    headers: { user_id: userId },
}) => {
    const result = await EvaluatorService.updateApproveOrRejectStatus({
        portfolioId,
        componentId,
        childrenId,
        scheduleIds,
        studentIds,
        type,
        userId,
        session,
    });

    return { statusCode: 200, data: result };
};

const updateStudentMarksAndRubrics = async ({
    body: {
        portfolioId,
        componentId,
        childrenId,
        scheduleIds = [],
        studentIds = [],
        type,
        rubrics = [],
        globalRubrics = [],
        marks,
        session,
    },
    headers: { user_id: userId },
}) => {
    const result = await EvaluatorService.updateStudentMarksAndRubrics({
        portfolioId,
        componentId,
        childrenId,
        scheduleIds,
        studentIds,
        type,
        rubrics,
        globalRubrics,
        marks,
        userId,
        session,
    });

    return { statusCode: 200, data: result };
};

const getRubricsAndMarksForEvaluator = async ({
    query: { portfolioId, componentId, childrenId },
    headers: { user_id: userId },
}) => {
    const result = await EvaluatorService.getRubricsAndMarksForEvaluator({
        portfolioId,
        componentId,
        childrenId,
        userId,
    });

    return { statusCode: 200, data: result };
};

const getInsightsWithRubricsAndMarks = async ({
    query: { portfolioId, componentId, childrenId, scheduleId, studentId, type },
    headers: { user_id: userId },
}) => {
    const result = await EvaluatorService.getInsightsWithRubricsAndMarks({
        portfolioId,
        componentId,
        childrenId,
        scheduleId,
        studentId,
        userId,
        type,
    });

    return { statusCode: 200, data: result };
};

const getStudentForm = async ({
    query: {
        portfolioId,
        componentId,
        childrenId,
        scheduleId,
        prepareAndPublishId,
        studentId,
        type,
    },
}) => {
    const response = await StudentResponseService.getStudentResponse({
        portfolioId,
        componentId,
        childrenId,
        userId: studentId,
        scheduleId,
        prepareAndPublishId,
        type,
    });

    return { message: 'STUDENT_RESPONSE_FETCHED_SUCCESSFULLY', data: response };
};

module.exports = {
    getEvaluatorAndApproverDashboard,
    getListOfComponentsForDashboard,
    getApproverOrEvaluatorComponentsWithCount,
    getComponentChildren,
    getApproverOrEvaluatorOverallCount,
    getScheduleForApproverOrEvaluator,
    getStudentsForSchedule,
    updateApproveOrRejectStatus,
    updateStudentMarksAndRubrics,
    getRubricsAndMarksForEvaluator,
    getInsightsWithRubricsAndMarks,
    getStudentForm,
};
