const StudentPortfolioModel = require('../portfolio/student-portfolio.model');
const StudentResponseModel = require('../student-response/student-response.model');
const CourseScheduleModel = require('../../../lib/models/course_schedule');
const EvaluationMappingModel = require('../evaluation/evaluation-mapping.model');
const FormModel = require('../form/form.model');

const {
    PUBLISHED,
    SUBMITTED,
    NOT_STARTED,
    APPROVED,
    EVALUATED,
    REJECTED,
} = require('../../common/utils/enums');
const { isIDEquals, convertToMongoObjectId } = require('../../common/utils/common.util');
const { REGULAR, STUDENT } = require('../../common/utils/constants');
const {
    canStudentStartExam,
    canStartExamFromScheduleType,
    isSessionPast,
    getStudentStatus,
    handleExtraEntries,
} = require('./student.helper');
const { getGradeBasedOnPercentage } = require('../report/report.helper');
const { getAssignedUserSections } = require('../form/form.helper');
const { getTotalMarksForChild } = require('../portfolio/portfolio.helper');

const getStudentPortfolio = async ({
    programId,
    courseId,
    institutionCalendarId,
    term,
    year,
    level,
    rotation,
    rotationCount,
    studentId,
}) => {
    const portfolio = await StudentPortfolioModel.findOne(
        {
            programId,
            courseId,
            institutionCalendarId,
            'student._id': studentId,
            status: PUBLISHED,
            term,
            year,
            level,
            ...(rotation && { rotation_no: rotation }),
            ...(rotationCount && { rotation_count: rotationCount }),
        },
        {
            'components._id': 1,
            'components.name': 1,
            'components.code': 1,
            'components.deliveryTypes': 1,
            'components.hasExtraEntries': 1,
            'components.hasReflection': 1,
            'components.isLogBook': 1,
            'components.hasGlobalRubric': 1,
            'components.marks': 1,
            'components.percentage': 1,
            'components.timeline': 1,
            'components.hasConsolidateEvaluation': 1,
            'components.awardedMarks': 1,
            'components.children._id': 1,
            'components.children.name': 1,
            'components.children.marks': 1,
            'components.children.formId': 1,
            'components.children.isExtraEntry': 1,
            'components.children.roles': 1,
            'components.children.formMarks': 1,
            'components.children.session': 1,
            'components.children.hasMultipleApprover': 1,
            'components.children.hasMultipleEvaluator': 1,
            'components.children.hasSimilarityCheck': 1,
            'components.children.awardedMarks': 1,
            'components.children.extraEntries': 1,
            totalMarks: 1,
            status: 1,
            publishedDate: 1,
            portfolioId: 1,
            term: 1,
            year: 1,
            level: 1,
            rotation: 1,
            rotationCount: 1,
            institutionCalendarId: 1,
            programId: 1,
            courseId: 1,
            curriculumId: 1,
            'report.percentage': 1,
            'report.grade': 1,
            'report.hasFailed': 1,
        },
    ).lean();
    if (!portfolio) {
        return {
            count: {
                upcomingCount: 0,
                totalCount: 0,
                completedCount: 0,
                studentResponseCount: 0,
                totalPoints: 0,
            },
            components: [],
        };
    }

    const hasLogBook = portfolio.components.some((component) => component.isLogBook);

    const courseSchedules = hasLogBook
        ? await CourseScheduleModel.find(
              {
                  _institution_calendar_id: institutionCalendarId,
                  _program_id: programId,
                  _course_id: courseId,
                  'students._id': studentId,
                  year_no: year,
                  term,
                  level_no: level,
                  ...(rotation &&
                      rotationCount && { rotation_no: rotation, rotation_count: rotationCount }),
                  type: REGULAR,
                  isDeleted: false,
                  isActive: true,
              },
              {
                  scheduleDate: '$schedule_date',
                  session: 1,
                  start: 1,
                  end: 1,
                  scheduleStartDateAndTime: 1,
                  scheduleEndDateAndTime: 1,
              },
          ).lean()
        : [];

    const studentResponses = await StudentResponseModel.find(
        {
            portfolioId: convertToMongoObjectId(portfolio._id),
            'student._id': studentId,
        },
        {
            status: 1,
            formId: 1,
            _id: 1,
            scheduleId: 1,
            portfolioId: 1,
            childrenId: 1,
            componentId: 1,
            reviews: 1,
            evaluationStatus: 1,
            approvalStatus: 1,
            'prepareAndPublish._id': 1,
            'prepareAndPublish.session': 1,
            'prepareAndPublish.status': 1,
            'prepareAndPublish.formTimestamps.submittedAt': 1,
            'prepareAndPublish.formTimestamps.lastUpdated': 1,
            'prepareAndPublish.formTimestamps.firstStartedAt': 1,
            approvals: 1,
            'deliveryType.deliveryTypeSymbol': 1,
            'deliveryType.deliveryTypeId': 1,
            session: 1,
            globalRubricTotalPoints: 1,
            globalRubricAwardedPoints: 1,
            submissionSession: 1,
        },
    ).lean();

    const evaluations = await EvaluationMappingModel.find(
        {
            portfolioId: convertToMongoObjectId(portfolio.portfolioId),
        },
        {
            typeOfEvaluation: 1,
            deliveryTypeId: 1,
            childrenId: 1,
            componentId: 1,
            infraEvaluation: 1,
            groups: 1,
            students: {
                $elemMatch: {
                    studentId: convertToMongoObjectId(studentId),
                },
            },
        },
    ).lean();

    const roleId = portfolio?.components
        ?.flatMap((c) => c?.children || [])
        ?.flatMap((child) => child?.roles || [])
        ?.find((role) => role?.type === STUDENT)?.roleId;
    portfolio.roleId = roleId;

    portfolio?.components?.forEach((component) => {
        if (component?.deliveryTypes?.length && component?.isLogBook) {
            const schedules = courseSchedules.filter((schedule) => {
                return component.deliveryTypes.some(
                    (deliveryType) =>
                        deliveryType?.deliveryTypeSymbol === schedule?.session?.delivery_symbol,
                );
            });

            const hasExtraEntries = component?.children?.some(
                (child) => child?.extraEntries?.length,
            );

            if (hasExtraEntries) {
                const extraEntries = handleExtraEntries({
                    component,
                    schedules,
                    studentResponses,
                });

                component.schedules = extraEntries;
            } else {
                schedules.forEach((schedule) => {
                    const deliveryType = component?.deliveryTypes?.find(
                        (deliveryType) =>
                            deliveryType?.deliveryTypeSymbol === schedule?.session?.delivery_symbol,
                    );
                    schedule.deliverTypeId = deliveryType?.deliverTypeId;

                    const children = component.children?.map((child) => {
                        const response = studentResponses.find(
                            (r) =>
                                isIDEquals(r.formId, child.formId) &&
                                isIDEquals(r.scheduleId, schedule._id) &&
                                isIDEquals(r.portfolioId, portfolio._id) &&
                                isIDEquals(r.componentId, component._id) &&
                                isIDEquals(r.childrenId, child._id),
                        );

                        const hasApproval = child?.roles?.some((role) => role.peerReview);

                        const canStartExam = canStartExamFromScheduleType({
                            schedule,
                            type: component?.timeline,
                            submissionSession: response?.submissionSession,
                        });

                        return {
                            ...child,
                            reviews: response?.reviews || [],
                            responseId: response?._id,
                            globalRubricTotalPoints: response?.globalRubricTotalPoints,
                            globalRubricAwardedPoints: response?.globalRubricAwardedPoints,
                            awardedMarks: response?.awardedMarks,
                            canStartExam,
                            status: getStudentStatus({
                                status: response?.status,
                                approvalStatus: response?.approvalStatus,
                                evaluationStatus: response?.evaluationStatus,
                                hasApproval,
                            }),
                        };
                    });
                    schedule.children = children;
                });
                component.schedules = schedules;
            }
        } else {
            component.children?.forEach((child) => {
                const response = studentResponses.find(
                    (r) =>
                        isIDEquals(r.formId, child.formId) &&
                        isIDEquals(r.portfolioId, portfolio._id) &&
                        isIDEquals(r.componentId, component._id) &&
                        isIDEquals(r.childrenId, child._id),
                );
                const hasApproval = child?.roles?.some((role) => role.peerReview);
                child.prepareAndPublish = response?.prepareAndPublish || [];
                const evaluation = evaluations.find((e) => isIDEquals(e.childrenId, child._id));
                const student = evaluation?.students?.[0];
                let session = response?.session || {};

                if (!session || !Object.keys(session).length) {
                    const matchedRole = student?.roles?.find(
                        (role) => role?.prepareAndPublish?.length,
                    );
                    session = matchedRole?.prepareAndPublish?.[0]?.session;
                }

                if (!session || !Object.keys(session).length) {
                    session = child?.session || {};
                }

                const userMap = new Map();
                let users = [];

                student?.roles?.forEach((role) => {
                    if (role.approver) {
                        role.users.forEach((user) => {
                            if (!userMap.has(String(user.userId))) {
                                userMap.set(String(user.userId), user);
                            }
                        });
                    }
                });
                users = Array.from(userMap.values());

                child.canStartExam = canStudentStartExam({
                    session,
                    submissionSession: response?.submissionSession,
                });
                child.reviews = response?.reviews || [];
                child.responseId = response?._id;
                child.session = session;
                child.globalRubricTotalPoints = response?.globalRubricTotalPoints;
                child.globalRubricAwardedPoints = response?.globalRubricAwardedPoints;
                if (child?.hasMultipleApprover) {
                    child.totalApprovers = users.length;
                    child.totalApproved = response?.approvals?.filter(
                        (approval) => approval.status === APPROVED,
                    ).length;
                }
                child.status = getStudentStatus({
                    status: response?.status,
                    approvalStatus: response?.approvalStatus,
                    evaluationStatus: response?.evaluationStatus,
                    hasApproval,
                });
            });
        }
    });

    return portfolio;
};

const getGlobalRubric = async ({ portfolioId, componentId, childId, studentId }) => {
    const studentPortfolio = await StudentPortfolioModel.findOne({
        portfolioId,
        'student._id': studentId,
    }).lean();

    const component = studentPortfolio.components.find((component) =>
        isIDEquals(component?._id, componentId),
    );

    const child = component.children.find((child) => isIDEquals(child?._id, childId));
    const rubric = child.rubrics;

    return rubric;
};

const addExtraEntry = async ({
    portfolioId,
    componentId,
    childrenId,
    scheduleId,
    session,
    studentId,
    deliveryTypeId,
    isNewEntry = false,
}) => {
    const studentPortfolio = await StudentPortfolioModel.findOne(
        { _id: portfolioId, 'student._id': studentId },
        {
            'components._id': 1,
            'components.isLogBook': 1,
            'components.children._id': 1,
            'components.children.extraEntries.scheduleId': 1,
            'components.children.formId': 1,
            'components.children.roles': 1,
            'components.deliveryTypes': 1,
            student: 1,
            portfolioId: 1,
        },
    ).lean();

    if (!studentPortfolio) throw new Error('PORTFOLIO_NOT_FOUND');

    const component = studentPortfolio.components.find((c) => isIDEquals(c._id, componentId));
    if (!component) throw new Error('COMPONENT_NOT_FOUND');
    if (!component.isLogBook) throw new Error('ADD_EXTRA_ENTRY_ONLY_FOR_LOG_BOOK');

    const child = component.children.find((c) => isIDEquals(c._id, childrenId));
    if (!child) throw new Error('CHILD_NOT_FOUND');

    const scheduleExists = child.extraEntries?.some((s) => isIDEquals(s.scheduleId, scheduleId));
    const generatedScheduleId = convertToMongoObjectId(scheduleId);
    const updateQuery = scheduleExists
        ? {
              $set: {
                  'components.$[component].children.$[child].extraEntries.$[schedule].session':
                      session,
                  'components.$[component].children.$[child].extraEntries.$[schedule].isNewEntry':
                      isNewEntry,
              },
          }
        : {
              $push: {
                  'components.$[component].children.$[child].extraEntries': {
                      scheduleId: generatedScheduleId,
                      session,
                      isNewEntry,
                  },
              },
          };

    await StudentPortfolioModel.updateOne(
        { _id: portfolioId, 'student._id': studentId },
        updateQuery,
        {
            arrayFilters: [
                { 'component._id': convertToMongoObjectId(componentId) },
                { 'child._id': convertToMongoObjectId(childrenId) },
                ...(scheduleExists
                    ? [{ 'schedule.scheduleId': convertToMongoObjectId(scheduleId) }]
                    : []),
            ],
        },
    );

    if (isNewEntry) {
        const form = await FormModel.findOne(
            {
                _id: child.formId,
            },
            { pages: 1, _id: 0, title: 1, type: 1 },
        ).lean();
        const deliveryType = component.deliveryTypes.find((d) =>
            isIDEquals(d.deliveryTypeId, deliveryTypeId),
        );

        const role = child.roles.find((r) => r.type === STUDENT);
        const pages = getAssignedUserSections({
            pages: form.pages,
            viewSections: role?.viewSections || [],
            modifySections: role?.modifySections || [],
        });

        const totalMarks = getTotalMarksForChild({ child });

        const base = {
            status: NOT_STARTED,
            pages,
            student: studentPortfolio.student,
            totalMarks,
            portfolioId: studentPortfolio._id,
            parentPortfolioId: studentPortfolio.portfolioId,
            componentId: component._id,
            childrenId: child._id,
            formId: child.formId,
            title: form.title,
            type: form.type,
            roles: child.roles,
            scheduleId: generatedScheduleId,
            deliveryType,
        };

        const studentResponse = await StudentResponseModel.create(base);
        return studentResponse;
    }
};

const deleteExtraEntry = async ({ portfolioId, componentId, childId, studentId }) => {
    await StudentPortfolioModel.updateOne(
        { _id: portfolioId, 'student._id': studentId },
        {
            $pull: {
                'components.$[componentId].children': {
                    _id: convertToMongoObjectId(childId),
                    isExtraEntry: true,
                },
            },
        },
        {
            arrayFilters: [{ 'componentId._id': convertToMongoObjectId(componentId) }],
        },
    );
};

const getStudentInsights = async ({ portfolioId, componentId, studentId }) => {
    const studentPortfolio = await StudentPortfolioModel.findOne(
        {
            _id: convertToMongoObjectId(portfolioId),
            'student._id': studentId,
        },
        {
            'components._id': 1,
            'components.isLogBook': 1,
            'components.hasGlobalRubric': 1,
            'components.awardedMarks': 1,
            'components.marks': 1,
            'components.children._id': 1,
            'components.children.session': 1,
            institutionCalendarId: 1,
            programId: 1,
            courseId: 1,
            year: 1,
            term: 1,
            level: 1,
            rotation: 1,
            rotationCount: 1,
            'report.isReportGenerated': 1,
            grades: 1,
        },
    ).lean();

    if (!studentPortfolio) throw new Error('PORTFOLIO_NOT_FOUND');

    const component = studentPortfolio.components.find((c) => isIDEquals(c._id, componentId));
    if (!component) throw new Error('COMPONENT_NOT_FOUND');

    const studentResponses = await StudentResponseModel.find(
        {
            portfolioId: convertToMongoObjectId(portfolioId),
            'student._id': convertToMongoObjectId(studentId),
            componentId: convertToMongoObjectId(componentId),
        },
        {
            approvalStatus: 1,
            evaluationStatus: 1,
            status: 1,
            session: 1,
            scheduleId: 1,
            childrenId: 1,
        },
    ).lean();

    const count = {
        totalEntries: studentResponses.length,
        submittedEntries: 0,
        completedEntries: 0,
        approvedEntries: 0,
        rejectedEntries: 0,
        missedEntries: 0,
        overAllPercentage: 0,
        grade: '-',
        globalRubricTotalPoints: 0,
        globalRubricAwardedPoints: 0,
        isReportGenerated: studentPortfolio?.report?.isReportGenerated || false,
    };

    const courseSchedules = component.isLogBook
        ? await CourseScheduleModel.find(
              {
                  _institution_calendar_id: studentPortfolio.institutionCalendarId,
                  _program_id: studentPortfolio.programId,
                  _course_id: studentPortfolio.courseId,
                  'students._id': convertToMongoObjectId(studentId),
                  year_no: studentPortfolio.year,
                  term: studentPortfolio.term,
                  level_no: studentPortfolio.level,
                  type: REGULAR,
                  isDeleted: false,
                  isActive: true,
                  ...(studentPortfolio?.rotation &&
                      studentPortfolio?.rotationCount && {
                          rotation_no: studentPortfolio.rotation,
                          rotation_count: studentPortfolio.rotationCount,
                      }),
              },
              { scheduleEndDateAndTime: 1 },
          ).lean()
        : [];

    const now = new Date();
    studentResponses.forEach((r) => {
        const { status, approvalStatus, evaluationStatus } = r;

        if (status === SUBMITTED) count.submittedEntries++;
        if (evaluationStatus === EVALUATED) count.completedEntries++;
        if (approvalStatus === APPROVED) count.approvedEntries++;
        if (evaluationStatus === REJECTED) count.rejectedEntries++;

        if (component.isLogBook) {
            const matched = courseSchedules.find((s) => isIDEquals(s._id, r.scheduleId));
            if (
                status === NOT_STARTED &&
                matched &&
                new Date(matched.scheduleEndDateAndTime) < now
            ) {
                count.missedEntries++;
            }
        } else {
            if (!r.session) {
                const child = component.children.find((c) => isIDEquals(c._id, r.childrenId));
                if (child?.session) r.session = child.session;
            }
            if (r.session && isSessionPast(r.session)) count.missedEntries++;
        }
    });

    if (count.isReportGenerated) {
        count.overAllPercentage = Math.round((component.awardedMarks / component.marks) * 100);
        const grade = getGradeBasedOnPercentage({
            percentage: count.overAllPercentage,
            grades: studentPortfolio.grades,
        });
        Object.assign(count, { grade: grade.name, isFailed: grade.isFail });
    }

    return count;
};

module.exports = {
    getStudentPortfolio,
    getGlobalRubric,
    addExtraEntry,
    deleteExtraEntry,
    getStudentInsights,
    getStudentStatus,
};
