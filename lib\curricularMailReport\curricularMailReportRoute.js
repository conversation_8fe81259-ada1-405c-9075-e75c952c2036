const express = require('express');
const router = express.Router();
const catchAsync = require('../utility/catch-async');
const {
    createSchedule,
    getAllSchedules,
    deleteSchedule,
    bulkUpdateSchedules,
    getAdminStaffList,
} = require('./curricularMailReportController');
const {
    createScheduleValidator,
    bulkUpdateSchedulesValidator,
    deleteScheduleValidator,
} = require('./curricularMailReportValidator');
const { userPolicyAuthentication } = require('../../middleware/policy.middleware');
const { validate } = require('../../middleware/validation');
const schedulePolicy = 'curriculum_monitoring:dashboard:customize:schedule_report';
// Create a new report schedule
router.post(
    '/',
    userPolicyAuthentication([schedulePolicy]),
    validate([{ schema: createScheduleValidator, property: 'body' }]),
    catchAsync(createSchedule),
);

// Get all report schedules with pagination and filters
router.get('/', [userPolicyAuthentication([schedulePolicy])], catchAsync(getAllSchedules));

// Delete report schedule
router.delete(
    '/',
    userPolicyAuthentication([schedulePolicy]),
    validate([{ schema: deleteScheduleValidator, property: 'query' }]),
    catchAsync(deleteSchedule),
);

// Bulk create/update schedules
router.post(
    '/bulk',
    userPolicyAuthentication([schedulePolicy]),
    validate([{ schema: bulkUpdateSchedulesValidator, property: 'body' }]),
    catchAsync(bulkUpdateSchedules),
);

// Get admin staff list
router.get(
    '/admin-staff',
    userPolicyAuthentication([schedulePolicy]),
    catchAsync(getAdminStaffList),
);

module.exports = router;
