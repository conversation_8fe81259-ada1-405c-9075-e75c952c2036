const StudentResponseService = require('./student-response.service');
const EvaluationService = require('../evaluation/evaluation.service');
const EvaluatorService = require('../evaluator/evaluator.service');

const startForm = async ({
    query: { portfolioId, componentId, childrenId, scheduleId, prepareAndPublishId },
    headers: { user_id: userId } = {},
}) => {
    const result = await StudentResponseService.startForm({
        prepareAndPublishId,
        portfolioId,
        componentId,
        childrenId,
        userId,
        scheduleId,
    });

    return { message: 'FORM_STARTED_SUCCESSFULLY', data: result };
};

const updateStudentResponse = async ({
    query: { studentResponseId },
    body: { prepareAndPublishId, pageId = '', sectionId = '', section = {} },
    headers: { user_id: userId } = {},
}) => {
    await StudentResponseService.updateStudentResponse({
        studentResponseId,
        prepareAndPublishId,
        pageId,
        sectionId,
        section,
        userId,
    });

    return { statusCode: 200, message: 'FORM_UPDATED_SUCCESSFULLY' };
};

const submitForm = async ({
    query: { studentResponseId, prepareAndPublishId },
    body: { pages = [] },
    headers: { user_id: userId } = {},
}) => {
    const result = await StudentResponseService.submitForm({
        studentResponseId,
        prepareAndPublishId,
        pages,
        userId,
    });

    return { message: 'FORM_SUBMITTED_SUCCESSFULLY', data: result };
};

const getStudentResponse = async ({
    query: { portfolioId, componentId, childrenId, scheduleId, prepareAndPublishId },
    headers: { user_id: userId } = {},
}) => {
    const response = await StudentResponseService.getStudentResponse({
        portfolioId,
        componentId,
        childrenId,
        userId,
        scheduleId,
        prepareAndPublishId,
        type: 'student',
    });

    return { message: 'STUDENT_RESPONSE_FETCHED_SUCCESSFULLY', data: response };
};

const uploadStudentAttachment = async ({
    body: {
        attachment,
        studentResponseId,
        pageId,
        sectionId,
        questionId,
        email,
        prepareAndPublishId,
    },
}) => {
    const signedUrl = await StudentResponseService.uploadStudentAttachment({
        studentResponseId,
        pageId,
        sectionId,
        questionId,
        attachment,
        email,
        prepareAndPublishId,
    });

    return { message: 'ATTACHMENT_UPDATED_SUCCESSFULLY', data: signedUrl };
};

const deleteStudentAttachment = async ({
    query: { studentResponseId, pageId, sectionId, questionId, email },
}) => {
    await StudentResponseService.deleteStudentAttachment({
        studentResponseId,
        pageId,
        sectionId,
        questionId,
        email,
    });

    return { message: 'ATTACHMENT_DELETED_SUCCESSFULLY' };
};

const getEvaluatorAndApproverForStudent = async ({
    query: { componentId, childrenId, deliveryTypeId, scheduleId },
    headers: { user_id: userId } = {},
}) => {
    const result = await StudentResponseService.getEvaluatorAndApproverForStudent({
        componentId,
        childrenId,
        deliveryTypeId,
        scheduleId,
        studentId: userId,
    });

    return { statusCode: 200, data: result };
};

const uploadSimilarityCheckAttachment = async ({
    body: { attachment, studentResponseId, prepareAndPublishId },
}) => {
    await StudentResponseService.uploadSimilarityCheckAttachment({
        studentResponseId,
        attachment,
        prepareAndPublishId,
    });

    return { message: 'SIMILARITY_CHECK_ATTACHMENT_UPDATED_SUCCESSFULLY' };
};

const deleteSimilarityCheckAttachment = async ({
    query: { studentResponseId, prepareAndPublishId },
}) => {
    await StudentResponseService.deleteSimilarityCheckAttachment({
        studentResponseId,
        prepareAndPublishId,
    });

    return { message: 'SIMILARITY_CHECK_ATTACHMENT_DELETED_SUCCESSFULLY' };
};

const updateSimilarityCheck = async ({
    body: { studentResponseId, prepareAndPublishId, isSimilarityCheck },
}) => {
    await StudentResponseService.updateSimilarityCheck({
        studentResponseId,
        prepareAndPublishId,
        isSimilarityCheck,
    });

    return { message: 'SIMILARITY_CHECK_UPDATED_SUCCESSFULLY' };
};

const addReview = async ({
    body: { studentResponseId, reviewId, text, roleId, externalEmail },
    headers: { user_id: userId } = {},
}) => {
    await StudentResponseService.addReview({
        studentResponseId,
        reviewId,
        text,
        userId,
        roleId,
        externalEmail,
    });

    return { message: 'REVIEW_UPDATED_SUCCESSFULLY' };
};

const getReview = async ({ query: { studentResponseId } }) => {
    const review = await StudentResponseService.getReview({ studentResponseId });
    return { message: 'REVIEW_FETCHED_SUCCESSFULLY', data: review };
};

const updateAndDeleteReview = async ({
    body: { studentResponseId, reviewId, threadId, text },
    headers: { user_id: userId } = {},
}) => {
    await StudentResponseService.updateAndDeleteReview({
        studentResponseId,
        reviewId,
        threadId,
        text,
        userId,
    });

    return { message: 'REVIEW_UPDATED_SUCCESSFULLY' };
};

const addExternalEvaluator = async ({
    body: {
        email,
        name,
        roleId,
        mobile,
        gender,
        componentId,
        childrenId,
        deliveryTypeId,
        isEvaluator,
    },
    headers: { user_id: userId } = {},
}) => {
    await EvaluationService.addExternalEvaluator({
        email,
        name,
        roleId,
        mobile,
        gender,
        componentId,
        childrenId,
        deliveryTypeId,
        studentId: userId,
        isEvaluator,
    });

    return { statusCode: 200, message: 'VERIFICATION_CODE_SENT_SUCCESSFULLY' };
};

const reSendVerificationCode = async ({
    body: { email, roleId, componentId, childrenId, deliveryTypeId },
    headers: { user_id: userId } = {},
}) => {
    await EvaluationService.reSendVerificationCode({
        email,
        roleId,
        componentId,
        childrenId,
        deliveryTypeId,
        studentId: userId,
    });

    return { statusCode: 200, data: 'VERIFICATION_CODE_RESENT_SUCCESSFULLY' };
};

const verifyVerificationCode = async ({
    body: { email, code, roleId, componentId, childrenId, deliveryTypeId },
    headers: { user_id: userId } = {},
}) => {
    const verification = await EvaluationService.verifyVerificationCode({
        email,
        code,
        roleId,
        componentId,
        childrenId,
        deliveryTypeId,
        studentId: userId,
    });

    return { statusCode: 200, data: verification };
};

const updateExternalEvaluatorImage = async ({
    body: { email, roleId, componentId, childrenId, deliveryTypeId },
    headers: { user_id: userId } = {},
}) => {
    await EvaluationService.updateExternalEvaluatorImage({
        email,
        roleId,
        componentId,
        childrenId,
        deliveryTypeId,
        studentId: userId,
    });
};

const getInsightsWithRubricsAndMarks = async ({
    query: { portfolioId, componentId, childrenId, scheduleId, studentId, type },
    headers: { user_id: userId },
}) => {
    const result = await EvaluatorService.getInsightsWithRubricsAndMarks({
        portfolioId,
        componentId,
        childrenId,
        scheduleId,
        studentId,
        userId,
        type,
        isExternalUser: true,
    });

    return { statusCode: 200, data: result };
};

const updateStudentMarksAndRubrics = async ({
    body: {
        portfolioId,
        componentId,
        childrenId,
        scheduleId,
        studentId,
        type,
        rubrics = [],
        globalRubrics = [],
        marks,
        session,
    },
    headers: { user_id: userId },
}) => {
    const result = await EvaluatorService.updateStudentMarksAndRubrics({
        portfolioId,
        componentId,
        childrenId,
        scheduleIds: scheduleId ? [scheduleId] : [],
        studentIds: studentId ? [studentId] : [],
        type,
        rubrics,
        globalRubrics,
        marks,
        userId: String(userId).toLowerCase(),
        isExternalUser: true,
        session,
    });

    return { statusCode: 200, data: result };
};

const updateFacialRecognition = async ({
    body: { email, roleId, componentId, childrenId, deliveryTypeId, attachment },
    headers: { user_id: userId } = {},
}) => {
    await StudentResponseService.updateFacialRecognition({
        email,
        roleId,
        componentId,
        childrenId,
        deliveryTypeId,
        attachment,
        studentId: userId,
    });
};

const updateApproveOrRejectStatus = async ({
    body: { portfolioId, componentId, childrenId, scheduleId, studentId, type, session },
    headers: { user_id: userId },
}) => {
    const result = await EvaluatorService.updateApproveOrRejectStatus({
        portfolioId,
        componentId,
        childrenId,
        scheduleIds: scheduleId ? [scheduleId] : [],
        studentIds: studentId ? [studentId] : [],
        type,
        userId,
        session,
        isExternalUser: true,
    });

    return { statusCode: 200, data: result };
};

const getStudentDetails = async ({
    query: { portfolioId, componentId, childrenId, scheduleId, studentId },
}) => {
    const result = await StudentResponseService.getStudentDetails({
        portfolioId,
        componentId,
        childrenId,
        scheduleId,
        studentId,
    });

    return { statusCode: 200, data: result };
};

module.exports = {
    startForm,
    updateStudentResponse,
    submitForm,
    getStudentResponse,
    uploadStudentAttachment,
    deleteStudentAttachment,
    getEvaluatorAndApproverForStudent,
    uploadSimilarityCheckAttachment,
    deleteSimilarityCheckAttachment,
    updateSimilarityCheck,
    addReview,
    getReview,
    updateAndDeleteReview,
    addExternalEvaluator,
    reSendVerificationCode,
    verifyVerificationCode,
    getInsightsWithRubricsAndMarks,
    updateStudentMarksAndRubrics,
    updateFacialRecognition,
    updateApproveOrRejectStatus,
    getStudentDetails,
};
