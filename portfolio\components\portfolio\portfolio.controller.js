const PortfolioService = require('./portfolio.service');
const FormService = require('../form/form.service');

const { getPaginationValues } = require('../../common/utils/common.util');
const { PUBLISHED } = require('../../common/utils/enums');

const getPortfolio = async ({
    query: {
        programId,
        courseId,
        institutionCalendarId,
        term,
        year,
        level,
        curriculumId,
        curriculumName,
        rotation,
        rotationCount,
    },
}) => {
    const portfolio = await PortfolioService.getPortfolio({
        programId,
        courseId,
        institutionCalendarId,
        term,
        year,
        level,
        curriculumId,
        curriculumName,
        rotation,
        rotationCount,
    });

    return { statusCode: 200, data: portfolio };
};

const updatePortfolio = async ({
    query: { portfolioId },
    body: { name, description, components, totalMarks },
}) => {
    const { portfolio, message } = await PortfolioService.updatePortfolio({
        portfolioId,
        name,
        description,
        components,
        totalMarks,
    });

    return { statusCode: 200, data: portfolio, message };
};

const publishPortfolio = async ({ query: { portfolioId } }) => {
    await PortfolioService.publishPortfolio({ portfolioId });

    return { statusCode: 200, data: 'PORTFOLIO_PUBLISHED_SUCCESSFULLY' };
};

const getComponentForm = async ({ query: { formId, portfolioId, componentId, childrenId } }) => {
    const form = await PortfolioService.getComponentForm({
        formId,
        portfolioId,
        componentId,
        childrenId,
    });

    return { statusCode: 200, data: form };
};

const getPortfolioDashboard = async ({ query = {} }) => {
    const { limit, pageNo, skip } = getPaginationValues(query);
    const { institutionCalendarId, programId, type, courseId, search } = query;
    const portfolios = await PortfolioService.getPortfolioDashboard({
        institutionCalendarId,
        programId,
        courseId,
        type,
        limit,
        pageNo,
        skip,
        search,
    });

    return { statusCode: 200, data: portfolios };
};

const detachPortfolioFrom = async ({ query: { portfolioId, componentId, childrenId } }) => {
    await PortfolioService.detachPortfolioFrom({ portfolioId, componentId, childrenId });

    return { statusCode: 200, data: 'FORM_DETACHED_SUCCESSFULLY' };
};

const deletePortfolio = async ({ query: { portfolioId } }) => {
    await PortfolioService.deletePortfolio({ portfolioId });

    return { statusCode: 200, data: 'PORTFOLIO_DELETED_SUCCESSFULLY' };
};

const assignStudentToPortfolio = async ({ body = {}, headers: { user_id: userId } = {} }) => {
    await PortfolioService.assignStudentToPortfolio({
        ...body,
        userId,
    });

    return { statusCode: 200, data: 'STUDENT_ASSIGNED_SUCCESSFULLY' };
};

const getStudentsByCourse = async ({ query = {} }) => {
    const students = await PortfolioService.getStudentsByCourse(query);

    return { statusCode: 200, data: students };
};

const updateStudentReview = async ({
    query: { componentId, childrenId, scheduleId, studentId, userId },
    body: { text },
}) => {
    await PortfolioService.updateStudentReview({
        componentId,
        childrenId,
        scheduleId,
        studentId,
        userId,
        text,
    });

    return { statusCode: 200, data: 'REVIEW_UPDATED_SUCCESSFULLY' };
};

const getPortfolioForAssignEvaluator = async ({ query = {} }) => {
    const portfolio = await PortfolioService.getPortfolioForAssignEvaluator(query);

    return { statusCode: 200, data: portfolio };
};

const getForms = async ({ query: { formId, isPages, type } }) => {
    const forms = await FormService.getForms({
        formId,
        isPages,
        type,
        status: PUBLISHED,
    });

    return { statusCode: 200, data: forms };
};

const updateRolesInPortfolio = async ({
    query: { portfolioId, componentId, childrenId },
    body: {
        roles,
        rubrics = [],
        marks = 0,
        isMarkSelected = false,
        hasMultipleApprover = false,
        hasMultipleEvaluator = false,
    },
}) => {
    await PortfolioService.updatePortfolioChildField({
        portfolioId,
        componentId,
        childrenId,
        update: {
            'components.$[componentId].children.$[childrenId].roles': roles,
            'components.$[componentId].children.$[childrenId].rubrics': rubrics,
            'components.$[componentId].children.$[childrenId].formMarks': marks,
            'components.$[componentId].children.$[childrenId].isMarkSelected': isMarkSelected,
            'components.$[componentId].children.$[childrenId].hasMultipleApprover':
                hasMultipleApprover,
            'components.$[componentId].children.$[childrenId].hasMultipleEvaluator':
                hasMultipleEvaluator,
        },
    });

    return { statusCode: 200, message: 'ROLES_UPDATED_SUCCESSFULLY' };
};

const updateFormInPortfolio = async ({
    body: { title, type, pages },
    headers: { user_id: userId },
}) => {
    const result = await PortfolioService.updateFormInPortfolio({
        title,
        type,
        pages,
        userId,
    });

    return { statusCode: 200, message: 'FORM_UPDATED_SUCCESSFULLY', data: result };
};

const attachFormToPortfolio = async ({
    query: { portfolioId, componentId, childrenId },
    body: { formId },
}) => {
    await PortfolioService.updatePortfolioChildField({
        portfolioId,
        componentId,
        childrenId,
        update: { 'components.$[componentId].children.$[childrenId].formId': formId },
    });

    return { statusCode: 200, message: 'FORM_ATTACHED_SUCCESSFULLY' };
};

const getCountForPortfolioDashboard = async ({ query: { institutionCalendarId, programId } }) => {
    const count = await PortfolioService.getCountForPortfolioDashboard({
        institutionCalendarId,
        programId,
    });

    return { statusCode: 200, data: count };
};

const getInsights = async ({ query: { institutionCalendarId, programId } }) => {
    const count = await PortfolioService.getInsights({
        institutionCalendarId,
        programId,
    });

    return { statusCode: 200, data: count };
};

module.exports = {
    getPortfolio,
    updatePortfolio,
    publishPortfolio,
    getComponentForm,
    getPortfolioDashboard,
    detachPortfolioFrom,
    deletePortfolio,
    assignStudentToPortfolio,
    getStudentsByCourse,
    updateStudentReview,
    getPortfolioForAssignEvaluator,
    getForms,
    updateRolesInPortfolio,
    updateFormInPortfolio,
    attachFormToPortfolio,
    getCountForPortfolioDashboard,
    getInsights,
};
