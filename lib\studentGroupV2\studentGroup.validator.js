const Joi = require('joi');
const {
    objectIdRQSchema,
    stringRQSchema,
    optionalKey,
    arrayOfObjectIds,
    booleanSchema,
    objectIdSchema,
    numberRQSchema,
    arrayOfString,
    arrayOfStringOptional,
    stringSchema,
} = require('../utility/validationSchemas');
const {
    YEAR_LEVEL_AUTHOR_TYPE_MODULE: { STUDENT_GROUP, SCHEDULE },
    // GENDER: { MALE, FEMALE, BOTH },
} = require('../utility/constants');

const userAuthorProgramListValidation = {
    schema: Joi.object({
        institutionCalendarId: Joi.string().optional(),
        userId: objectIdRQSchema.optional(),
        module: Joi.string().valid(SCHEDULE, STUDENT_GROUP).optional(),
        withDetail: Joi.boolean().optional(),
    }),
    property: 'query',
};

const courseDeliveryTypeValidation = {
    query: Joi.object({
        institutionCalendarId: objectIdRQSchema,
        programId: objectIdRQSchema,
        year: optionalKey,
        level: optionalKey,
        term: stringRQSchema,
        courseId: objectIdSchema,
    }),
};

const selectedGroupSettingValidation = {
    headers: Joi.object({
        _institution_id: objectIdRQSchema,
    }).unknown(),
    body: Joi.object({
        institutionCalendarId: objectIdRQSchema,
        programId: objectIdRQSchema,
        selectedType: optionalKey,
        year: optionalKey,
        level: optionalKey,
        term: stringRQSchema,
        courseIds: arrayOfObjectIds,
        autoGenerate: booleanSchema,
        upcomingYears: arrayOfStringOptional,
        importedStudents: Joi.array()
            .items(
                Joi.object().keys({
                    academicId: stringSchema,
                    mark: stringSchema,
                }),
            )
            .optional(),
        removedImportedGroupingIds: arrayOfObjectIds,
        deliveryGroups: Joi.array()
            .items(
                Joi.object().keys({
                    gender: stringRQSchema,
                    deliveryTypes: Joi.array()
                        .items(
                            Joi.object().keys({
                                typeName: stringRQSchema,
                                selectedType: Joi.array().items(
                                    Joi.object().keys({
                                        deliveryType: stringRQSchema,
                                        deliverySymbol: stringRQSchema,
                                        noOfGroups: numberRQSchema,
                                        groupName: optionalKey,
                                    }),
                                ),
                            }),
                        )
                        .optional(),
                }),
            )
            .optional(),
    }),
};

const editGroupSettingValidation = {
    body: Joi.object({
        groupSettingId: objectIdRQSchema,
        year: optionalKey,
        level: optionalKey,
        selectedType: optionalKey,
        courseIds: arrayOfObjectIds,
        autoGenerate: booleanSchema,
        upcomingYears: arrayOfStringOptional,
        importedStudents: Joi.array()
            .items(
                Joi.object().keys({
                    academicId: stringSchema,
                    mark: stringSchema,
                }),
            )
            .optional(),
        removedImportedGroupingIds: arrayOfObjectIds,
        deliveryGroups: Joi.array()
            .items(
                Joi.object().keys({
                    gender: stringRQSchema,
                    deliveryTypes: Joi.array()
                        .items(
                            Joi.object().keys({
                                typeName: stringRQSchema,
                                selectedType: Joi.array().items(
                                    Joi.object().keys({
                                        deliveryType: stringRQSchema,
                                        deliverySymbol: stringRQSchema,
                                        noOfGroups: numberRQSchema,
                                        groupName: optionalKey,
                                        isGrouped: booleanSchema,
                                    }),
                                ),
                            }),
                        )
                        .optional(),
                }),
            )
            .optional(),
        groupStudents: booleanSchema,
        isDeleted: booleanSchema,
    }),
};

const selectedProgramListValidation = {
    query: Joi.object({
        institutionCalendarId: objectIdRQSchema,
        programId: objectIdRQSchema,
        year: optionalKey,
        selectedType: optionalKey,
        courseId: objectIdSchema,
        level: optionalKey,
    }),
};

const publishedStudentGroupingValidation = {
    headers: Joi.object({
        _institution_id: objectIdRQSchema,
    }).unknown(),
    body: Joi.object({
        groupSettingId: objectIdRQSchema,
        institutionCalendarId: objectIdRQSchema,
    }),
};

const importedStudentViewListValidation = {
    query: Joi.object({
        groupSettingId: objectIdRQSchema,
        genderType: optionalKey,
        studentGender: optionalKey,
        searchKey: optionalKey,
        pageNo: optionalKey,
        limit: optionalKey,
        groupType: optionalKey,
    }),
};

const getSingleStudentDetailsValidation = {
    query: Joi.object({
        groupSettingId: objectIdRQSchema,
        academicId: stringRQSchema,
    }),
};

const addSingleStudentValidation = {
    headers: Joi.object({
        _institution_id: objectIdRQSchema,
        _user_id: objectIdRQSchema,
    }).unknown(),
    body: Joi.object({
        groupSettingId: objectIdRQSchema,
        academicId: stringRQSchema,
        mark: stringRQSchema,
        studentId: objectIdRQSchema,
    }),
};

const autoGenerateGroupNamesValidation = {
    headers: Joi.object({
        _institution_id: objectIdRQSchema,
    }).unknown(),
    query: Joi.object({
        institutionCalendarId: objectIdRQSchema,
        programId: objectIdRQSchema,
        year: optionalKey,
        level: optionalKey,
        term: stringRQSchema,
        courseIds: arrayOfObjectIds,
    }),
};

const deleteStudentsValidation = {
    body: Joi.object({
        groupSettingId: objectIdRQSchema,
        studentIds: arrayOfObjectIds,
        removeCompleteCourse: booleanSchema,
    }),
};

const groupingStudentValidation = {
    body: Joi.object({
        groupSettingId: objectIdRQSchema,
        genderType: optionalKey,
        studentIds: arrayOfObjectIds,
        isAllStudent: booleanSchema,
        deliveryTypes: arrayOfString,
        groupingMethod: optionalKey,
        groupingType: optionalKey,
        removedStudentIds: arrayOfObjectIds,
        moveTo: booleanSchema,
        studentAttendanceStatus: optionalKey,
        selectedDeliveryType: Joi.array()
            .items(
                Joi.object().keys({
                    deliveryType: optionalKey,
                    groupNo: numberRQSchema,
                }),
            )
            .optional(),
    }),
};

const totalCompletedSessionValidation = {
    headers: Joi.object({
        _institution_id: objectIdRQSchema,
    }).unknown(),
    body: Joi.object({
        institutionCalendarId: objectIdRQSchema,
        programId: objectIdRQSchema,
        year: optionalKey,
        level: optionalKey,
        term: stringRQSchema,
        gender: optionalKey,
        courseIds: arrayOfObjectIds,
        selectedDeliveryType: Joi.array()
            .items(
                Joi.object().keys({
                    deliverySymbol: optionalKey,
                    groupNo: numberRQSchema,
                }),
            )
            .optional(),
    }),
};

const yearWiseImportedStudentListValidation = {
    headers: Joi.object({
        _institution_id: objectIdRQSchema,
    }).unknown(),
    query: Joi.object({
        institutionCalendarId: objectIdRQSchema,
        programId: objectIdRQSchema,
        year: optionalKey,
        level: optionalKey,
        term: stringRQSchema,
        gender: optionalKey,
        courseIds: arrayOfObjectIds,
        studentGroupSettingId: objectIdRQSchema,
        userSelectedType: optionalKey,
        genderType: optionalKey,
        groupType: optionalKey,
        studentGender: optionalKey,
    }),
};

module.exports = {
    userAuthorProgramListValidation,
    selectedGroupSettingValidation,
    courseDeliveryTypeValidation,
    editGroupSettingValidation,
    selectedProgramListValidation,
    publishedStudentGroupingValidation,
    importedStudentViewListValidation,
    getSingleStudentDetailsValidation,
    addSingleStudentValidation,
    autoGenerateGroupNamesValidation,
    deleteStudentsValidation,
    groupingStudentValidation,
    totalCompletedSessionValidation,
    yearWiseImportedStudentListValidation,
};
