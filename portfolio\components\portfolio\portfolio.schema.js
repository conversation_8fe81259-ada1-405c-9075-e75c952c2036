const {
    Types: { ObjectId },
} = require('mongoose');

const { rubric } = require('../rubric/rubric.schema');
const {
    WITH_IN_WEEK,
    WITH_IN_DAY,
    OPEN_ALWAYS,
    SINGLE,
    MULTIPLE,
    WITH_IN_SCHEDULE,
} = require('../../common/utils/constants');
const { ABSENT, PRESENT } = require('../../common/utils/enums');
const constant = require('../../../lib/utility/constants');

const roleSchema = {
    name: { type: String },
    roleId: { type: ObjectId },
    viewSections: [
        {
            isEnabled: { type: Boolean, default: false },
            sectionId: { type: ObjectId },
        },
    ],
    modifySections: [
        {
            isEnabled: { type: Boolean, default: false },
            sectionId: { type: ObjectId },
        },
    ],
    evaluate: {
        isEnabled: { type: Boolean, default: false },
        marks: { type: Number },
        rubrics: [],
        isMarkSelected: { type: Boolean, default: false },
    },
    peerReview: { type: Boolean, default: false },
    _id: false,
    type: { type: String },
};

const portfolioSchema = {
    institutionCalendarId: {
        type: ObjectId,
        required: true,
    },
    programId: {
        type: ObjectId,
        required: true,
        ref: constant.DIGI_PROGRAM,
    },
    courseId: {
        type: ObjectId,
        required: true,
        ref: constant.DIGI_COURSE,
    },
    term: {
        type: String,
        required: true,
    },
    year: {
        type: String,
        required: true,
    },
    level: {
        type: String,
        required: true,
    },
    rotation: {
        type: String,
    },
    rotationCount: {
        type: Number,
    },
    curriculumId: {
        type: ObjectId,
    },
    curriculumName: {
        type: String,
    },
    name: {
        type: String,
    },
    description: {
        type: String,
    },
    components: [
        {
            name: { type: String },
            code: { type: String },
            componentId: { type: ObjectId },
            marks: { type: Number },
            percentage: { type: Number },
            hasExtraEntries: { type: Boolean, default: false },
            hasReflection: { type: Boolean, default: false },
            isLogBook: { type: Boolean, default: false },
            deliveryTypes: [
                {
                    sessionId: { type: ObjectId },
                    deliveryTypeId: { type: ObjectId },
                    deliveryTypeName: { type: String },
                    deliveryTypeSymbol: { type: String },
                },
            ],
            hasConsolidateEvaluation: { type: Boolean, default: false },
            rubrics: [rubric],
            hasGlobalRubric: { type: Boolean, default: false },
            hasNoGrade: { type: Boolean, default: false },
            timeline: {
                type: String,
                enum: [WITH_IN_WEEK, WITH_IN_DAY, OPEN_ALWAYS, WITH_IN_SCHEDULE],
            },
            children: [
                {
                    name: { type: String },
                    percentage: { type: Number },
                    marks: { type: Number },
                    formId: { type: ObjectId },
                    templateId: { type: ObjectId },
                    awardedMarks: { type: Number },
                    isExtraEntry: { type: Boolean, default: false },
                    startDate: { type: Date },
                    endDate: { type: Date },
                    isAssigned: { type: Boolean, default: false },
                    attendance: { type: String, enum: [ABSENT, PRESENT] },
                    passMarks: { type: Number },
                    mustPass: { type: Boolean, default: false },
                    globalRubricAwardedPoints: { type: Number },
                    globalRubricTotalPoints: { type: Number },
                    hasSimilarityCheck: { type: Boolean, default: false },
                    hasMultipleApprover: { type: Boolean, default: false },
                    hasMultipleEvaluator: { type: Boolean, default: false },
                    roles: [roleSchema],
                    rubrics: [],
                    globalRubrics: [],
                    formMarks: { type: Number },
                    isMarkSelected: { type: Boolean, default: false },
                    session: {
                        mode: { type: String, enum: [SINGLE, MULTIPLE] },
                        startDate: { type: Date },
                        endDate: { type: Date },
                        start: {
                            hour: { type: Number },
                            minute: { type: Number },
                            format: { type: String },
                        },
                        end: {
                            hour: { type: Number },
                            minute: { type: Number },
                            format: { type: String },
                        },
                        date: { type: Date },
                    },
                    extraEntries: [
                        {
                            scheduleId: { type: ObjectId, ref: constant.COURSE_SCHEDULE },
                            session: {
                                date: { type: Date },
                                start: {
                                    hour: { type: Number },
                                    minute: { type: Number },
                                    format: { type: String },
                                },
                                end: {
                                    hour: { type: Number },
                                    minute: { type: Number },
                                    format: { type: String },
                                },
                            },
                            isNewEntry: { type: Boolean, default: false },
                        },
                    ],
                },
            ],

            attendance: { type: String, enum: [ABSENT, PRESENT] },
            awardedMarks: { type: Number },
        },
    ],
    totalMarks: {
        type: Number,
        default: 0,
    },
    isDeleted: {
        type: Boolean,
        default: false,
    },
    status: {
        type: String,
    },
    publishedDate: { type: Date },
    isReportGenerated: { type: Boolean, default: false },
    grades: [
        {
            from: { type: Number },
            to: { type: Number },
            name: { type: String },
            isFail: { type: Boolean },
        },
    ],
};

module.exports = { portfolioSchema, roleSchema };
