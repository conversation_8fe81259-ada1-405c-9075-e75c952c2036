const { logger } = require('../utility/util_keys');
const {
    programList,
    programYearLevelList,
    getCourseBasedDelivery,
    getProgramCalenderCourseIds,
    checkStudentRegister,
    updateOldStudentGroupData,
    notRegisterUserDetails,
    alreadyInStudentGroup,
    getAutoGenerateGroupName,
    updatedStudentGrouping,
    groupingDeliveryData,
    groupWiseListStudent,
    studentGroupDashboardService,
    studentRemoveFromCourseSchedule,
    getTotalSchedulingCount,
    getYearWiseImportedStudentList,
    checkStudentsAreImported,
    studentPushInExistingSchedule,
    createDeliveryGroups,
    removedImportedStudentsIds,
} = require('./studentGroup.service');
const { getActiveInstitutionCalendars } = require('../utility/utility.service');
const { getUserRoleProgramListWithYearLevel } = require('../utility/utility.service');
const { convertToMongoObjectId, query: queryConstant } = require('../utility/common');
const {
    YEAR_LEVEL_AUTHOR_TYPE_MODULE: { YEAR, LEVEL },
    EVENT_WHOM: { STAFF },
} = require('../utility/constants');
const studentGroupSettingSchema = require('./studentGroupSetting.model');
const studentGroupSchema = require('../models/student_group');

const userAuthorProgramList = async ({ query = {}, headers = {} }) => {
    try {
        const { _institution_id, user_id, role_id } = headers;
        let { institutionCalendarId } = query;
        const { module, withDetail = false } = query;

        if (!institutionCalendarId) {
            const { activeCalendarIds } = await getActiveInstitutionCalendars({ _institution_id });
            institutionCalendarId = [activeCalendarIds[0]];
        } else if (!Array.isArray(institutionCalendarId))
            institutionCalendarId = [institutionCalendarId];

        // Convert withDetail to boolean
        const withDetailBoolean = withDetail === 'true' || withDetail === true;

        const queryParsing = {
            _institution_id,
            user_id,
            role_id,
            institutionCalendarId,
            module,
            withDetail: withDetailBoolean,
        };
        logger.info(queryParsing, 'studentGroupMaster -> userAuthorProgramList -> Start');
        const { userProgramIds } = await getUserRoleProgramListWithYearLevel(queryParsing);
        logger.info(queryParsing, 'studentGroupMaster -> userAuthorProgramList -> End');

        const programDatas = await programList({
            programIds: userProgramIds,
        });

        return { statusCode: 200, message: 'DATA_RETRIEVED', data: programDatas };
    } catch (error) {
        console.log(error);
        logger.error({ error }, 'studentGroupMaster -> userAuthorProgramList -> Error');
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const userAuthorProgramYearLevelList = async ({ query = {}, headers = {} }) => {
    try {
        const { _institution_id, user_id, role_id } = headers;
        const { module, withDetail = true, institutionCalendarId, programId, term } = query;

        const queryParsing = {
            _institution_id,
            user_id,
            role_id,
            institutionCalendarId,
            programId,
            term,
            module,
            withDetail,
        };
        logger.info(queryParsing, 'studentGroupMaster -> userAuthorProgramYearLevelList -> Start');
        // studentGroupDashboardService({
        //     _institution_id,
        //     institutionCalendarId,
        //     programId,
        // });
        const userRoleProgramListData = await getUserRoleProgramListWithYearLevel(queryParsing);
        const programYearLevelData = await programYearLevelList(queryParsing);
        let filteredData = [];

        if (userRoleProgramListData.isYearLevelAuthor) {
            const yearLevelDetails = userRoleProgramListData.yearLevelAuthorDetails;

            const authorizedYears = new Set(
                yearLevelDetails
                    .filter(({ authorType }) => authorType === YEAR)
                    .map(({ yearName }) => yearName),
            );
            const authorizedLevels = new Set(
                yearLevelDetails
                    .filter(({ authorType }) => authorType === LEVEL)
                    .map(({ levelName }) => levelName),
            );

            filteredData = programYearLevelData.reduce((result, yearLevelElement) => {
                const { year, level } = yearLevelElement;
                if (authorizedYears.has(year)) {
                    result.push(yearLevelElement);
                    return result;
                }
                const filteredLevels = level.filter(({ level_no }) =>
                    authorizedLevels.has(level_no),
                );
                if (filteredLevels.length) {
                    result.push({
                        ...yearLevelElement,
                        level: filteredLevels,
                    });
                }

                return result;
            }, []);
        } else if (userRoleProgramListData.isCourseAdmin) {
            const authorizedCourses = new Set(
                userRoleProgramListData.userCourseIds.map(
                    ({ _course_id, _institution_calendar_id, level_no, term }) =>
                        `${_course_id}_${_institution_calendar_id}_${level_no}_${term}`,
                ),
            );

            filteredData = programYearLevelData.reduce((result, yearLevelElement) => {
                const filteredLevels = yearLevelElement.level.reduce((levels, levelElement) => {
                    const filteredCourses = levelElement.course.filter((courseElement) =>
                        authorizedCourses.has(
                            `${courseElement._course_id}_${institutionCalendarId}_${levelElement.level_no}_${yearLevelElement.term}`,
                        ),
                    );
                    if (filteredCourses.length) {
                        levels.push({
                            ...levelElement,
                            course: filteredCourses,
                        });
                    }
                    return levels;
                }, []);
                if (filteredLevels.length) {
                    result.push({
                        ...yearLevelElement,
                        level: filteredLevels,
                    });
                }
                return result;
            }, []);
        } else {
            filteredData = programYearLevelData;
        }
        logger.info(queryParsing, 'studentGroupMaster -> userAuthorProgramYearLevelList -> End');
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: filteredData,
        };
    } catch (error) {
        console.log(error);
        logger.error({ error }, 'studentGroupMaster -> userAuthorProgramYearLevelList -> Error');
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const courseDeliveryType = async ({ headers = {}, query = {} }) => {
    try {
        const { institutionCalendarId, programId, year, level, term, courseId } = query;
        const queryParsing = {
            institutionCalendarId,
            programId,
            year,
            level,
            term,
            courseId,
        };
        const courseIds = await getProgramCalenderCourseIds({
            institutionCalendarId,
            programId,
            year,
            level,
            term,
            courseId,
        });
        logger.info(queryParsing, 'studentGroupMaster -> courseDeliveryType -> Start');
        if (!courseIds.length) return { statusCode: 404, message: 'NO_DATA_FOUND' };
        const deliveryTypes = await getCourseBasedDelivery({ courseIds });
        logger.info(queryParsing, 'studentGroupMaster -> courseDeliveryType -> End');
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: deliveryTypes };
    } catch (error) {
        logger.error({ error }, 'studentGroupMaster -> courseDeliveryType -> Error');
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const selectedGroupSetting = async ({ body = {}, headers = {} }) => {
    try {
        const { _institution_id, _user_id } = headers;
        const {
            institutionCalendarId,
            programId,
            term,
            selectedType,
            year,
            level,
            courseIds,
            deliveryGroups,
            autoGenerate,
            upcomingYears,
            importedStudents,
            removedImportedGroupingIds,
        } = body;
        const queryParsing = {
            _institution_id,
            _user_id,
            institutionCalendarId,
            programId,
            term,
            selectedType,
            year,
            level,
            courseIds,
            deliveryGroups,
            autoGenerate,
            upcomingYears,
            importedStudents,
            removedImportedGroupingIds,
        };
        logger.info(queryParsing, 'studentGroupMaster -> selectedGroupSetting -> Start');
        let students = [];
        if (importedStudents?.length) {
            const studentGroupCheck = await checkStudentsAreImported({
                institutionCalendarId,
                programId,
                term,
                selectedType,
                year,
                level,
                importedStudents,
                courseIds,
                userId: _user_id,
            });
            if (studentGroupCheck?.studentDuplicateCheck?.length) {
                return {
                    statusCode: 404,
                    message: 'DATA_CHECK_FAILED',
                    data: studentGroupCheck?.studentDuplicateCheck || [],
                };
            }
            students = studentGroupCheck?.students;
        }
        let createDeliveryGroup;
        if (deliveryGroups?.length) {
            createDeliveryGroup = createDeliveryGroups({ deliveryGroups });
        }
        if (removedImportedGroupingIds?.length) {
            await removedImportedStudentsIds({ removedImportedGroupingIds });
        }
        const groupSettingData = await studentGroupSettingSchema.create({
            _institution_id: convertToMongoObjectId(_institution_id),
            institutionCalendarId: convertToMongoObjectId(institutionCalendarId),
            programId: convertToMongoObjectId(programId),
            term,
            selectedType,
            year,
            level,
            courseIds,
            ...(createDeliveryGroup && { deliveryGroups: createDeliveryGroup }),
            autoGenerate,
            upcomingYears,
            students,
        });
        if (!groupSettingData)
            return {
                statusCode: 404,
                message: 'NOT_SAVED',
            };
        logger.info(queryParsing, 'studentGroupMaster -> selectedGroupSetting -> End');
        return { statusCode: 200, message: 'SAVED_SUCCESSFULLY' };
    } catch (error) {
        logger.error({ error }, 'studentGroupMaster -> selectedGroupSetting -> Error');
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const editGroupSetting = async ({ body = {}, headers = {} }) => {
    try {
        const { _user_id } = headers;
        const {
            groupSettingId,
            selectedType,
            year,
            level,
            courseIds,
            deliveryGroups,
            autoGenerate,
            upcomingYears,
            groupStudents,
            isDeleted,
            importedStudents,
            removedImportedGroupingIds,
        } = body;
        const queryParsing = {
            _user_id,
            groupSettingId,
            selectedType,
            year,
            level,
            courseIds,
            deliveryGroups,
            autoGenerate,
            upcomingYears,
            groupStudents,
            isDeleted,
            importedStudents,
            removedImportedGroupingIds,
        };
        const studentGroupSettingData = await studentGroupSettingSchema
            .findOne(
                { _id: convertToMongoObjectId(groupSettingId) },
                { students: 1, deliveryGroups: 1, institutionCalendarId: 1, programId: 1, term: 1 },
            )
            .lean();
        if (!studentGroupSettingData) {
            return { statusCode: 404, message: 'NO_DATA_FOUND' };
        }
        const existingDeliveryGroup = studentGroupSettingData?.deliveryGroups || [];
        logger.info(queryParsing, 'studentGroupMaster -> editGroupSetting -> Start');
        let students = [];
        if (importedStudents?.length) {
            const studentGroupCheck = await checkStudentsAreImported({
                groupSettingId,
                institutionCalendarId: studentGroupSettingData.institutionCalendarId,
                programId: studentGroupSettingData.programId,
                term: studentGroupSettingData.term,
                selectedType,
                year,
                level,
                importedStudents,
                courseIds,
                userId: _user_id,
            });
            if (studentGroupCheck?.studentDuplicateCheck?.length) {
                return {
                    statusCode: 404,
                    message: 'DATA_CHECK_FAILED',
                    data: studentGroupCheck?.studentDuplicateCheck || [],
                };
            }
            students = studentGroupCheck?.students;
        }
        const checkedDeliveryGroups = [];
        if (deliveryGroups?.length) {
            deliveryGroups.forEach((deliveryGroupElement) => {
                const matchedExisting = existingDeliveryGroup.find(
                    (existingGroup) => existingGroup.gender === deliveryGroupElement.gender,
                );
                if (!matchedExisting) {
                    deliveryGroupElement?.deliveryTypes?.forEach((deliveryTypeElement) => {
                        deliveryTypeElement.selectedType?.forEach((selectedTypeElement) => {
                            const requiredGroups = parseInt(selectedTypeElement.noOfGroups) || 0;
                            const groups = [];
                            for (let i = 0; i < requiredGroups; i++) {
                                groups.push({
                                    groupNo: i + 1,
                                    studentIds: [],
                                    _id: convertToMongoObjectId(),
                                });
                            }
                            selectedTypeElement.groups = groups;
                        });
                    });
                    checkedDeliveryGroups.push(deliveryGroupElement);
                } else {
                    const newGroup = {
                        gender: deliveryGroupElement.gender,
                        _id: matchedExisting._id,
                        deliveryTypes: [],
                    };
                    deliveryGroupElement.deliveryTypes.forEach((deliveryTypeElement) => {
                        const matchedDeliveryType = matchedExisting.deliveryTypes.find(
                            (matchingDeliveryElement) =>
                                matchingDeliveryElement.typeName === deliveryTypeElement.typeName,
                        );

                        if (!matchedDeliveryType) {
                            newGroup.deliveryTypes.push(deliveryTypeElement);
                        } else {
                            const newDeliveryType = {
                                typeName: deliveryTypeElement.typeName,
                                _id: matchedDeliveryType._id,
                                selectedType: [],
                            };
                            deliveryTypeElement.selectedType.forEach((selectedType) => {
                                const matchedSelected = matchedDeliveryType.selectedType.find(
                                    (selectTypeElement) =>
                                        selectTypeElement.deliveryType ===
                                        selectedType.deliveryType,
                                );
                                if (!matchedSelected) {
                                    newDeliveryType.selectedType.push(selectedType);
                                } else {
                                    let existingGroups = selectedType?.isGrouped
                                        ? [...(matchedSelected.groups || [])]
                                        : [];
                                    const requiredGroups = parseInt(selectedType.noOfGroups) || 0;
                                    if (selectedType?.isGrouped) {
                                        for (
                                            let i = existingGroups.length;
                                            i < requiredGroups;
                                            i++
                                        ) {
                                            existingGroups.push({
                                                groupNo: i + 1,
                                                studentIds: [],
                                                _id: convertToMongoObjectId(),
                                            });
                                        }
                                    } else {
                                        existingGroups = [];
                                        for (let i = 0; i < requiredGroups; i++) {
                                            existingGroups.push({
                                                groupNo: i + 1,
                                                studentIds: [],
                                                _id: convertToMongoObjectId(),
                                            });
                                        }
                                    }
                                    newDeliveryType.selectedType.push({
                                        ...matchedSelected,
                                        noOfGroups: requiredGroups,
                                        groups: existingGroups,
                                    });
                                }
                            });
                            newGroup.deliveryTypes.push(newDeliveryType);
                        }
                    });
                    checkedDeliveryGroups.push(newGroup);
                }
            });
        }
        if (removedImportedGroupingIds?.length) {
            await removedImportedStudentsIds({ removedImportedGroupingIds });
        }
        const groupSettingData = await studentGroupSettingSchema.updateOne(
            {
                _id: convertToMongoObjectId(groupSettingId),
            },
            {
                ...(selectedType && { selectedType }),
                ...(year && { year }),
                ...(level && { level }),
                ...(courseIds && { courseIds }),
                ...(deliveryGroups?.length && { deliveryGroups: checkedDeliveryGroups }),
                ...(upcomingYears && { upcomingYears }),
                ...(typeof autoGenerate === 'boolean' && { autoGenerate }),
                ...(typeof groupStudents === 'boolean' && { groupStudents }),
                ...(typeof isDeleted === 'boolean' && { isDeleted }),
                ...(students?.length && {
                    $push: {
                        students: {
                            $each: students,
                        },
                    },
                }),
            },
        );
        logger.info(queryParsing, 'studentGroupMaster -> editGroupSetting -> End');
        if (groupSettingData.modifiedCount)
            return {
                statusCode: 200,
                message: 'SAVED_SUCCESSFULLY',
            };
        return { statusCode: 404, message: 'NOT_SAVED' };
    } catch (error) {
        logger.error({ error }, 'studentGroupMaster -> editGroupSetting -> Error');
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const selectedProgramList = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { institutionCalendarId, programId, term, year, selectedType, courseId, level } =
            query;

        const queryParsing = {
            _institution_id,
            institutionCalendarId,
            programId,
            term,
            year,
            selectedType,
            courseId,
            level,
        };
        logger.info(queryParsing, 'studentGroupMaster -> selectedProgramList -> Start');
        const groupSettingData = await studentGroupSettingSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    institutionCalendarId: convertToMongoObjectId(institutionCalendarId),
                    programId: convertToMongoObjectId(programId),
                    term: { $regex: term, $options: 'i' },
                    ...(year && { year: { $regex: year, $options: 'i' } }),
                    ...(selectedType && { selectedType }),
                    ...(level && { level }),
                    ...(courseId && { courseIds: convertToMongoObjectId(courseId) }),
                },
                {
                    selectedType: 1,
                    courseIds: 1,
                    autoGenerate: 1,
                    deliveryGroups: 1,
                    upcomingYears: 1,
                    groupStudents: 1,
                    year: 1,
                    level: 1,
                    isPublished: 1,
                },
            )
            .lean();
        logger.info(queryParsing, 'studentGroupMaster -> selectedProgramList -> End');
        if (!groupSettingData.length) return { statusCode: 404, message: 'NO_DATA_FOUND' };
        return groupingDeliveryData({ groupSettingData });
    } catch (error) {
        logger.error({ error }, 'studentGroupMaster -> selectedProgramList -> Error');
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const publishedStudentGrouping = async ({ body = {}, headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const { groupSettingId, institutionCalendarId } = body;
        const queryParsing = {
            groupSettingId,
            institutionCalendarId,
        };
        logger.info(queryParsing, 'studentGroupMaster -> publishedStudentGrouping -> Start');
        const groupSettingData = await studentGroupSettingSchema
            .findOne(
                {
                    _id: convertToMongoObjectId(groupSettingId),
                },
                {
                    term: 1,
                    programId: 1,
                    year: 1,
                    level: 1,
                    courseIds: 1,
                    deliveryGroups: 1,
                    groupStudents: 1,
                    students: 1,
                    isPublished: 1,
                },
            )
            .populate({ path: 'students.studentId', select: { name: 1, gender: 1 } })
            .populate({ path: 'students.importedId', select: { name: 1 } })
            .lean();
        const { term, programId, level, courseIds, year, deliveryGroups, students, isPublished } =
            groupSettingData;

        await studentGroupDashboardService({
            _institution_id,
            institutionCalendarId,
            programId,
        });

        return await updateOldStudentGroupData({
            _institution_id,
            term,
            programId,
            level,
            courseIds,
            institutionCalendarId,
            year,
            deliveryGroups,
            students,
            groupSettingId,
            isPublished,
        });
    } catch (error) {
        logger.error({ error }, 'studentGroupMaster -> publishedStudentGrouping -> Error');
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const importedStudentViewList = async ({ query = {} }) => {
    try {
        const { groupSettingId, genderType, searchKey, groupType, studentGender } = query;
        const queryParsing = {
            groupSettingId,
            genderType,
            searchKey,
            groupType,
            studentGender,
        };
        logger.info(queryParsing, 'studentGroupMaster -> importedStudentViewList -> Start');
        const studentGroupSettingData = await studentGroupSettingSchema
            .findOne(
                {
                    _id: convertToMongoObjectId(groupSettingId),
                },
                {
                    students: 1,
                    deliveryGroups: 1,
                },
            )
            .populate({ path: 'students.studentId', select: { name: 1, gender: 1 } })
            .populate({ path: 'students.importedId', select: { name: 1 } })
            .lean();
        logger.info(queryParsing, 'studentGroupMaster -> importedStudentViewList -> End');
        if (!studentGroupSettingData?.students?.length) {
            return { statusCode: 404, message: 'NO_DATA_FOUND' };
        }
        return groupWiseListStudent({
            students: studentGroupSettingData?.students,
            deliveryGroups: studentGroupSettingData?.deliveryGroups || [],
            searchKey,
            genderType,
            groupType,
            studentGender,
            query,
        });
    } catch (error) {
        logger.error({ error }, 'studentGroupMaster -> importedStudentViewList -> Error');
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getSingleStudentDetails = async ({ query = {} }) => {
    try {
        const { groupSettingId, academicId } = query;
        const queryParsing = {
            groupSettingId,
            academicId,
        };
        logger.info(queryParsing, 'studentGroupMaster -> getSingleStudentDetails -> Start');
        const studentRegister = await checkStudentRegister({
            studentAcademicIds: [new RegExp(`^${academicId}$`, 'i')],
        });
        if (!studentRegister?.length) {
            return { statusCode: 404, message: 'STUDENT_ACADEMIC_NO_NOT_FOUND' };
        }
        if (studentRegister[0]?.user_type === STAFF) {
            return { statusCode: 404, message: 'IT_IS_STAFF_NOT_TO_REGISTER' };
        }
        //student is already exist
        const existingInStudentGroup = await alreadyInStudentGroup({ groupSettingId, academicId });
        logger.info(queryParsing, 'studentGroupMaster -> getSingleStudentDetails -> End');
        if (existingInStudentGroup) {
            return { statusCode: 404, message: 'STUDENT_IS_ALREADY_EXIST_IN_THIS_GROUP' };
        }
        return { statusCode: 200, message: 'YOU_CAN_ADD_STUDENT', data: studentRegister[0] };
    } catch (error) {
        logger.error({ error }, 'studentGroupMaster -> getSingleStudentDetails -> Error');
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const addSingleStudent = async ({ headers = {}, body = {} }) => {
    try {
        const { _user_id } = headers;
        const { groupSettingId, studentId, academicId, mark } = body;
        const queryParsing = {
            _user_id,
            groupSettingId,
            studentId,
            academicId,
            mark,
        };
        logger.info(queryParsing, 'studentGroupMaster -> addSingleStudent -> Start');
        await studentGroupSettingSchema.updateOne(
            {
                _id: convertToMongoObjectId(groupSettingId),
            },
            {
                $push: {
                    students: [
                        {
                            studentId: convertToMongoObjectId(studentId),
                            academicId,
                            mark,
                            importedId: convertToMongoObjectId(_user_id),
                            importedDate: new Date(),
                        },
                    ],
                },
            },
        );
        logger.info(queryParsing, 'studentGroupMaster -> addSingleStudent -> END');
        return { statusCode: 200, message: 'STUDENT_ADDED_SUCCESSFULLY' };
    } catch (error) {
        logger.error({ error }, 'studentGroupMaster -> addSingleStudent -> Error');
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const autoGenerateGroupNames = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { institutionCalendarId, programId, term, year, level, courseIds } = query;
        return getAutoGenerateGroupName({
            _institution_id,
            institutionCalendarId,
            programId,
            term,
            year,
            level,
            courseIds,
        });
    } catch (error) {
        logger.error({ error }, 'studentGroupMaster -> addSingleStudent -> Error');
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const deleteStudents = async ({ body = {} }) => {
    try {
        const { groupSettingId, studentIds, removeCompleteCourse } = body;
        const queryParsing = {
            groupSettingId,
            studentIds,
            removeCompleteCourse,
        };
        logger.info(queryParsing, 'studentGroupMaster -> deleteStudents -> Start');
        const studentIdsNeedToRemove = studentIds.map((studentElement) =>
            convertToMongoObjectId(studentElement),
        );
        //need to check the student is grouped or ungrouped
        await studentGroupSettingSchema.updateOne(
            {
                _id: convertToMongoObjectId(groupSettingId),
            },
            {
                $pull: {
                    students: {
                        studentId: {
                            $in: studentIdsNeedToRemove,
                        },
                    },
                    'deliveryGroups.$[].deliveryTypes.$[].selectedType.$[].groups.$[].studentIds': {
                        $in: studentIdsNeedToRemove,
                    },
                },
            },
        );
        const settingData = await studentGroupSettingSchema
            .findOne(
                {
                    _id: convertToMongoObjectId(groupSettingId),
                },
                {
                    _institution_id: 1,
                    institutionCalendarId: 1,
                    programId: 1,
                    selectedType: 1,
                    term: 1,
                    year: 1,
                    level: 1,
                    courseIds: 1,
                },
            )
            .lean();

        const studentGroupDoc = await studentGroupSchema
            .findOne(
                {
                    ...queryConstant,
                    _institution_id: convertToMongoObjectId(settingData._institution_id),
                    _institution_calendar_id: convertToMongoObjectId(
                        settingData.institutionCalendarId,
                    ),
                    'master.year': settingData.year,
                    'master._program_id': convertToMongoObjectId(settingData.programId),
                },
                {
                    _id: 1,
                    'groups._id': 1,
                    'groups.term': 1,
                    'groups.level': 1,
                    'groups.courses._id': 1,
                    'groups.courses._course_id': 1,
                    'groups.courses.setting._id': 1,
                    'groups.courses.setting.session_setting._id': 1,
                    'groups.courses.setting.session_setting.groups._id': 1,
                    'groups.courses.setting.session_setting.groups._student_ids': 1,
                },
            )
            .lean();

        if (!studentGroupDoc) {
            logger.warn(
                { studentIdsNeedToRemove, settingData },
                'studentGroupMaster -> deleteStudents -> No student group document found',
            );
            return { statusCode: 404, message: 'STUDENT_GROUP_DOCUMENT_NOT_FOUND' };
        }

        const oldStudentGroupBulkWrite = [];

        studentGroupDoc.groups.forEach((group, groupIndex) => {
            if (group.term !== settingData.term) {
                return;
            }
            if (settingData.level && group.level !== settingData.level) {
                return;
            }
            group.courses.forEach((course, courseIndex) => {
                if (
                    !settingData.courseIds.some(
                        (courseId) => String(courseId) === String(course._course_id),
                    )
                ) {
                    return;
                }
                course.setting.forEach((setting, settingIndex) => {
                    setting.session_setting.forEach((sessionSetting, sessionIndex) => {
                        sessionSetting.groups.forEach((groupItem, groupItemIndex) => {
                            const hasStudentsToRemove = groupItem._student_ids.some((studentId) =>
                                studentIdsNeedToRemove.find(
                                    (studentIdNeedToRemove) =>
                                        String(studentIdNeedToRemove) === String(studentId),
                                ),
                            );

                            if (hasStudentsToRemove) {
                                oldStudentGroupBulkWrite.push({
                                    updateOne: {
                                        filter: {
                                            _id: studentGroupDoc._id,
                                        },
                                        update: {
                                            $pull: {
                                                [`groups.${groupIndex}.courses.${courseIndex}.setting.${settingIndex}.session_setting.${sessionIndex}.groups.${groupItemIndex}._student_ids`]:
                                                    {
                                                        $in: studentIdsNeedToRemove,
                                                    },
                                            },
                                        },
                                    },
                                });
                            }
                        });
                    });
                });
            });
        });
        if (oldStudentGroupBulkWrite.length) {
            logger.info(
                {
                    studentIdsNeedToRemove,
                    operationsCount: oldStudentGroupBulkWrite.length,
                    operations: oldStudentGroupBulkWrite.map((op) => op.updateOne.update.$pull),
                },
                'studentGroupMaster -> deleteStudents -> oldStudentGroupBulkWrite -> Before execution',
            );
            const oldStudentGroupBulkWriteResult = await studentGroupSchema.bulkWrite(
                oldStudentGroupBulkWrite,
            );
            logger.info(
                {
                    result: oldStudentGroupBulkWriteResult,
                    operationsCount: oldStudentGroupBulkWrite.length,
                },
                'studentGroupMaster -> deleteStudents -> oldStudentGroupBulkWrite -> After execution',
            );
        } else {
            logger.warn(
                { studentIdsNeedToRemove },
                'studentGroupMaster -> deleteStudents -> No operations to perform - student not found in groups',
            );
        }
        await studentRemoveFromCourseSchedule({
            _institution_id: settingData._institution_id,
            institutionCalendarId: settingData.institutionCalendarId,
            programId: settingData.programId,
            term: settingData.term,
            yearNo: settingData.year,
            levelNo: settingData.level,
            courseIds: settingData.courseIds,
            studentIds: studentIdsNeedToRemove,
            removeCompleteCourse,
        });
        logger.info(queryParsing, 'studentGroupMaster -> deleteStudents -> End');
        return { statusCode: 200, message: 'STUDENT_DELETED_SUCCESSFULLY' };
    } catch (error) {
        logger.error({ error }, 'studentGroupMaster -> deleteStudents -> Error');
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const groupingStudent = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const {
            groupSettingId,
            genderType,
            studentIds,
            deliveryTypes,
            groupingMethod,
            groupingType,
            selectedDeliveryType,
            isAllStudent,
            removedStudentIds,
            moveTo,
            studentAttendanceStatus,
        } = body;
        const queryParsing = {
            _institution_id,
            groupSettingId,
            genderType,
            studentIds,
            deliveryTypes,
            groupingMethod,
            groupingType,
            selectedDeliveryType,
            isAllStudent,
            removedStudentIds,
            moveTo,
            studentAttendanceStatus,
        };
        logger.info(queryParsing, 'studentGroupMaster -> groupingStudent -> Start');
        return updatedStudentGrouping({
            _institution_id,
            groupSettingId,
            genderType,
            studentIds,
            deliveryTypes,
            groupingMethod,
            groupingType,
            selectedDeliveryType,
            isAllStudent,
            removedStudentIds,
            moveTo,
            studentAttendanceStatus,
        });
    } catch (error) {
        logger.error({ error }, 'studentGroupMaster -> deleteStudents -> Error');
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const groupingCount = async ({ query = {} }) => {
    try {
        const { groupSettingId, genderType, searchKey, groupType } = query;
        const queryParsing = {
            groupSettingId,
            genderType,
            searchKey,
            groupType,
        };
        logger.info(queryParsing, 'studentGroupMaster -> groupingCount -> Start');
        const studentGroupSettingData = await studentGroupSettingSchema
            .findOne(
                {
                    _id: convertToMongoObjectId(groupSettingId),
                },
                {
                    students: 1,
                    deliveryGroups: 1,
                },
            )
            .populate({ path: 'students.studentId', select: { name: 1, gender: 1 } })
            .populate({ path: 'students.importedId', select: { name: 1 } })
            .lean();

        if (!studentGroupSettingData?.students?.length) {
            return { statusCode: 404, message: 'NO_DATA_FOUND' };
        }
        return groupWiseListStudent({
            students: studentGroupSettingData?.students,
            deliveryGroups: studentGroupSettingData?.deliveryGroups || [],
            searchKey,
            genderType,
            groupType,
            studentCount: true,
        });
    } catch (error) {
        logger.error({ error }, 'studentGroupMaster -> groupingCount -> Error');
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const totalCompletedSession = async ({ body = {}, headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const {
            institutionCalendarId,
            term,
            year,
            level,
            programId,
            courseIds,
            selectedDeliveryType,
            gender,
        } = body;

        const queryParsing = {
            _institution_id,
            institutionCalendarId,
            term,
            year,
            level,
            programId,
            courseIds,
            selectedDeliveryType,
            gender,
        };
        logger.info(queryParsing, 'studentGroupMaster -> totalCompletedSession -> Start');
        const totalCompletedScheduleCount = await getTotalSchedulingCount({
            _institution_id,
            institutionCalendarId,
            term,
            year,
            level,
            programId,
            courseIds,
            selectedDeliveryType,
            gender,
        });
        logger.info(queryParsing, 'studentGroupMaster -> totalCompletedSession -> End');
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: totalCompletedScheduleCount,
        };
    } catch (error) {
        logger.error({ error }, 'studentGroupMaster -> totalCompletedSession -> Error');
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const yearWiseImportedStudentList = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const {
            institutionCalendarId,
            programId,
            term,
            year,
            level,
            courseIds,
            studentGroupSettingId,
            userSelectedType,
            genderType,
            groupType,
            studentGender,
        } = query;

        const queryParsing = {
            _institution_id,
            institutionCalendarId,
            programId,
            term,
            year,
            level,
            courseIds,
            studentGroupSettingId,
            userSelectedType,
            genderType,
            groupType,
            studentGender,
        };
        logger.info(queryParsing, 'studentGroupMaster -> yearWiseImportedStudentList -> Start');
        return getYearWiseImportedStudentList({
            _institution_id,
            institutionCalendarId,
            programId,
            term,
            year,
            level,
            courseIds,
            studentGroupSettingId,
            userSelectedType,
            genderType,
            groupType,
            studentGender,
        });
    } catch (error) {
        logger.error({ error }, 'studentGroupMaster -> yearWiseImportedStudentList -> Error');
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const pushStudentInExistingSchedule = async ({ body = {}, headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const { deliveryGroupDetails, groupSettingId, studentIds, attendanceStatus } = body;
        const queryParsing = {
            _institution_id,
            groupSettingId,
            studentIds,
            deliveryGroupDetails,
            attendanceStatus,
        };
        logger.info(queryParsing, 'studentGroupMaster -> studentPushInExistingSchedule -> Start');
        const studentData = await studentPushInExistingSchedule({
            _institution_id,
            groupSettingId,
            studentIds,
            deliveryGroupDetails,
            attendanceStatus,
        });
        logger.info(queryParsing, 'studentGroupMaster -> studentPushInExistingSchedule -> End');
        return {
            statusCode: 200,
            message: 'STUDENTS_PUSHED_SUCCESSFULLY',
            data: studentData,
        };
    } catch (error) {
        logger.error({ error }, 'studentGroupMaster -> studentPushInExistingSchedule -> Error');
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = {
    userAuthorProgramList,
    userAuthorProgramYearLevelList,
    courseDeliveryType,
    selectedGroupSetting,
    editGroupSetting,
    selectedProgramList,
    publishedStudentGrouping,
    importedStudentViewList,
    addSingleStudent,
    getSingleStudentDetails,
    autoGenerateGroupNames,
    deleteStudents,
    groupingStudent,
    groupingCount,
    totalCompletedSession,
    yearWiseImportedStudentList,
    pushStudentInExistingSchedule,
};
