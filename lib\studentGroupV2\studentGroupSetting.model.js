const {
    Schema,
    model,
    Types: { ObjectId },
} = require('mongoose');
const {
    INSTITUTION_CALENDAR,
    DIGI_PROGRAM,
    INSTITUTION,
    STUDENT_GROUP_SETTING,
    USER,
    DIGI_COURSE,
} = require('../utility/constants');

const studentGroupSettingSchema = new Schema(
    {
        _institution_id: { type: ObjectId, ref: INSTITUTION },
        institutionCalendarId: {
            type: ObjectId,
            ref: INSTITUTION_CALENDAR,
        },
        programId: { type: ObjectId, ref: DIGI_PROGRAM },
        term: { type: String },
        selectedType: { type: String },
        year: { type: String },
        level: { type: String },
        courseIds: [{ type: ObjectId, ref: DIGI_COURSE }],
        deliveryGroups: [
            {
                gender: { type: String },
                deliveryTypes: [
                    {
                        typeName: { type: String },
                        selectedType: [
                            {
                                deliveryType: { type: String },
                                deliverySymbol: { type: String },
                                noOfGroups: { type: Number },
                                groupName: { type: String },
                                groups: [
                                    {
                                        groupNo: { type: Number },
                                        groupName: { type: String },
                                        studentIds: [
                                            {
                                                type: Schema.Types.ObjectId,
                                                ref: USER,
                                            },
                                        ],
                                    },
                                ],
                            },
                        ],
                    },
                ],
            },
        ],
        autoGenerate: { type: Boolean, default: false },
        upcomingYears: [{ type: String }],
        groupStudents: { type: Boolean, default: false },
        isActive: { type: Boolean, default: true },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        students: [
            {
                studentId: {
                    type: Schema.Types.ObjectId,
                    ref: USER,
                },
                academicId: { type: String },
                mark: { type: String },
                isGrouped: { type: Boolean, default: false },
                importedId: {
                    type: Schema.Types.ObjectId,
                    ref: USER,
                },
                importedDate: { type: Date },
            },
        ],
        isPublished: { type: Boolean, default: false },
    },
    {
        timestamps: true,
    },
);
module.exports = model(STUDENT_GROUP_SETTING, studentGroupSettingSchema);
