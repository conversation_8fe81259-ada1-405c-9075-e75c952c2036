const router = require('express').Router();
const { validate } = require('../../common/middlewares/validation');
const catchAsync = require('../../../lib/utility/catch-async');
const EvaluatorController = require('./evaluator.controller');
const {
    getEvaluatorAndApproverDashboardSchema,
    getListOfComponentsForDashboardSchema,
    getComponentChildrenSchema,
    getScheduleForApproverOrEvaluatorSchema,
    getStudentsForScheduleSchema,
    updateApproveOrRejectStatusSchema,
    updateEvaluatorSchema,
    getRubricsAndMarksForEvaluatorSchema,
    getInsightsWithRubricsAndMarksSchema,
} = require('./evaluator.validation');

const {
    userPolicyAuthentication,
    defaultPolicy,
} = require('../../../middleware/policy.middleware');
const { getStudentResponseSchema } = require('../student-response/student-response.validation');

router.get(
    '/dashboard',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate(getEvaluatorAndApproverDashboardSchema),
    catchAsync(EvaluatorController.getEvaluatorAndApproverDashboard),
);

router.get(
    '/component/list',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate(getListOfComponentsForDashboardSchema),
    catchAsync(EvaluatorController.getListOfComponentsForDashboard),
);

router.get(
    '/component/count',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate(getListOfComponentsForDashboardSchema),
    catchAsync(EvaluatorController.getApproverOrEvaluatorComponentsWithCount),
);

router.get(
    '/component/children',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate(getComponentChildrenSchema),
    catchAsync(EvaluatorController.getComponentChildren),
);

router.get(
    '/overall-count',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate(getListOfComponentsForDashboardSchema),
    catchAsync(EvaluatorController.getApproverOrEvaluatorOverallCount),
);

router.get(
    '/schedule',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate(getScheduleForApproverOrEvaluatorSchema),
    catchAsync(EvaluatorController.getScheduleForApproverOrEvaluator),
);

router.get(
    '/schedule/student',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate(getStudentsForScheduleSchema),
    catchAsync(EvaluatorController.getStudentsForSchedule),
);

router.put(
    '/approve-or-reject',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate(updateApproveOrRejectStatusSchema),
    catchAsync(EvaluatorController.updateApproveOrRejectStatus),
);

router.put(
    '/update-evaluator',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate(updateEvaluatorSchema),
    catchAsync(EvaluatorController.updateStudentMarksAndRubrics),
);

router.get(
    '/mark',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate(getRubricsAndMarksForEvaluatorSchema),
    catchAsync(EvaluatorController.getRubricsAndMarksForEvaluator),
);

router.get(
    '/insights',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate(getInsightsWithRubricsAndMarksSchema),
    catchAsync(EvaluatorController.getInsightsWithRubricsAndMarks),
);

router.get(
    '/schedule/student/form',
    validate(getStudentResponseSchema),
    catchAsync(EvaluatorController.getStudentForm),
);

module.exports = router;
