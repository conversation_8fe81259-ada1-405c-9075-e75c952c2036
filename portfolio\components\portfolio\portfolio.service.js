const PortfolioModel = require('./portfolio.model');
const FormModel = require('../form/form.model');
const StudentPortfolioModel = require('./student-portfolio.model');
const UserModel = require('../../../lib/models/user');
const StudentResponseModel = require('../student-response/student-response.model');
const CourseScheduleModel = require('../../../lib/models/course_schedule');
const EvaluationMappingModel = require('../evaluation/evaluation-mapping.model');

const BaseHelper = require('../../base/base.helper');

const {
    ON_GOING,
    PUBLISHED,
    NOT_STARTED,
    APPROVED,
    EVALUATED,
} = require('../../common/utils/enums');
const {
    convertToMongoObjectId,
    isIDEquals,
    deepClone,
    paginate,
    searchInArray,
} = require('../../common/utils/common.util');
const { getDocumentsCount } = require('../../base/base.helper');
const {
    NotFoundError,
    BadRequestError,
    UpdateFailedError,
} = require('../../common/utils/api_error_util');
const {
    getStudentListFromStudentGroup,
} = require('../../../lib/digi_class/course_session/course_session_service');
const { getGrades } = require('../../../lib/utility/digiAssess.helper');
const { standardGrades } = require('../report/report.helper');
const { getAssignedUserSections } = require('../form/form.helper');
const { STUDENT, REGULAR } = require('../../common/utils/constants');
const { getCourseDetails } = require('../course/course.service');
const {
    buildPortfolioCourseFilters,
    getCountFromStudentResponse,
    getTotalMarksForChild,
} = require('./portfolio.helper');
const { filterByConditions } = require('../../common/utils/array.util');
const { calculateRubricPoint } = require('../rubric/rubric.helper');

const formProject = {
    title: 1,
    pages: 1,
    type: 1,
    publishedDate: 1,
    createdBy: 1,
    status: 1,
    isTemplate: 1,
};

const getPortfolio = async ({
    programId,
    courseId,
    institutionCalendarId,
    term,
    year,
    level,
    curriculumId,
    curriculumName,
    rotation,
    rotationCount,
}) => {
    const portfolio = await PortfolioModel.findOne(
        {
            programId,
            courseId,
            institutionCalendarId,
            term,
            year,
            level,
            ...(curriculumId && { curriculumId }),
            ...(rotation && { rotation }),
            ...(rotationCount && { rotationCount }),
        },
        {
            components: 1,
            courseId: 1,
            programId: 1,
            totalMarks: 1,
            status: 1,
            publishedDate: 1,
            institutionCalendarId: 1,
            name: 1,
            description: 1,
        },
    ).lean();
    if (portfolio) {
        const evaluations = await EvaluationMappingModel.find(
            { portfolioId: portfolio._id },
            { componentId: 1, childrenId: 1, typeOfEvaluation: 1 },
        ).lean();

        portfolio.components.forEach((component) => {
            component.children.forEach((child) => {
                const evaluation = evaluations.find(
                    (evaluation) =>
                        isIDEquals(evaluation.componentId, component._id) &&
                        isIDEquals(evaluation.childrenId, child._id),
                );

                child.isAssigned = !!evaluation;
            });
        });

        return portfolio;
    }

    const newPortfolio = await PortfolioModel.create({
        courseId,
        programId,
        institutionCalendarId,
        status: NOT_STARTED,
        term,
        year,
        level,
        curriculumId,
        curriculumName,
        ...(rotation && { rotation }),
        ...(rotationCount && { rotationCount }),
    });

    return { _id: newPortfolio._id, courseId, programId, institutionCalendarId, totalMarks: 0 };
};

const updatePortfolio = async ({ portfolioId, name, description, components, totalMarks }) => {
    const isPublished = await PortfolioModel.exists({
        _id: portfolioId,
        status: PUBLISHED,
    });

    if (isPublished) {
        return { portfolio: null, message: 'PORTFOLIO_ALREADY_PUBLISHED' };
    }

    const portfolio = await PortfolioModel.findByIdAndUpdate(
        { _id: portfolioId },
        {
            ...(name && { name }),
            ...(description && { description }),
            components,
            totalMarks,
            status: ON_GOING,
        },
        {
            new: true,
            project: {
                components: 1,
                courseId: 1,
                programId: 1,
                academicCalenderId: 1,
                totalMarks: 1,
                name: 1,
                description: 1,
            },
        },
    ).lean();

    if (portfolio) {
        const evaluations = await EvaluationMappingModel.find(
            { portfolioId: portfolio._id },
            { componentId: 1, childrenId: 1, typeOfEvaluation: 1 },
        ).lean();

        portfolio.components.forEach((component) => {
            component.children.forEach((child) => {
                const evaluation = evaluations.find(
                    (evaluation) =>
                        isIDEquals(evaluation.componentId, component._id) &&
                        isIDEquals(evaluation.childrenId, child._id),
                );

                child.isAssigned = !!evaluation;
            });
        });

        return { portfolio, message: 'PORTFOLIO_UPDATED_SUCCESSFULLY' };
    }

    return { portfolio, message: 'PORTFOLIO_UPDATED_SUCCESSFULLY' };
};

const publishPortfolio = async ({ portfolioId }) => {
    const portfolio = await PortfolioModel.findOne(
        { _id: portfolioId },
        {
            courseId: 1,
            programId: 1,
            institutionCalendarId: 1,
            components: 1,
            status: 1,
            totalMarks: 1,
            publishedDate: 1,
            term: 1,
            year: 1,
            level: 1,
            curriculumId: 1,
            rotation: 1,
            rotationCount: 1,
        },
    ).lean();

    if (!portfolio) throw new NotFoundError('PORTFOLIO_NOT_FOUND');
    if (portfolio.status === PUBLISHED) throw new BadRequestError('PORTFOLIO_ALREADY_PUBLISHED');
    if (!portfolio?.components?.length) throw new BadRequestError('PORTFOLIO_HAS_NO_COMPONENTS');

    const isRolesUnAssigned = portfolio.components.some((component) =>
        component.children.some((child) => !child.roles?.length),
    );
    if (isRolesUnAssigned) throw new BadRequestError('ROLES_NOT_ASSIGNED_TO_COMPONENT');

    const grades = await getGrades();
    const grade = grades.find((g) => g.isActive);

    const { sgStudentList: students = [] } = await getStudentListFromStudentGroup({
        programId: portfolio.programId,
        year: portfolio.year,
        level: portfolio.level,
        ...(portfolio.rotation && portfolio.rotation === 'yes' && { rotation: portfolio.rotation }),
        ...(portfolio.rotationCount && { rotationCount: portfolio.rotationCount }),
        term: portfolio.term,
        courseId: portfolio.courseId,
        institutionCalendarId: portfolio.institutionCalendarId,
    });

    // Insert students
    const insertedStudents = await StudentPortfolioModel.insertMany(
        students.map((student) => ({
            portfolioId: portfolio._id,
            student: {
                _id: student._student_id,
                name: student.name,
                academicNo: student.user_id,
                gender: student.gender,
            },
            components: portfolio.components,
            totalMarks: portfolio.totalMarks,
            status: PUBLISHED,
            publishedDate: portfolio.publishedDate,
            programId: portfolio.programId,
            courseId: portfolio.courseId,
            institutionCalendarId: portfolio.institutionCalendarId,
            grades: grade?.grades || standardGrades,
            year: portfolio.year,
            term: portfolio.term,
            level: portfolio.level,
            ...(portfolio.rotation && { rotation: portfolio.rotation }),
            ...(portfolio.rotationCount && { rotationCount: portfolio.rotationCount }),
        })),
    );

    // Update portfolio status
    const updateResult = await PortfolioModel.updateOne(
        { _id: portfolioId },
        {
            $set: {
                status: PUBLISHED,
                publishedDate: new Date(),
                grades: grade?.grades || standardGrades,
            },
        },
    );
    if (!updateResult.modifiedCount) throw new UpdateFailedError('UPDATE_FAILED');

    // Get course schedules
    const courseSchedules = await CourseScheduleModel.find(
        {
            _institution_calendar_id: portfolio.institutionCalendarId,
            _program_id: portfolio.programId,
            _course_id: portfolio.courseId,
            year_no: portfolio.year,
            term: portfolio.term,
            level_no: portfolio.level,
            ...(portfolio.rotation &&
                portfolio.rotation !== 'no' && { rotation_no: portfolio.rotation }),
            ...(portfolio.rotationCount && { rotation_count: portfolio.rotationCount }),
            isDeleted: false,
            isActive: true,
            type: REGULAR,
        },
        {
            scheduleDate: '$schedule_date',
            session: 1,
            start: 1,
            end: 1,
        },
    ).lean();

    // Preload forms
    const formIds = portfolio.components.flatMap((c) =>
        c.children
            .filter((child) => child.formId)
            .map((child) => convertToMongoObjectId(child.formId)),
    );

    const forms = await FormModel.find(
        { _id: { $in: formIds } },
        {
            pages: 1,
            status: 1,
            marks: 1,
            title: 1,
            type: 1,
            evaluations: 1,
        },
    ).lean();

    const formMap = new Map(forms.map((form) => [form._id.toString(), form]));

    // Precompute schedule mapping for logbook components
    const scheduleMap = {};
    portfolio.components.forEach((component) => {
        if (component.isLogBook) {
            scheduleMap[component._id] = courseSchedules
                .map((schedule) => {
                    const match = component.deliveryTypes?.find(
                        (d) => d?.deliveryTypeSymbol === schedule?.session?.delivery_symbol,
                    );
                    return match ? { ...schedule, deliveryType: match } : null;
                })
                .filter(Boolean);
        }
    });

    // Create filtered sections per child
    const sectionMap = {};
    portfolio.components.forEach((component) => {
        component.children.forEach((child) => {
            const form = formMap.get(child.formId?.toString());
            if (form && child.roles?.length) {
                const role = child.roles.find((r) => r.type === STUDENT);
                const pages = getAssignedUserSections({
                    pages: deepClone(form.pages),
                    viewSections: role?.viewSections || [],
                    modifySections: role?.modifySections || [],
                });

                sectionMap[child._id] = {
                    ...form,
                    pages,
                    totalMarks: getTotalMarksForChild({ child }),
                };
            }
        });
    });

    // Prepare bulk student response creation
    const bulkOps = [];

    insertedStudents.forEach((studentDoc) => {
        const student = studentDoc.toObject();
        student.components.forEach((component) => {
            const isLogBook = component.isLogBook;
            const targetSchedules = isLogBook ? scheduleMap[component._id] || [] : [null];

            targetSchedules.forEach((schedule) => {
                component.children.forEach((child) => {
                    const form = sectionMap[child._id];
                    if (!form) return;

                    const base = {
                        status: NOT_STARTED,
                        pages: form.pages,
                        student: student.student,
                        totalMarks: form.totalMarks,
                        portfolioId: student._id,
                        parentPortfolioId: portfolio._id,
                        componentId: component._id,
                        childrenId: child._id,
                        formId: form._id,
                        title: form.title,
                        type: form.type,
                        roles: child.roles,
                    };

                    const filter = {
                        'student._id': convertToMongoObjectId(student.student._id),
                        componentId: convertToMongoObjectId(component._id),
                        childrenId: convertToMongoObjectId(child._id),
                    };

                    if (isLogBook) {
                        filter.scheduleId = convertToMongoObjectId(schedule._id);
                        base.scheduleId = schedule._id;
                        base.deliveryType = schedule.deliveryType;
                    }

                    bulkOps.push({
                        updateOne: {
                            filter,
                            update: { $set: base },
                            upsert: true,
                        },
                    });
                });
            });
        });
    });

    await StudentResponseModel.bulkWrite(bulkOps).catch(() => {
        throw new BadRequestError('UPDATE_FAILED');
    });

    return students;
};

const getComponentForm = async ({ formId, portfolioId, componentId, childrenId }) => {
    const portfolio = await PortfolioModel.findOne(
        { _id: portfolioId },
        {
            'components._id': 1,
            'components.children._id': 1,
            'components.children.formId': 1,
            'components.children.templateId': 1,
        },
    ).lean();

    if (!portfolio) throw new NotFoundError('PORTFOLIO_NOT_FOUND');

    // Check if the form is attached to the given component & child
    const component = portfolio.components.find((component) =>
        isIDEquals(component._id, componentId),
    );
    const child = component?.children.find((child) => isIDEquals(child._id, childrenId));
    const isAttachedForm = child && isIDEquals(child?.formId, formId);

    const form = await FormModel.findOne({ _id: formId }, { ...formProject, _id: 0 }).lean();
    if (!form) throw new NotFoundError('FORM_NOT_FOUND');

    if (!isAttachedForm || !form?.isTemplate) {
        return {
            ...form,
            isEditable: isAttachedForm,
            formId: form?.isTemplate ? form._id : child?.templateId,
        };
    }

    // Create and attach new form if isAttachedForm is true and componentForm not found
    const newForm = await FormModel.create({
        ...form,
        isTemplate: false,
    });

    if (newForm?._id) {
        await PortfolioModel.updateOne(
            { _id: portfolioId },
            {
                $set: {
                    'components.$[componentId].children.$[childrenId].formId': newForm._id,
                    'components.$[componentId].children.$[childrenId].templateId': formId,
                },
            },
            {
                arrayFilters: [
                    { 'componentId._id': convertToMongoObjectId(componentId) },
                    { 'childrenId._id': convertToMongoObjectId(childrenId) },
                ],
            },
        );
    }

    return { _id: newForm._id, ...form, isTemplate: false, isEditable: true };
};

const getPortfolioDashboard = async ({
    institutionCalendarId,
    type,
    programId,
    limit,
    pageNo,
    skip,
    search,
}) => {
    let allCourseDetails = await getCourseDetails({ institutionCalendarId, programId });

    if (search) allCourseDetails = searchInArray({ search, array: allCourseDetails });

    const paginatedCourseDetails = !type
        ? paginate(allCourseDetails, limit, pageNo)
        : allCourseDetails;

    const defaultPortfolio = {
        components: [],
        status: NOT_STARTED,
        publishedDate: null,
        totalMarks: 0,
        isReportGenerated: false,
        createdBy: null,
    };
    const orFilters = paginatedCourseDetails.map(
        ({ courseId, level, term, year, rotation, curriculumId, rotationCount }) => ({
            institutionCalendarId: convertToMongoObjectId(institutionCalendarId),
            programId: convertToMongoObjectId(programId),
            courseId: convertToMongoObjectId(courseId),
            level,
            term,
            year,
            rotation,
            curriculumId: convertToMongoObjectId(curriculumId),
            ...(rotationCount && { rotationCount }),
            ...(type && type !== NOT_STARTED && { status: type }),
        }),
    );

    if (type === NOT_STARTED) {
        const { data: startedPortfolios = [] } = await BaseHelper.getDocuments({
            Model: PortfolioModel,
            query: { $or: orFilters },
            project: {
                _id: 0,
                courseId: 1,
                curriculumId: 1,
                level: 1,
                term: 1,
                year: 1,
                rotation: 1,
                rotationCount: 1,
            },
            canThrowError: false,
        });

        allCourseDetails = allCourseDetails.filter((courseDetail) => {
            const alreadyStarted = startedPortfolios.some(
                (p) =>
                    isIDEquals(p.courseId, courseDetail.courseId) &&
                    isIDEquals(p.curriculumId, courseDetail.curriculumId) &&
                    p.level === courseDetail.level &&
                    p.term === courseDetail.term &&
                    p.year === courseDetail.year &&
                    p.rotation === courseDetail.rotation &&
                    (!courseDetail.rotationCount || p.rotationCount === courseDetail.rotationCount),
            );

            return !alreadyStarted;
        });

        return {
            count: {
                totalPages: Math.ceil(allCourseDetails.length / limit),
                totalCount: allCourseDetails.length,
                currentPage: pageNo,
            },
            portfolios: paginate(allCourseDetails, limit, pageNo).map((course) => ({
                ...course,
                ...defaultPortfolio,
                institutionCalendarId,
                programId,
            })),
        };
    }

    const projection = {
        courseId: 1,
        'components.name': 1,
        status: 1,
        publishedDate: 1,
        totalMarks: 1,
        isReportGenerated: 1,
        createdBy: 1,
        term: 1,
        level: 1,
        curriculumId: 1,
        year: 1,
        rotation: 1,
        rotationCount: 1,
    };

    if (!type) {
        const portfolios = await PortfolioModel.find({ $or: orFilters }, projection).lean();

        const formattedPortfolios = paginatedCourseDetails.map((course) => {
            const portfolio = portfolios.find(
                (p) =>
                    isIDEquals(p.courseId, course.courseId) &&
                    p.level === course.level &&
                    p.term === course.term &&
                    p.year === course.year &&
                    p.rotation === course.rotation &&
                    (!course.rotationCount || p.rotationCount === course.rotationCount),
            );
            return {
                ...course,
                ...(portfolio || defaultPortfolio),
                institutionCalendarId,
                programId,
            };
        });

        return {
            count: {
                totalPages: Math.ceil(allCourseDetails.length / limit),
                totalCount: allCourseDetails.length,
                currentPage: pageNo,
            },
            portfolios: formattedPortfolios,
        };
    }

    const [portfolios, totalCount] = await Promise.all([
        PortfolioModel.find({ $or: orFilters }, projection).limit(limit).skip(skip).lean(),
        getDocumentsCount({ Model: PortfolioModel, query: { $or: orFilters } }),
    ]);

    const formattedPortfolios = portfolios.map((portfolio) => {
        const course = allCourseDetails.find(
            (c) =>
                isIDEquals(portfolio.courseId, c.courseId) &&
                portfolio.level === c.level &&
                portfolio.term === c.term &&
                portfolio.year === c.year &&
                portfolio.rotation === c.rotation &&
                (!c.rotationCount || portfolio.rotationCount === c.rotationCount),
        );
        return { ...course, ...portfolio, institutionCalendarId, programId };
    });

    return {
        count: {
            totalPages: Math.ceil(totalCount / limit),
            totalCount,
            currentPage: pageNo,
        },
        portfolios: formattedPortfolios,
    };
};

const detachPortfolioFrom = async ({ portfolioId, componentId, childrenId }) => {
    if (portfolioId && componentId && childrenId) {
        const portfolio = await PortfolioModel.findOneAndUpdate(
            { _id: portfolioId },
            {
                $unset: {
                    'components.$[componentId].children.$[childrenId].formId': '',
                    'components.$[componentId].children.$[childrenId].templateId': '',
                },
            },
            {
                arrayFilters: [
                    { 'componentId._id': convertToMongoObjectId(componentId) },
                    { 'childrenId._id': convertToMongoObjectId(childrenId) },
                ],
            },
            {
                project: {
                    'components._id': 1,
                    'components.children._id': 1,
                    'components.children.formId': 1,
                    'components.children.templateId': 1,
                },
            },
        ).lean();

        // Check if the form is attached to the given component & child
        const component = portfolio.components.find((component) =>
            isIDEquals(component._id, componentId),
        );
        const child = component?.children.find((child) => isIDEquals(child._id, childrenId));
        if (child?.formId) {
            await FormModel.deleteOne({ _id: child?.formId, isTemplate: false });
        }
    } else {
        const portfolio = await PortfolioModel.findOne(
            { _id: portfolioId },
            {
                'components._id': 1,
                'components.children._id': 1,
                'components.children.formId': 1,
            },
        );
        const formIds = [];
        portfolio.components.forEach((component) => {
            if (componentId && isIDEquals(component._id, componentId)) {
                component.children.forEach((child) => {
                    if (childrenId) {
                        if (isIDEquals(child._id, childrenId)) {
                            if (child?.formId) {
                                formIds.push(child.formId);
                            }
                        }
                    }
                });
            } else if (!componentId) {
                component.children.forEach((child) => {
                    if (child?.formId) {
                        formIds.push(child.formId);
                    }
                });
            }
        });

        await FormModel.deleteMany({ _id: { $in: formIds }, isTemplate: false });
    }
};

const deletePortfolio = async ({ portfolioId }) => {
    const portfolio = await PortfolioModel.deleteOne({ _id: portfolioId });
    if (!portfolio.deletedCount) {
        throw new BadRequestError('DELETE_FAILED');
    }

    await StudentPortfolioModel.deleteMany({ portfolioId });

    await StudentResponseModel.deleteMany({ parentPortfolioId: portfolioId });

    await EvaluationMappingModel.deleteMany({ portfolioId });
};

const assignStudentToPortfolio = async ({
    programId,
    courseId,
    institutionCalendarId,
    portfolioId,
    componentId,
    childrenId,
    students,
    startDate,
    endDate,
    isAssigned = true,
}) => {
    const filter = {
        programId,
        courseId,
        institutionCalendarId,
        portfolioId,
        'student._id': { $in: students.map(({ studentId }) => convertToMongoObjectId(studentId)) },
    };

    const update = isAssigned
        ? {
              $set: {
                  'components.$[componentId].children.$[childrenId].isAssigned': true,
                  ...(startDate && {
                      'components.$[componentId].children.$[childrenId].startDate': startDate,
                  }),
                  ...(endDate && {
                      'components.$[componentId].children.$[childrenId].endDate': endDate,
                  }),
              },
          }
        : {
              $set: { 'components.$[componentId].children.$[childrenId].isAssigned': false },
              $unset: {
                  'components.$[componentId].children.$[childrenId].startDate': '',
                  'components.$[componentId].children.$[childrenId].endDate': '',
              },
          };

    await StudentPortfolioModel.updateMany(filter, update, {
        arrayFilters: [
            { 'componentId._id': convertToMongoObjectId(componentId) },
            { 'childrenId._id': convertToMongoObjectId(childrenId) },
        ],
    });
};

const getStudentsByCourse = async ({
    programId = '',
    year = '',
    level = '',
    rotation = '',
    rotationCount = '',
    term = '',
    courseId = '',
    institutionCalendarId = '',
    portfolioId = '',
    componentId = '',
    childrenId = '',
}) => {
    const studentGroup = await getStudentListFromStudentGroup({
        programId,
        year,
        level,
        rotation,
        rotationCount,
        term,
        courseId,
        institutionCalendarId,
    });

    const { masterGroup: groups = [], sgStudentList: students = [] } = studentGroup;

    if (!students.length) {
        return {
            students: [],
            count: { total: 0, male: 0, female: 0 },
            groups: groups.map((g) => g.group_name),
            deliveryTypes: groups.map((g) => g.delivery_type),
        };
    }

    const studentIds = students.map((student) => convertToMongoObjectId(student._student_id));
    const activeStudents = await UserModel.find(
        { _id: { $in: studentIds }, isActive: true },
        { _id: 1, email: 1, name: 1, academicNo: '$user_id', gender: 1 },
    ).lean();

    const formattedStudents = activeStudents.map(({ _id, name, academicNo, email, gender }) => ({
        name,
        studentId: _id,
        academicNo,
        email,
        gender: gender === 'male' ? 'M' : 'F',
    }));

    const studentsPortfolio = await StudentPortfolioModel.find(
        {
            programId,
            courseId,
            institutionCalendarId,
        },
        {
            'student._id': 1,
            'components._id': 1,
            'components.children._id': 1,
            'components.children.isAssigned': 1,
            'components.children.startDate': 1,
            'components.children.endDate': 1,
        },
    ).lean();

    const studentResponses = await StudentResponseModel.find(
        {
            'student._id': {
                $in: formattedStudents.map((student) => convertToMongoObjectId(student.studentId)),
            },
            componentId,
            childrenId,
        },
        { 'student._id': 1, status: 1, evaluationStatus: 1, awardedMarks: 1 },
    ).lean();

    formattedStudents.forEach((student) => {
        const response = studentResponses.find((response) =>
            isIDEquals(response?.student?._id, student.studentId),
        );
        const studentPortfolio = studentsPortfolio.find((s) =>
            isIDEquals(s.student._id, student.studentId),
        );
        const matchingComponent = studentPortfolio?.components?.find((c) =>
            isIDEquals(c._id, componentId),
        );
        const matchingChild = matchingComponent?.children?.find((ch) =>
            isIDEquals(ch._id, childrenId),
        );

        Object.assign(student, {
            status: response?.status || NOT_STARTED,
            evaluationStatus: response?.evaluationStatus || NOT_STARTED,
            awardedMarks: response?.awardedMarks || 0,
            isAssigned: matchingChild?.isAssigned,
            startDate: matchingChild?.startDate,
            endDate: matchingChild?.endDate,
            totalMarks: response?.totalMarks,
        });
    });

    const count = formattedStudents.reduce(
        (acc, { gender }) => {
            acc.total++;
            if (gender === 'M') acc.male++;
            if (gender === 'F') acc.female++;
            return acc;
        },
        { total: 0, male: 0, female: 0 },
    );

    const groupData = groups.map(
        ({ group_name, delivery_type, delivery_symbol, session_group, _id }) => ({
            groupName: group_name,
            deliveryType: delivery_type,
            deliverySymbol: delivery_symbol,
            sessionGroups: session_group.map((s) => ({
                _id: s._id,
                studentIds: s._student_ids,
                groupName: s.group_name,
            })),
            _id,
        }),
    );

    return { students: formattedStudents, count, groups: groupData };
};

const updateStudentReview = async ({
    componentId,
    childrenId,
    scheduleId,
    studentId,
    userId,
    text,
}) => {
    const user = await UserModel.findOne({ _id: userId }, { name: 1 }).lean();

    await StudentResponseModel.updateOne(
        { 'student._id': studentId, componentId, childrenId, ...(scheduleId && { scheduleId }) },
        { $push: { reviews: { userId, text, createdAt: new Date(), name: user?.name } } },
    );
};

const getPortfolioForAssignEvaluator = async ({ programId, courseId, institutionCalendarId }) => {
    const portfolio = await PortfolioModel.findOne(
        { programId, courseId, institutionCalendarId, status: PUBLISHED },
        { components: 1 },
    ).lean();

    const { components = [] } = portfolio || {};

    if (!portfolio || !components?.length) {
        return [];
    }

    const assignedEvaluators = await EvaluationMappingModel.find(
        { portfolioId: portfolio._id },
        { evaluators: 1, role: 1, childrenId: 1, componentId: 1 },
    ).lean();

    const formattedComponents = components.map((component) => {
        const children = component.children.map((child) => {
            const evaluators = assignedEvaluators.filter(
                (evaluator) =>
                    isIDEquals(evaluator.childrenId, child._id) &&
                    isIDEquals(evaluator.componentId, component._id),
            );

            const roles = evaluators
                .map((evaluator) => {
                    if (evaluator?.role?._id) {
                        return {
                            _id: evaluator.role._id,
                            name: evaluator.role.name,
                            evaluators: evaluator.evaluators,
                        };
                    }

                    return null;
                })
                .filter(Boolean);

            return {
                ...child,
                roles,
            };
        });

        return {
            _id: component._id,
            name: component.name,
            code: component.code,
            children,
        };
    });

    return { _id: portfolio._id, components: formattedComponents };
};

const updatePortfolioChildField = async ({ portfolioId, componentId, childrenId, update }) => {
    await PortfolioModel.updateOne(
        { _id: portfolioId, status: { $ne: PUBLISHED } },
        { $set: update },
        {
            arrayFilters: [
                { 'componentId._id': convertToMongoObjectId(componentId) },
                { 'childrenId._id': convertToMongoObjectId(childrenId) },
            ],
        },
    );
};

const updateFormInPortfolio = async ({ title, type, pages, userId }) => {
    const user = await UserModel.findOne({ _id: userId }, { name: 1, _id: 0 }).lean();

    const newForm = await FormModel.create({
        title,
        type,
        pages,
        createdBy: { id: userId, name: user?.name },
        isTemplate: true,
        status: PUBLISHED,
    });

    return newForm._id;
};

const getCountForPortfolioDashboard = async ({ institutionCalendarId, programId }) => {
    const funcParam = {
        courseDetails: await getCourseDetails({ institutionCalendarId, programId }),
        institutionCalendarId,
        programId,
    };

    const [published, onGoing] = await Promise.all([
        getDocumentsCount({
            Model: PortfolioModel,
            query: {
                $or: buildPortfolioCourseFilters({ ...funcParam, status: PUBLISHED }),
                isDeleted: false,
            },
        }),
        getDocumentsCount({
            Model: PortfolioModel,
            query: {
                $or: buildPortfolioCourseFilters({ ...funcParam, status: ON_GOING }),
                isDeleted: false,
            },
        }),
    ]);

    return {
        published,
        onGoing,
        notStarted: funcParam.courseDetails.length - (published + onGoing),
        courseCount: funcParam.courseDetails.length,
    };
};

const getInsights = async ({ institutionCalendarId, programId }) => {
    const funcParam = {
        institutionCalendarId,
        programId,
        courseDetails: await getCourseDetails({ institutionCalendarId, programId }),
    };

    const { data: portfolios = [] } = await BaseHelper.getDocuments({
        Model: PortfolioModel,
        query: { $or: buildPortfolioCourseFilters(funcParam), isDeleted: false },
        project: { _id: 1, status: 1 },
        canThrowError: false,
    });
    const approvedAndEvaluatedStatus = await getCountFromStudentResponse({
        matchQuery: { parentPortfolioId: { $in: portfolios.map(({ _id }) => _id) } },
    });

    return {
        all: portfolios.length,
        published: filterByConditions({
            array: portfolios,
            conditions: [['status', PUBLISHED]],
            canReturnCount: true,
        }),
        ongoing: filterByConditions({
            array: portfolios,
            conditions: [['status', ON_GOING]],
            canReturnCount: true,
        }),
        approved: filterByConditions({
            array: approvedAndEvaluatedStatus.filter(({ peerReviewTotal }) => peerReviewTotal),
            conditions: [['approved', true]],
            canReturnCount: true,
        }),
        evaluated: filterByConditions({
            array: approvedAndEvaluatedStatus,
            conditions: [['evaluated', true]],
            canReturnCount: true,
        }),
        courseCount: funcParam.courseDetails.length,
    };
};

module.exports = {
    getPortfolio,
    updatePortfolio,
    publishPortfolio,
    getComponentForm,
    getPortfolioDashboard,
    detachPortfolioFrom,
    deletePortfolio,
    assignStudentToPortfolio,
    getStudentsByCourse,
    updateStudentReview,
    getPortfolioForAssignEvaluator,
    updatePortfolioChildField,
    updateFormInPortfolio,
    getCountForPortfolioDashboard,
    getInsights,
};
