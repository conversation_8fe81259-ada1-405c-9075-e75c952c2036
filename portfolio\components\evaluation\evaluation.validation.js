const { Joi } = require('../../common/middlewares/validation');

const { objectIdSchema, objectIdRQSchema } = require('../../../lib/utility/validationSchemas');

const assignEvaluatorSchema = Joi.object({
    body: Joi.object({
        portfolioId: objectIdRQSchema,
        componentId: objectIdRQSchema,
        childrenId: objectIdRQSchema,
    }),
}).unknown(true);

const getInfrastructuresForAssignEvaluatorSchema = Joi.object({
    query: Joi.object({
        portfolioId: objectIdRQSchema,
        componentId: objectIdRQSchema,
        childrenId: objectIdRQSchema,
    }),
}).unknown(true);

const switchEvaluationTypeSchema = Joi.object({
    query: Joi.object({
        portfolioId: objectIdRQSchema,
        componentId: objectIdRQSchema,
        childrenId: objectIdRQSchema,
    }),
}).unknown(true);

const getEvaluatorsSchema = Joi.object({
    query: Joi.object({
        portfolioId: objectIdRQSchema,
        componentId: objectIdRQSchema,
        childrenId: objectIdRQSchema,
        isGeneralUser: Joi.boolean().optional(),
    }),
});

const getStudentGroupsForAssignEvaluatorSchema = Joi.object({
    query: Joi.object({
        portfolioId: objectIdRQSchema,
        componentId: objectIdRQSchema,
        childrenId: objectIdRQSchema,
        isStudentGroup: Joi.boolean(),
        deliveryTypeSymbol: Joi.string().optional(),
        deliveryTypeId: objectIdSchema.optional(),
    }),
});

const getDeliveryTypeForAssignEvaluatorSchema = Joi.object({
    query: Joi.object({
        portfolioId: objectIdRQSchema,
        componentId: objectIdRQSchema,
    }),
}).unknown(true);

const updateStudentsForPrepareAndPublishSchema = Joi.object({
    body: Joi.object({
        portfolioId: objectIdRQSchema,
        componentId: objectIdRQSchema,
        childrenId: objectIdRQSchema,
    }),
}).unknown(true);

const getChildrenForPrepareAndPublishSchema = Joi.object({
    query: Joi.object({
        programId: objectIdRQSchema,
        courseId: objectIdRQSchema,
        institutionCalendarId: objectIdRQSchema,
        term: Joi.string().required(),
        year: Joi.string().required(),
        level: Joi.string().required(),
        curriculumId: objectIdSchema.optional(),
        rotation: Joi.string().optional(),
        rotationCount: Joi.string().optional(),
    }),
}).unknown(true);

const validateChildIdSchema = Joi.object({
    query: Joi.object({
        portfolioId: objectIdRQSchema,
        componentId: objectIdRQSchema,
        childrenId: objectIdRQSchema,
    }),
});

module.exports = {
    assignEvaluatorSchema,
    getInfrastructuresForAssignEvaluatorSchema,
    switchEvaluationTypeSchema,
    getEvaluatorsSchema,
    getStudentGroupsForAssignEvaluatorSchema,
    getDeliveryTypeForAssignEvaluatorSchema,
    updateStudentsForPrepareAndPublishSchema,
    getChildrenForPrepareAndPublishSchema,
    validateChildIdSchema,
};
