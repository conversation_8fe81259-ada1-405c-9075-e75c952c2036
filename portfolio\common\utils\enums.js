const enums = Object.freeze({
    ON_GOING: 'ON_GOING',
    PUBLISHED: 'PUBLISHED',
    COMPLETED: 'COMPLETED',
    NOT_STARTED: 'NOT_STARTED',
    SUBMITTED: 'SUBMITTED',

    // Rubrics
    POINT: 'POINT',
    PERCENTAGE: 'PERCENTAGE',
    DIMENSIONAL: 'DIMENSIONAL',
    COMPONENTIAL: 'COMPONENTIAL',
    HEADER: 'HEADER',
    SUB_HEADER: 'SUB_HEADER',
    YET_TO_EVALUATE: 'YET_TO_EVALUATE',
    STARTED: 'STARTED',
    CUSTOM: 'CUSTOM',
    ANGOFF: 'ANGOFF',
    BORDER_LINE_REGRESSION: 'BORDER_LINE_REGRESSION',
    INTERNAL: 'INTERNAL',
    EXTERNAL: 'EXTERNAL',
    DRAFT: 'DRAFT',
    SENDING: 'SENDING',
    SENT: 'SENT',
    EVALUATED: 'EVALUATED',
    LB: 'LB',
    REJECTED: 'REJECTED',
    APPROVED: 'APPROVED',
    ABSENT: 'ABSENT',
    PRESENT: 'PRESENT',
    PASS: 'PASS',
    FAIL: 'FAIL',
    IN_PROGRESS: 'IN_PROGRESS',
    APPROVAL_PENDING: 'APPROVAL_PENDING',
    EVALUATION_PENDING: 'EVALUATION_PENDING',
    CONTINUE_APPROVAL: 'CONTINUE_APPROVAL',
    READY_TO_APPROVE: 'READY_TO_APPROVE',
    PARTIALLY_APPROVED: 'PARTIALLY_APPROVED',
    ALL_APPROVED: 'ALL_APPROVED',
    CONTINUE_EVALUATE: 'CONTINUE_EVALUATE',
    READY_TO_EVALUATE: 'READY_TO_EVALUATE',
    PARTIALLY_EVALUATE: 'PARTIALLY_EVALUATE',
    ALL_EVALUATE: 'ALL_EVALUATE',
    RESUBMIT: 'RESUBMIT',
    REVOKE: 'REVOKE',
    PENDING: 'PENDING',
    NO_SUBMISSION: 'NO_SUBMISSION',
});

module.exports = enums;
