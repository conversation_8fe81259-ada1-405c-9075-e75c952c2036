const { capitalize, nameFormatter } = require('../utility/common_functions');
const { clone } = require('../utility/common');
const { TIMEZONE, LOCAL_TIMEZONE, APP_STATE } = require('../utility/util_keys');
const { DS_PRODUCTION_ENV } = require('../utility/constants');
const moment = require('moment-timezone');

/**
 * Sort matching dates by schedule date and start time
 * @param {Array} matchingDates - Array of matching date objects
 * @returns {Array} Sorted array of matching dates
 */
const sortMatchingDatesByDateTime = (matchingDates) => {
    return matchingDates.sort((a, b) => {
        return new Date(a.scheduleStart) - new Date(b.scheduleStart);
    });
};

// Helper function to convert time to 24-hour format
const convertTo24Hour = (timeObj) => {
    let hour = timeObj.hour;
    if (timeObj.format === 'PM' && hour !== 12) hour += 12;
    if (timeObj.format === 'AM' && hour === 12) hour = 0;
    return hour;
};

// Helper function to create UTC timestamp with timezone handling
const createUTCTimestamp = (date, timeObj) => {
    const timeZone = APP_STATE.toLowerCase() === DS_PRODUCTION_ENV ? TIMEZONE : LOCAL_TIMEZONE;
    const dateObj = new Date(date);
    const hour = convertTo24Hour(timeObj);
    const minute = timeObj.minute;

    const dateString = `${dateObj.getFullYear()}-${String(dateObj.getMonth() + 1).padStart(
        2,
        '0',
    )}-${String(dateObj.getDate()).padStart(2, '0')} ${String(hour).padStart(2, '0')}:${String(
        minute,
    ).padStart(2, '0')}`;

    return moment.tz(dateString, 'YYYY-M-D H:m', timeZone).tz('UTC').toISOString();
};

/**
 * Generate matching dates based on occurrences and date range
 * @param {Array} occurrences - Array of occurrence objects with day, start, and end times
 * @param {string} occurrenceStartDate - Start date for the occurrence range
 * @param {string} occurrenceEndDate - End date for the occurrence range
 * @returns {Array} Array of matching dates sorted by date and time
 */
const generateMatchingDates = ({ occurrences, occurrenceStartDate, occurrenceEndDate }) => {
    const matchingDates = [];
    const courseEnd = new Date(occurrenceEndDate);
    // let count = 1;
    occurrences.forEach((occurrenceElement) => {
        let courseStart = new Date(occurrenceStartDate);
        while (courseStart.toISOString() <= courseEnd.toISOString()) {
            if (
                courseStart.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase() ===
                occurrenceElement.day.toString()
            ) {
                const scheduleStart = createUTCTimestamp(courseStart, occurrenceElement.start);
                const scheduleEnd = createUTCTimestamp(courseStart, occurrenceElement.end);

                matchingDates.push({
                    schedule_date: courseStart.toISOString(),
                    start: occurrenceElement.start,
                    end: occurrenceElement.end,
                    scheduleStart,
                    scheduleEnd,
                    // week: count,
                    scheduled: false,
                    matchingCheck: false,
                    day: occurrenceElement.day.toString().toUpperCase().substring(0, 3),
                });
            }
            courseStart = new Date(courseStart.setDate(courseStart.getDate() + 1));
            // if (courseStart.toLocaleDateString('en-US', { weekday: 'long' }) === 'Monday')
            //     count++;
        }
    });

    // Sort matchingDates by schedule_date and start time
    return sortMatchingDatesByDateTime(matchingDates);
};
/**
 * Process and format schedule data with course program details
 * @param {Array} dateBasedSchedule - Array of schedule data
 * @returns {Array} Processed schedule data with formatted details
 */
const processScheduleData = ({ dateBasedSchedule = [] }) => {
    const scheduleDatas = [];
    const skipIds = new Set();

    // Create deep copy to avoid modifying original data
    const sortedSchedules = clone(dateBasedSchedule).sort(
        (a, b) => (a.session?.s_no ?? 0) - (b.session?.s_no ?? 0),
    );

    const scheduleMap = new Map();
    sortedSchedules.forEach((s) => scheduleMap.set(String(s._id), s));

    const formatTime = ({ hour, minute, format }) =>
        `${hour}:${minute === 0 ? '00' : minute} ${format}`;

    const getSessionIdentifier = ({ session, type, title, mergedSessions, merge_status }) => {
        if (session?.delivery_symbol) {
            let id = `${session.delivery_symbol}${session.delivery_no}`;
            if (merge_status && mergedSessions) id += mergedSessions;
            return id;
        }
        return `${type}-${title}`;
    };

    const buildCourseProgramDetails = (schedule) => {
        const { program_name, year_no, level_no, term, course_code, start, end } = schedule;
        const programPart = `${program_name}-${year_no}-${level_no}-${capitalize(
            term,
        )}-${course_code}`;
        const sessionPart = getSessionIdentifier(schedule);
        const timePart = `${formatTime(start)} to ${formatTime(end)}`;
        return `${programPart}-${sessionPart} for ${timePart}`;
    };

    for (const schedule of sortedSchedules) {
        const scheduleId = String(schedule._id);

        // Handle merged sessions
        if (schedule.merge_status && Array.isArray(schedule.merge_with)) {
            const mergedSessions = schedule.merge_with
                .map(({ schedule_id }) => {
                    const merged = scheduleMap.get(String(schedule_id));
                    if (merged) {
                        skipIds.add(String(merged._id));
                        return getSessionIdentifier(merged);
                    }
                    return null;
                })
                .filter(Boolean);

            schedule.mergedSessions = mergedSessions.length ? ', ' + mergedSessions.join(', ') : '';
        }

        schedule.courseProgramDetails = buildCourseProgramDetails(schedule);

        if (!skipIds.has(scheduleId)) {
            scheduleDatas.push(schedule);
        }
    }

    return scheduleDatas;
};

const groupNameFormatter = (group_name, offset) => {
    if (typeof group_name !== 'string') return group_name;
    const splitted = group_name.split('-');
    return splitted.slice(splitted.length - offset, splitted.length).join('-');
};

const sessionScheduleCheck = ({ scheduledSession, studentGroups }) => {
    const studentGroupNames = new Set();
    const sessionGroupIdMap = new Map();

    // Single pass through studentGroups to build both data structures
    studentGroups.forEach((group) => {
        const groupName = group.group_name?.toString();
        if (groupName) {
            studentGroupNames.add(groupName);
        }

        // Build session group map in the same loop
        group.session_group?.forEach((sessionGroup) => {
            const sessionGroupId = sessionGroup.session_group_id?.toString();
            if (sessionGroupId) {
                sessionGroupIdMap.set(sessionGroupId, sessionGroup.group_name);
            }
        });
    });

    // Early return if no valid student group names
    if (studentGroupNames.size === 0) return true;

    // Optimize: Use Set for unique session group IDs and array for conflicts
    const sessionGroupIds = new Set();
    const sessionGroupConflict = [];

    // Process scheduled sessions
    for (const sessionElement of scheduledSession) {
        // Optimize: Use filter with early return pattern
        const matchingGroups =
            sessionElement.student_groups?.filter((group) => {
                const groupName = group.group_name?.toString();
                return groupName && studentGroupNames.has(groupName);
            }) || [];

        // Process matching groups
        for (const group of matchingGroups) {
            // Optimize: Use filter with early return pattern
            const matchingSessionGroups =
                group.session_group?.filter((sessionGroup) => {
                    const sessionGroupId = sessionGroup.session_group_id?.toString();
                    return sessionGroupId && sessionGroupIdMap.has(sessionGroupId);
                }) || [];

            if (matchingSessionGroups.length > 0) {
                // Optimize: Process session group names and IDs in single pass
                const sessionGroupNames = matchingSessionGroups
                    .map((sessionGroup) => groupNameFormatter(sessionGroup.group_name, 3))
                    .join(', ');

                // Add session group IDs to Set for uniqueness
                matchingSessionGroups.forEach((sessionGroup) => {
                    const sessionGroupId = sessionGroup.session_group_id?.toString();
                    if (sessionGroupId) {
                        sessionGroupIds.add(sessionGroupId);
                    }
                });

                sessionGroupConflict.push({
                    sessionGroupNames,
                });
            }
        }
    }

    // Return conflict data if any conflicts found
    if (sessionGroupIds.size > 0) {
        return {
            sessionGroupIds: Array.from(sessionGroupIds),
            conflictMessage: sessionGroupConflict
                .map(
                    (conflict) =>
                        `Course Delivery Group ${conflict.sessionGroupNames} is already scheduled`,
                )
                .join(', '),
        };
    }

    return true;
};

/**
 * Get student IDs that match session group IDs from courseGroup setting
 * @param {Object} courseGroup - Course group object with setting array
 * @param {Array} studentGroups - Array of student groups with session_group
 * @returns {Array} Array of student IDs that match the session group IDs
 */
const getMatchingStudentIds = (courseGroup, studentGroups) => {
    const matchingStudentIds = [];

    // Create a Set of session group IDs from studentGroups for O(1) lookup
    const sessionGroupIds = new Set();
    studentGroups.forEach((studentGroup) => {
        studentGroup.session_group.forEach((sessionGroup) => {
            sessionGroupIds.add(sessionGroup.session_group_id.toString());
        });
    });

    // Iterate through courseGroup settings to find matching session groups
    courseGroup.setting.forEach((setting) => {
        setting.session_setting.forEach((sessionSetting) => {
            sessionSetting.groups.forEach((group) => {
                // Check if this group's _id matches any session_group_id in studentGroups
                if (sessionGroupIds.has(group._id.toString())) {
                    // Add all student IDs from this group
                    if (group._student_ids && Array.isArray(group._student_ids)) {
                        matchingStudentIds.push(...group._student_ids);
                    }
                }
            });
        });
    });

    // Return unique student IDs
    return [...new Set(matchingStudentIds)];
};

/**
 * Check infrastructure conflicts
 * @param {Object} scheduleElement - Schedule element to check
 * @param {string} infraId - Infrastructure ID
 * @param {string} mode - Schedule mode
 * @param {string} infraName - Infrastructure name
 * @param {Array} errorResponse - Current error response array
 * @returns {Array} Updated error response array
 */
const checkInfrastructureConflict = (scheduleElement, infraId, mode, infraName, errorResponse) => {
    if (
        infraId &&
        mode === 'onsite' &&
        scheduleElement._infra_id &&
        String(scheduleElement._infra_id) === String(infraId) &&
        !errorResponse.find((error) => error.field === 'infra')
    ) {
        errorResponse.push({
            field: 'infra',
            message: `Selected Infrastructure is already scheduled in ${scheduleElement.courseProgramDetails}`,
        });
    }
    return errorResponse;
};

/**
 * Check staff conflicts
 * @param {Object} scheduleElement - Schedule element to check
 * @param {Array} staffs - Staff array
 * @param {Array} errorResponse - Current error response array
 * @returns {Array} Updated error response array
 */
const checkStaffConflict = (scheduleElement, staffs, errorResponse) => {
    const staffCheck = scheduleElement.staffs.filter((scheduleStaff) =>
        staffs.some((staff) => String(staff) === String(scheduleStaff._staff_id)),
    );

    if (staffCheck.length > 0) {
        const scheduledStaffNames = staffCheck.map((scheduleStaff) =>
            nameFormatter(scheduleStaff.staff_name),
        );
        const staffError = errorResponse.findIndex((error) => error.field === 'staff');
        if (staffError === -1) {
            errorResponse.push({
                field: 'staff',
                message: `Selected Faculty - ${scheduledStaffNames} is already scheduled in ${scheduleElement.courseProgramDetails}`,
            });
        } else {
            errorResponse[staffError].message += `, ${scheduledStaffNames}`;
        }
    }
    return errorResponse;
};

/**
 * Check student group conflicts
 * @param {Object} scheduleElement - Schedule element to check
 * @param {Array} studentGroups - Student groups array
 * @returns {Object} Object containing group names and course group names
 */
const checkStudentGroupConflict = (scheduleElement, studentGroups) => {
    const courseGroupNames = [];
    const groupNames = [];

    const courseGroupCheck = scheduleElement.student_groups.filter((scheduleStudentGroup) =>
        studentGroups.some(
            (studentGroup) =>
                scheduleStudentGroup.group_name.toString() === studentGroup.group_name.toString(),
        ),
    );

    courseGroupCheck.forEach((scheduleStudentGroup) => {
        const studentGroupLocation = studentGroups.findIndex(
            (studentGroup) =>
                studentGroup.group_name.toString() === scheduleStudentGroup.group_name.toString(),
        );

        const studentGroupSessionCheck = scheduleStudentGroup.session_group.filter(
            (scheduleStudentGroupSession) =>
                studentGroups[studentGroupLocation].session_group.some(
                    (studentGroupSession) =>
                        scheduleStudentGroupSession.session_group_id.toString() ===
                        studentGroupSession.session_group_id.toString(),
                ),
        );

        if (studentGroupSessionCheck.length > 0) {
            studentGroupSessionCheck.forEach((scheduleStudentGroupSession) => {
                courseGroupNames.push(scheduleStudentGroupSession.group_name);
            });
            groupNames.push(scheduleStudentGroup.group_name);
        }
    });

    return { groupNames, courseGroupNames };
};

/**
 * Check student conflicts
 * @param {Object} scheduleElement - Schedule element to check
 * @param {Array} groupStudentIds - Group student IDs
 * @param {Array} groupNames - Group names (if empty, check students)
 * @returns {Array} Student check array
 */
const checkStudentConflict = (scheduleElement, groupStudentIds, groupNames) => {
    if (groupNames.length > 0) return [];

    return groupStudentIds.filter((groupStudentId) =>
        scheduleElement.students.some(
            (studentId) => String(studentId._id) === String(groupStudentId),
        ),
    );
};

/**
 * Process schedule conflicts and generate error responses
 * @param {Array} scheduleDatas - Schedule data array
 * @param {Object} occurrenceData - Occurrence data
 * @param {Object} sessionData - Session data
 * @param {Array} studentGroups - Student groups array
 * @param {Array} groupStudentIds - Group student IDs
 * @param {Array} errorResponse - Error response array to be modified directly
 */
const processScheduleConflicts = (
    scheduleDatas,
    occurrenceData,
    sessionData,
    studentGroups,
    groupStudentIds,
    errorResponse = [],
) => {
    const occurrenceStartDate = new Date(occurrenceData.scheduleStart);
    const occurrenceEndDate = new Date(occurrenceData.scheduleEnd);

    const allStudentCheck = [];

    scheduleDatas.forEach((scheduleElement) => {
        const scheduleStartDate = new Date(scheduleElement.scheduleStartDateAndTime);
        const scheduleEndDate = new Date(scheduleElement.scheduleEndDateAndTime);

        if (
            (scheduleStartDate <= occurrenceStartDate && scheduleEndDate >= occurrenceStartDate) ||
            (scheduleStartDate <= occurrenceEndDate && scheduleEndDate >= occurrenceEndDate)
        ) {
            // Check infrastructure conflicts
            checkInfrastructureConflict(
                scheduleElement,
                sessionData.infraId,
                sessionData.mode,
                sessionData.infraName,
                errorResponse,
            );

            // Check staff conflicts
            checkStaffConflict(scheduleElement, sessionData.staffs, errorResponse);

            // Check student group conflicts and push errors immediately
            const { groupNames, courseGroupNames } = checkStudentGroupConflict(
                scheduleElement,
                studentGroups,
            );

            if (groupNames.length > 0) {
                const uniqueGroupNames = [...new Set(groupNames)];
                const groupNamesString = uniqueGroupNames
                    .map((groupName) => groupNameFormatter(groupName, 2))
                    .join(', ');

                errorResponse.push({
                    field: 'course_group',
                    message: `Course Group ${groupNamesString} is already scheduled in ${scheduleElement.courseProgramDetails}`,
                });

                if (
                    courseGroupNames.length > 0 &&
                    !errorResponse.find((error) => error.field === 'session_group')
                ) {
                    const uniqueCourseGroupNames = [...new Set(courseGroupNames)];
                    const sessionGroupNames = uniqueCourseGroupNames
                        .map((courseGroupName) => groupNameFormatter(courseGroupName, 3))
                        .join(', ');
                    const sessionGroupError = errorResponse.findIndex(
                        (error) => error.field === 'session_group',
                    );
                    if (sessionGroupError === -1) {
                        errorResponse.push({
                            field: 'session_group',
                            message: `Course Delivery Group ${sessionGroupNames} is already scheduled in ${scheduleElement.courseProgramDetails}`,
                        });
                    } else {
                        errorResponse[sessionGroupError].message += `, ${sessionGroupNames}`;
                    }
                }
            }

            // Check student conflicts only if no group conflicts found
            if (groupNames.length === 0 && courseGroupNames.length === 0) {
                const studentCheck = checkStudentConflict(
                    scheduleElement,
                    groupStudentIds,
                    groupNames,
                );
                allStudentCheck.push(...studentCheck);
            }
        }
    });

    // Process student conflicts
    if (allStudentCheck.length > 0) {
        const uniqueStudentCheck = [...new Set(allStudentCheck)];
        const conflictingSchedule = scheduleDatas.find((schedule) =>
            schedule.students.some((student) => uniqueStudentCheck.includes(String(student._id))),
        );
        const scheduleDetails = conflictingSchedule?.courseProgramDetails || 'Multiple schedules';
        errorResponse.push({
            field: 'student',
            message: `${uniqueStudentCheck.length} Students are already scheduled in ${scheduleDetails}`,
        });
    }
};

module.exports = {
    sortMatchingDatesByDateTime,
    generateMatchingDates,
    processScheduleData,
    sessionScheduleCheck,
    groupNameFormatter,
    getMatchingStudentIds,
    checkInfrastructureConflict,
    checkStaffConflict,
    checkStudentGroupConflict,
    checkStudentConflict,
    processScheduleConflicts,
};
