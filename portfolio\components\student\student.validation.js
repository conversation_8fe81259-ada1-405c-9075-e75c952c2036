const { Joi } = require('../../common/middlewares/validation');
const { objectIdSchema } = require('../../../lib/utility/validationSchemas');

const getStudentCoursesSchema = Joi.object({
    query: Joi.object({
        institutionCalendarId: objectIdSchema.required(),
    }),
}).unknown(true);

const getStudentPortfolioSchema = Joi.object({
    query: Joi.object({
        programId: objectIdSchema.required(),
        courseId: objectIdSchema.required(),
        institutionCalendarId: objectIdSchema.required(),
    }),
}).unknown(true);

const getGlobalRubricSchema = Joi.object({
    query: Joi.object({
        portfolioId: objectIdSchema.required(),
        componentId: objectIdSchema.required(),
        childId: objectIdSchema.required(),
        studentId: objectIdSchema.required(),
    }),
}).unknown(true);

const validateExtraEntrySchema = Joi.object({
    query: Joi.object({
        portfolioId: objectIdSchema.required(),
        componentId: objectIdSchema.required(),
        childId: objectIdSchema.required(),
    }),
}).unknown(true);

const getStudentInsightsSchema = Joi.object({
    query: Joi.object({
        portfolioId: objectIdSchema.required(),
        componentId: objectIdSchema.required(),
    }),
}).unknown(true);

module.exports = {
    getStudentCoursesSchema,
    getStudentPortfolioSchema,
    getGlobalRubricSchema,
    validateExtraEntrySchema,
    getStudentInsightsSchema,
};
