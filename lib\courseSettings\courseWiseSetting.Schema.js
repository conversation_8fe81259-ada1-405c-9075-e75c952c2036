const {
    Schema,
    model,
    Types: { ObjectId },
} = require('mongoose');

const {
    COURSE_DELIVERY_GROUP_ADMIN,
    INSTITUTION,
    INSTITUTION_CALENDAR,
    DIGI_PROGRAM,
    DIGI_COURSE,
    USER,
    GENDER: { MALE, FEMALE, BOTH },
} = require('../utility/constants');

const courseDeliveryGroupAdmin = new Schema(
    {
        _institution_id: {
            type: ObjectId,
            ref: INSTITUTION,
        },
        institutionCalendarId: {
            type: ObjectId,
            ref: INSTITUTION_CALENDAR,
            required: true,
        },
        programId: {
            type: ObjectId,
            ref: DIGI_PROGRAM,
            required: true,
        },
        courseId: {
            type: ObjectId,
            ref: DIGI_COURSE,
            required: true,
        },
        term: {
            type: String,
            required: true,
        },
        yearNo: {
            type: String,
            required: true,
        },
        levelNo: {
            type: String,
            required: true,
        },
        rotation: {
            type: String,
            default: 'no',
        },
        rotation_count: {
            type: Number,
        },
        deliveryType: {
            type: String,
            required: true,
        },
        gender: {
            type: String,
            enum: [MALE, FEMALE, BOTH],
            required: true,
        },
        groupName: {
            type: String,
            required: true,
        },
        groupId: {
            type: ObjectId,
        },
        userIds: [
            {
                type: ObjectId,
                ref: USER,
            },
        ],
        sessionId: {
            type: ObjectId,
        },
    },
    { timestamps: true },
);
module.exports = model(COURSE_DELIVERY_GROUP_ADMIN, courseDeliveryGroupAdmin);
