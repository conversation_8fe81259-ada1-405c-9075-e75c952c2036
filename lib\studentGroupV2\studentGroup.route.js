const express = require('express');
const catchAsync = require('../utility/catch-async');
const {
    userAuthorProgramList,
    userAuthorProgramYearLevelList,
    courseDeliveryType,
    selectedGroupSetting,
    editGroupSetting,
    selectedProgramList,
    publishedStudentGrouping,
    importedStudentViewList,
    addSingleStudent,
    getSingleStudentDetails,
    autoGenerateGroupNames,
    deleteStudents,
    groupingStudent,
    groupingCount,
    totalCompletedSession,
    yearWiseImportedStudentList,
    pushStudentInExistingSchedule,
} = require('./studentGroup.controller');
const { userPolicyAuthentication, defaultPolicy } = require('../../middleware/policy.middleware');
const { validate } = require('../../middleware/validation');
const {
    userAuthorProgramListValidation,
    courseDeliveryTypeValidation,
    selectedGroupSettingValidation,
    editGroupSettingValidation,
    selectedProgramListValidation,
    publishedStudentGroupingValidation,
    importedStudentViewListValidation,
    addSingleStudentValidation,
    getSingleStudentDetailsValidation,
    autoGenerateGroupNamesValidation,
    deleteStudentsValidation,
    groupingStudentValidation,
    totalCompletedSessionValidation,
    yearWiseImportedStudentListValidation,
} = require('./studentGroup.validator');

const route = express.Router();

route.get(
    '/userAuthorProgramList',
    [
        userPolicyAuthentication(['program_student_group:program_list:view']),
        validate([userAuthorProgramListValidation]),
    ],
    catchAsync(userAuthorProgramList),
);

route.get(
    '/userAuthorProgramYearLevelList',
    [
        userPolicyAuthentication(['program_student_group:program_list:view']),
        // validate([userAuthorProgramListValidation]),
    ],
    catchAsync(userAuthorProgramYearLevelList),
);

route.get(
    '/courseDeliveryType',
    [
        userPolicyAuthentication([
            'program_student_group:program_list:year_group:view',
            'program_student_group:program_list:level_group:view',
            'program_student_group:program_list:course_group:view',
        ]),
    ],
    validate([
        {
            schema: courseDeliveryTypeValidation.query,
            property: 'query',
        },
    ]),
    catchAsync(courseDeliveryType),
);

route.post(
    '/selectedGroupSetting',
    [
        userPolicyAuthentication([
            'program_student_group:program_list:year_group:view',
            'program_student_group:program_list:year_group:add_group',
            'program_student_group:program_list:year_group:import',
            'program_student_group:program_list:year_group:edit_group',
            'program_student_group:program_list:level_group:view',
            'program_student_group:program_list:level_group:import',
            'program_student_group:program_list:level_group:add_group',
            'program_student_group:program_list:level_group:edit_group',
            'program_student_group:program_list:course_group:view',
            'program_student_group:program_list:course_group:add_group',
            'program_student_group:program_list:course_group:edit_group',
            'program_student_group:program_list:course_group:import',
        ]),
    ],
    validate([
        {
            schema: selectedGroupSettingValidation.headers,
            property: 'headers',
        },
        {
            schema: selectedGroupSettingValidation.body,
            property: 'body',
        },
    ]),
    catchAsync(selectedGroupSetting),
);

route.put(
    '/editGroupSetting',
    [
        userPolicyAuthentication([
            'program_student_group:program_list:year_group:view',
            'program_student_group:program_list:year_group:import',
            'program_student_group:program_list:year_group:edit_group',
            'program_student_group:program_list:level_group:import',
            'program_student_group:program_list:level_group:view',
            'program_student_group:program_list:level_group:edit_group',
            'program_student_group:program_list:course_group:view',
            'program_student_group:program_list:course_group:edit_group',
            'program_student_group:program_list:course_group:import',
        ]),
    ],
    validate([
        {
            schema: editGroupSettingValidation.body,
            property: 'body',
        },
    ]),
    catchAsync(editGroupSetting),
);

route.get(
    '/selectedProgramList',
    [
        userPolicyAuthentication([
            'program_student_group:program_list:year_group:view',
            'program_student_group:program_list:level_group:view',
            'program_student_group:program_list:course_group:view',
        ]),
    ],
    validate([
        {
            schema: selectedProgramListValidation.headers,
            property: 'headers',
        },
        {
            schema: selectedProgramListValidation.body,
            property: 'query',
        },
    ]),
    catchAsync(selectedProgramList),
);

route.put(
    '/publishedStudentGrouping',
    [
        userPolicyAuthentication([
            'program_student_group:program_list:level_group:publish',
            'program_student_group:program_list:year_group:publish',
            'program_student_group:program_list:course_group:publish',
        ]),
    ],
    validate([
        {
            schema: selectedProgramListValidation.headers,
            property: 'headers',
        },
        {
            schema: publishedStudentGroupingValidation.body,
            property: 'body',
        },
    ]),
    catchAsync(publishedStudentGrouping),
);

route.get(
    '/importedStudentViewList',
    [
        userPolicyAuthentication([
            'program_student_group:program_list:year_group:view',
            'program_student_group:program_list:level_group:view',
            'program_student_group:program_list:course_group:view',
            'program_student_group:program_list:course_group:import',
            'program_student_group:program_list:level_group:import',
            'program_student_group:program_list:year_group:import',
        ]),
    ],
    validate([
        {
            schema: importedStudentViewListValidation.query,
            property: 'query',
        },
    ]),
    catchAsync(importedStudentViewList),
);

route.get(
    '/getSingleStudentDetails',
    [
        userPolicyAuthentication([
            'program_student_group:program_list:year_group:view',
            'program_student_group:program_list:level_group:view',
            'program_student_group:program_list:course_group:view',
        ]),
    ],
    validate([
        {
            schema: getSingleStudentDetailsValidation.body,
            property: 'query',
        },
    ]),
    catchAsync(getSingleStudentDetails),
);

route.put(
    '/addSingleStudent',
    [
        userPolicyAuthentication([
            'program_student_group:program_list:year_group:add_student',
            'program_student_group:program_list:level_group:add_group',
            'program_student_group:program_list:course_group:add_group',
        ]),
    ],
    validate([
        {
            schema: addSingleStudentValidation.headers,
            property: 'headers',
        },
        {
            schema: addSingleStudentValidation.body,
            property: 'body',
        },
    ]),
    catchAsync(addSingleStudent),
);

route.get(
    '/autoGenerateGroupNames',
    [
        userPolicyAuthentication([
            'program_student_group:program_list:year_group:view',
            'program_student_group:program_list:level_group:view',
            'program_student_group:program_list:course_group:view',
        ]),
    ],
    validate([
        {
            schema: autoGenerateGroupNamesValidation.body,
            property: 'query',
        },
    ]),
    catchAsync(autoGenerateGroupNames),
);

route.put(
    '/deleteStudents',
    [
        userPolicyAuthentication([
            'program_student_group:program_list:level_group:delete',
            'program_student_group:program_list:year_group:delete',
            'program_student_group:program_list:course_group:delete',
        ]),
    ],
    validate([
        {
            schema: deleteStudentsValidation.body,
            property: 'body',
        },
    ]),
    catchAsync(deleteStudents),
);

route.put(
    '/groupingStudent',
    [
        userPolicyAuthentication([
            'program_student_group:program_list:course_group:group',
            'program_student_group:program_list:level_group:group',
            'program_student_group:program_list:year_group:group',
            'program_student_group:program_list:year_group:move_to',
            'program_student_group:program_list:level_group:move_to',
            'program_student_group:program_list:course_group:move_to',
        ]),
    ],
    validate([
        {
            schema: groupingStudentValidation.body,
            property: 'body',
        },
    ]),
    catchAsync(groupingStudent),
);

route.get(
    '/groupingCount',
    [
        userPolicyAuthentication([
            'program_student_group:program_list:year_group:view',
            'program_student_group:program_list:level_group:view',
            'program_student_group:program_list:course_group:view',
        ]),
    ],
    validate([
        {
            schema: importedStudentViewListValidation.query,
            property: 'query',
        },
    ]),
    catchAsync(groupingCount),
);

route.post(
    '/totalCompletedSession',
    [
        userPolicyAuthentication([
            'program_student_group:program_list:year_group:move_to',
            'program_student_group:program_list:level_group:move_to',
            'program_student_group:program_list:course_group:move_to',
        ]),
    ],
    validate([
        {
            schema: totalCompletedSessionValidation.headers,
            property: 'headers',
        },
        {
            schema: totalCompletedSessionValidation.body,
            property: 'body',
        },
    ]),
    catchAsync(totalCompletedSession),
);

route.get(
    '/yearWiseImportedStudentList',
    [
        userPolicyAuthentication([
            'program_student_group:program_list:year_group:view',
            'program_student_group:program_list:level_group:view',
            'program_student_group:program_list:course_group:view',
        ]),
    ],
    validate([
        {
            schema: yearWiseImportedStudentListValidation.headers,
            property: 'headers',
        },
        {
            schema: yearWiseImportedStudentListValidation.query,
            property: 'query',
        },
    ]),
    catchAsync(yearWiseImportedStudentList),
);

route.put(
    '/pushStudentInExistingSchedule',
    [
        userPolicyAuthentication([
            'program_student_group:program_list:year_group:edit_group',
            'program_student_group:program_list:level_group:edit_group',
            'program_student_group:program_list:course_group:edit_group',
        ]),
    ],
    catchAsync(pushStudentInExistingSchedule),
);
module.exports = route;
