const {
    logger,
    SERVICES: { SG_MAX_MARK },
} = require('../utility/util_keys');
const { convertToMongoObjectId, query: commonQuery } = require('../utility/common');
const { getPaginationValues } = require('../utility/pagination');
const programCurriculumSchema = require('../models/digi_curriculum');
const yearLevelAuthorSchema = require('../models/yearLevelAuthor');
const programSchema = require('../models/digi_programs');
const programCalendarSchema = require('../models/program_calendar');
const courseSchema = require('../models/digi_course');
const programCalenderSchema = require('../models/program_calendar');
const studentGroupSettingSchema = require('./studentGroupSetting.model');
const userSchema = require('../models/user');
const courseScheduleSchema = require('../models/course_schedule');
const studentGroupSchema = require('../models/student_group');
const sessionTypeDeliverySchema = require('../models/digi_session_delivery_types');
const courseSessionOrderSchema = require('../models/digi_session_order');
const {
    YEAR_LEVEL_AUTHOR_TYPE_MODULE,
    EVENT_WHOM: { STUDENT, STAFF },
    DS_COURSE_KEY,
    YEAR_LEVEL: { YEAR, LEVEL },
    GENDER: { MALE, FEMALE, BOTH },
    CLUSTER,
    MARK,
    ACADEMIC,
    ALPHABETICAL,
    SESSION_MODE: { AUTO },
    GROUPED,
    UNGROUPED,
    MIXED,
    ROTATION: { YES },
    PROGRAM_TYPE: { PREREQUISITE },
    STUDENT_GROUP_MODE: { FYD, COURSE, ROTATION },
    PUBLISHED,
    PENDING,
    MISSED,
    COMPLETED,
    ONGOING,
    BATCH: { REGULAR },
} = require('../utility/constants');
const {
    removedStudentCheckExistAssignment,
} = require('../../commonService/api/assignment_module/assignment/assignment.controller');
const { usersCoursesRedisCacheRemove } = require('../../service/redisCache.service');
const { studentGroupRedisKeyUpdates } = require('../utility/utility.service');

const getYearLevelList = async ({ programId }) => {
    try {
        const programCurriculumList = await programCurriculumSchema
            .findOne(
                {
                    _program_id: convertToMongoObjectId(programId),
                },
                {
                    curriculum_name: 1,
                    'year_level.y_type': 1,
                    'year_level.levels.level_name': 1,
                },
            )
            .sort({ _id: 1 })
            .lean();

        if (!programCurriculumList) return [];

        const yearLevelAuthors = await yearLevelAuthorSchema
            .find({ programId: convertToMongoObjectId(programId) })
            .populate({
                path: 'users.userId',
                select: { name: 1 },
            })
            .lean();

        return {
            curriculumId: programCurriculumList._id,
            curriculumName: programCurriculumList.curriculum_name,
            year: programCurriculumList.year_level.map((yearElement) => {
                const yearAuthor = yearLevelAuthors.find(
                    (author) =>
                        author.yearName === yearElement.y_type &&
                        author.authorType === YEAR_LEVEL_AUTHOR_TYPE_MODULE.YEAR,
                );

                return {
                    yearName: yearElement.y_type,
                    // termName: yearAuthor?.termName || null,
                    users:
                        yearAuthor?.users.map((user) => ({
                            userId: user.userId._id,
                            name: user.userId.name,
                            modules: user.modules,
                        })) || [],
                    level: yearElement.levels.map((levelElement) => {
                        const levelAuthor = yearLevelAuthors.find(
                            (author) =>
                                author.yearName === yearElement.y_type &&
                                author.levelName === levelElement.level_name &&
                                author.authorType === YEAR_LEVEL_AUTHOR_TYPE_MODULE.LEVEL,
                        );

                        return {
                            levelName: levelElement.level_name,
                            // termName: levelAuthor?.termName || null,
                            users:
                                levelAuthor?.users.map((user) => ({
                                    userId: user.userId._id,
                                    name: user.userId.name,
                                    modules: user.modules,
                                })) || [],
                        };
                    }),
                };
            }),
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const programList = async ({ programIds }) => {
    try {
        return await programSchema
            .find(
                {
                    _id: { $in: programIds },
                    isDeleted: false,
                },
                {
                    name: 1,
                    code: 1,
                    term: 1,
                },
            )
            .lean();
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const countGroups = (deliveryGroups) => {
    let groupCount = 0;
    deliveryGroups?.forEach((deliveryElement) => {
        deliveryElement?.deliveryTypes?.forEach((typeElement) => {
            typeElement?.selectedType?.forEach((selectedTypeElement) => {
                groupCount += selectedTypeElement?.noOfGroups || 0;
            });
        });
    });
    return groupCount;
};

const countGender = (students) => {
    let maleCount = 0;
    let femaleCount = 0;
    let ungroupedCount = 0;
    students?.forEach((studentElement) => {
        const gender = studentElement?.studentId?.gender;
        if (gender === MALE) maleCount++;
        else if (gender === FEMALE) femaleCount++;
        if (!studentElement?.isGrouped) ungroupedCount++;
    });
    return { maleCount, femaleCount, ungroupedCount };
};

const checkUngroupedCount = async ({ studentGroupData }) => {
    try {
        const yearWiseCount = {};
        const yearLevelWiseCount = {};
        studentGroupData?.forEach((groupElement) => {
            const year = groupElement.year;
            const level = groupElement.level;
            if (!yearWiseCount[year]) {
                yearWiseCount[year] = 0;
            }
            if (!yearLevelWiseCount[year]) {
                yearLevelWiseCount[year] = {};
            }
            if (level && !yearLevelWiseCount[year][level]) {
                yearLevelWiseCount[year][level] = 0;
            }
            const uniqueStudentIds = new Set();
            groupElement?.students?.forEach((studentElement) => {
                const studentId = String(studentElement.studentId._id);
                if (!studentElement.isGrouped && !uniqueStudentIds.has(studentId)) {
                    uniqueStudentIds.add(studentId);
                    yearWiseCount[year]++;
                    if (level) {
                        yearLevelWiseCount[year][level]++;
                    }
                }
            });
        });

        return {
            yearWiseCount,
            yearLevelWiseCount,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const programYearLevelList = async ({ institutionCalendarId, programId, term }) => {
    try {
        const programCalendarData = await programCalendarSchema
            .findOne(
                {
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    _program_id: convertToMongoObjectId(programId),
                },
                {
                    'level.term': 1,
                    'level.year': 1,
                    'level.level_no': 1,
                    'level.rotation': 1,
                    'level.rotation_count': 1,
                    'level.course._course_id': 1,
                    'level.course.courses_name': 1,
                    'level.course.courses_number': 1,
                    'level.rotation_course.rotation_count': 1,
                    'level.rotation_course.course._course_id': 1,
                    'level.rotation_course.course.courses_name': 1,
                    'level.rotation_course.course.courses_number': 1,
                },
            )
            .lean();
        if (!programCalendarData || !programCalendarData.level) {
            return [];
        }
        //filter the courseIds
        const courseIdsSet = new Set();
        programCalendarData?.level.forEach((levelElement) => {
            levelElement?.course?.forEach((courseElement) => {
                if (courseElement?._course_id) courseIdsSet.add(String(courseElement._course_id));
            });
            levelElement?.rotation_course?.forEach((rotationCourseElement) => {
                rotationCourseElement?.course?.forEach((courseElement) => {
                    if (courseElement?._course_id)
                        courseIdsSet.add(String(courseElement._course_id));
                });
            });
        });
        const courseIds = Array.from(courseIdsSet);
        //get version details in course
        const courseVersionData = await courseSchema
            .find(
                {
                    _id: {
                        $in: courseIds.map((courseIdElement) =>
                            convertToMongoObjectId(courseIdElement),
                        ),
                    },
                },
                {
                    versionName: 1,
                    versionedCourseIds: 1,
                    versioned: 1,
                },
            )
            .lean();
        const courseVersionMap = new Map(
            courseVersionData.map((courseElement) => [String(courseElement._id), courseElement]),
        );
        // Add version details to each course
        programCalendarData.level.forEach((levelElement) => {
            levelElement?.course?.forEach((courseElement) => {
                const versionDetails = courseVersionMap.get(String(courseElement._course_id));
                if (versionDetails) {
                    courseElement.versionNo = versionDetails.versionNo;
                    courseElement.versionName = versionDetails.versionName;
                    courseElement.versionedFrom = versionDetails.versionedFrom;
                    courseElement.versionedCourseIds = versionDetails.versionedCourseIds;
                }
            });
            levelElement?.rotation_course?.forEach((rotationCourse) => {
                rotationCourse?.course?.forEach((courseElement) => {
                    const versionDetails = courseVersionMap.get(String(courseElement._course_id));
                    if (versionDetails) {
                        courseElement.versionNo = versionDetails.versionNo;
                        courseElement.versionName = versionDetails.versionName;
                        courseElement.versionedFrom = versionDetails.versionedFrom;
                        courseElement.versionedCourseIds = versionDetails.versionedCourseIds;
                    }
                });
            });
        });

        // Filter levels by term if term is provided
        const filteredLevels = term
            ? programCalendarData.level.filter((levelElement) => levelElement.term === term)
            : programCalendarData.level;
        // Group levels by year
        const yearGroups = filteredLevels.reduce((acc, levelElement) => {
            if (!acc[levelElement.year]) {
                acc[levelElement.year] = [];
            }
            acc[levelElement.year].push(levelElement);
            return acc;
        }, {});
        //student group setting
        const studentGroupData = await studentGroupSettingSchema
            .find(
                {
                    institutionCalendarId: convertToMongoObjectId(institutionCalendarId),
                    programId: convertToMongoObjectId(programId),
                    term: { $regex: term, $options: 'i' },
                },
                {
                    'deliveryGroups.deliveryTypes.selectedType.noOfGroups': 1,
                    year: 1,
                    term: 1,
                    level: 1,
                    courseIds: 1,
                    selectedType: 1,
                    'students.studentId': 1,
                    'students.isGrouped': 1,
                },
            )
            .populate({ path: 'students.studentId', select: { gender: 1 } })
            .lean();

        //year wise ungrouped count
        const { yearWiseCount, yearLevelWiseCount } = await checkUngroupedCount({
            studentGroupData,
        });
        // Transform into the desired nested structure
        const yearLevelDetails = Object.entries(yearGroups).map(([year, levels]) => {
            const matchingGroups = studentGroupData.filter(
                (groupElement) => String(groupElement.year) === String(year),
            );
            let totalYearGroupCount = 0;
            let totalLevelGroupCount = 0;
            let yearMaleCount = 0;
            let yearFemaleCount = 0;
            let isImportedYear = false;
            matchingGroups.forEach((groupElement) => {
                if (groupElement?.selectedType === YEAR) {
                    totalYearGroupCount += countGroups(groupElement.deliveryGroups);
                    if (groupElement?.students?.length) {
                        const { maleCount, femaleCount, ungroupedCount } = countGender(
                            groupElement.students,
                        );
                        yearMaleCount += maleCount;
                        yearFemaleCount += femaleCount;
                        isImportedYear = true;
                    }
                }
            });
            const levelDetails = levels.map((levelElement) => {
                let levelOnlyGroupCount = 0;
                let courseGroupCount = 0;
                let levelMaleCount = 0;
                let levelFemaleCount = 0;
                let isImportedLevel = false;
                const courseDetails = levelElement?.course.map((course) => {
                    let courseGroup = 0;
                    let courseMale = 0;
                    let courseFemale = 0;
                    let courseUngroupedCount = 0;
                    let isImportedCourse = false;
                    const versionDetails = courseVersionMap.get(String(course._course_id));
                    if (versionDetails) {
                        Object.assign(course, {
                            versionName: versionDetails.versionName,
                            versionedCourseIds: versionDetails.versionedCourseIds,
                            versioned: versionDetails.versioned,
                        });
                    }
                    matchingGroups.forEach((groupElement) => {
                        if (
                            groupElement.selectedType === DS_COURSE_KEY &&
                            (!groupElement.level || groupElement.level === levelElement.level_no)
                        ) {
                            if (
                                groupElement.courseIds?.some(
                                    (courseElement) =>
                                        String(courseElement) === String(course._course_id),
                                )
                            ) {
                                courseGroup += countGroups(groupElement.deliveryGroups);
                                if (groupElement?.students?.length) {
                                    const { maleCount, femaleCount, ungroupedCount } = countGender(
                                        groupElement.students,
                                    );
                                    courseMale += maleCount;
                                    courseFemale += femaleCount;
                                    courseUngroupedCount = ungroupedCount;
                                    isImportedCourse = true;
                                }
                            }
                        }
                    });
                    courseGroupCount += courseGroup;

                    return {
                        ...course,
                        courseNumberOfGroup: courseGroup,
                        courseMaleCount: courseMale,
                        courseFemaleCount: courseFemale,
                        isImportedCourse,
                        courseUngroupedCount,
                    };
                });
                matchingGroups.forEach((groupElement) => {
                    if (
                        groupElement.selectedType === LEVEL &&
                        groupElement.level === levelElement.level_no
                    ) {
                        levelOnlyGroupCount += countGroups(groupElement.deliveryGroups);
                        if (groupElement.students?.length) {
                            const { maleCount, femaleCount, ungroupedCount } = countGender(
                                groupElement.students,
                            );
                            levelMaleCount += maleCount;
                            levelFemaleCount += femaleCount;
                            isImportedLevel = true;
                        }
                    }
                });
                const levelTotal = levelOnlyGroupCount;
                totalLevelGroupCount += levelTotal;
                return {
                    level_no: levelElement.level_no,
                    rotation: levelElement.rotation,
                    rotation_count: levelElement.rotation_count,
                    course: courseDetails,
                    rotation_course: levelElement.rotation_course || [],
                    levelNumberOfGroup: levelTotal,
                    isImportedLevel,
                    levelMaleCount,
                    levelFemaleCount,
                    levelUngroupedCount: yearLevelWiseCount[year]?.[levelElement.level_no] || 0,
                };
            });
            return {
                year,
                term: levels[0]?.term || '',
                level: levelDetails,
                yearNumberOfGroup: totalYearGroupCount,
                yearMaleCount,
                yearFemaleCount,
                yearUngroupedCount: yearWiseCount[year] || 0,
                isImportedYear,
            };
        });
        return yearLevelDetails;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getCourseBasedDelivery = async ({ courseIds }) => {
    try {
        const courseData = await courseSchema
            .find(
                {
                    _id: {
                        $in: courseIds.map((courseElement) =>
                            convertToMongoObjectId(courseElement),
                        ),
                    },
                    isActive: true,
                    isDeleted: false,
                },
                {
                    'credit_hours.credit_hours': 1,
                    'credit_hours.type_name': 1,
                    'credit_hours.delivery_type.delivery_type': 1,
                    'credit_hours.delivery_type.delivery_symbol': 1,
                },
            )
            .lean();
        const courseSessionOrderData = await courseSessionOrderSchema
            .find(
                {
                    _course_id: {
                        $in: courseIds.map((courseElement) =>
                            convertToMongoObjectId(courseElement),
                        ),
                    },
                    isActive: true,
                    isDeleted: false,
                },
                {
                    _id: 0,
                    _course_id: 1,
                    'session_flow_data.delivery_type': 1,
                    'session_flow_data.delivery_symbol': 1,
                },
            )
            .lean();
        const courseDeliveryMap = [];
        courseSessionOrderData.forEach((courseElement) => {
            const uniqueDeliveries = new Set();
            courseElement.session_flow_data.forEach((sessionFlowElement) => {
                uniqueDeliveries.add(
                    JSON.stringify({
                        delivery_type: sessionFlowElement.delivery_type,
                        delivery_symbol: sessionFlowElement.delivery_symbol,
                    }),
                );
            });
            courseDeliveryMap.push({
                courseId: courseElement._course_id,
                delivery: Array.from(uniqueDeliveries).map((deliveryElement) =>
                    JSON.parse(deliveryElement),
                ),
            });
        });
        const filteredCourseData = courseData?.map((courseElement) => {
            const courseDelivery = courseDeliveryMap.find(
                (deliveryElement) => String(deliveryElement.courseId) === String(courseElement._id),
            );
            const filteredCreditHours = courseElement?.credit_hours
                .filter((createdElement) => {
                    if (createdElement.credit_hours <= 0) return false;
                    if (!courseDelivery) return false;
                    return courseDelivery.delivery.some((deliveryElement) =>
                        createdElement.delivery_type.find(
                            (deliveryTypeElement) =>
                                deliveryElement.delivery_type ===
                                    deliveryTypeElement.delivery_type &&
                                deliveryElement.delivery_symbol ===
                                    deliveryTypeElement.delivery_symbol,
                        ),
                    );
                })
                .map((createdElement) => ({
                    type_name: createdElement.type_name,
                    delivery_type: createdElement.delivery_type,
                }));
            return {
                _id: courseElement._id,
                credit_hours: filteredCreditHours,
            };
        });
        return filteredCourseData;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getProgramCalenderCourseIds = async ({
    institutionCalendarId,
    programId,
    year,
    level,
    term,
    courseId,
}) => {
    try {
        const programCalenderData = await programCalenderSchema
            .findOne(
                {
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    _program_id: convertToMongoObjectId(programId),
                    'level.term': { $regex: term, $options: 'i' },
                    ...(year && { 'level.year': { $regex: year, $options: 'i' } }),
                    ...(level && { 'level.level_no': level }),
                    ...(courseId && {
                        'level.course._course_id': convertToMongoObjectId(courseId),
                    }),
                    isActive: true,
                    isDeleted: false,
                },
                {
                    'level.term': 1,
                    'level.year': 1,
                    'level.level_no': 1,
                    'level.course._course_id': 1,
                },
            )
            .lean();
        if (!programCalenderData) {
            return { statusCode: 404, message: 'NO_DELIVERY_TYPE' };
        }
        const courseIds = new Set();
        programCalenderData?.level?.forEach((levelElement) => {
            if (
                String(levelElement.term) === String(term) &&
                (!year || String(levelElement.year) === String(year)) &&
                (!level || String(levelElement.level_no) === String(level))
            ) {
                const matchedCourses = levelElement.course?.filter((courseElement) => {
                    return !courseId || String(courseElement._course_id) === String(courseId);
                });
                matchedCourses.forEach((courseElement) => {
                    if (!courseIds.has(String(courseElement._course_id)))
                        courseIds.add(String(courseElement._course_id));
                });
            }
        });
        return Array.from(courseIds);
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const checkStudentRegister = async ({ studentAcademicIds }) => {
    try {
        return await userSchema
            .find(
                {
                    user_id: { $in: studentAcademicIds },
                    isDeleted: false,
                },
                {
                    name: 1,
                    gender: 1,
                    user_id: 1,
                    user_type: 1,
                },
            )
            .lean();
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateStudentIds = ({ existingIds, newIds }) => {
    const existingSet = new Set(existingIds?.map(String) || []);
    const uniqueNewIds = newIds
        .map(String)
        .filter((newIdElement) => !existingSet.has(newIdElement));
    return [...existingSet, ...uniqueNewIds];
};

const findMatchingSessionSetting = ({
    settings,
    gender,
    groupName,
    deliveryType,
    deliverySymbol,
}) => {
    return settings.find(
        (settingElement) =>
            settingElement.gender === gender &&
            settingElement.session_setting.some(
                (sessionElement) =>
                    sessionElement.group_name === groupName &&
                    sessionElement.delivery_type === deliveryType &&
                    sessionElement.session_type === deliverySymbol,
            ),
    );
};

const createStudentStructure = (studentElement) => ({
    _student_id: studentElement.studentId._id,
    academic_no: studentElement.academicId,
    name: {
        first: studentElement.studentId.name.first || '',
        middle: studentElement.studentId.name.middle || '',
        last: studentElement.studentId.name.last || '',
        family: studentElement.studentId.name.family || '',
    },
    gender: studentElement.studentId.gender,
    mark: studentElement.mark,
    imported_on: studentElement.importedDate,
    imported_by: {
        first: studentElement.importedId?.name?.first || '',
        middle: studentElement.importedId?.name?.middle || '',
        last: studentElement.importedId?.name?.last || '',
        family: studentElement.importedId?.name?.family || '',
    },
    _imported_by: studentElement.importedId?._id,
    master_group_status: PUBLISHED,
});

const getGroupedStudentIds = (deliveryGroups) => {
    const groupedStudentIds = new Set();
    deliveryGroups?.forEach(({ deliveryTypes }) => {
        deliveryTypes?.forEach(({ selectedType }) => {
            selectedType?.forEach(({ groups }) => {
                groups?.forEach(({ studentIds }) => {
                    studentIds?.forEach((studentElement) =>
                        groupedStudentIds.add(String(studentElement)),
                    );
                });
            });
        });
    });
    return groupedStudentIds;
};

const updateCourseGroupStatus = (existingStatus, courseIds) => {
    const existingCourseIds = new Set(
        existingStatus.map((courseIdElement) => String(courseIdElement._course_id)),
    );
    const updatedStatus = [...existingStatus];
    courseIds.forEach((courseIdElement) => {
        if (!existingCourseIds.has(String(courseIdElement))) {
            updatedStatus.push({
                _course_id: courseIdElement,
                status: PUBLISHED,
            });
        }
    });

    return updatedStatus;
};

const groupedStudentStructure = ({ deliveryGroups, students, groupedStudents, courseIds }) => {
    const groupedStudentIds = getGroupedStudentIds(deliveryGroups);
    const existingStudentsMap = new Map(
        groupedStudents?.map((student) => [
            String(student._student_id),
            { ...student, course_group_status: [...(student.course_group_status || [])] },
        ]) || [],
    );
    const transformedStudents = students
        .filter((studentElement) => groupedStudentIds.has(String(studentElement.studentId._id)))
        .map((studentElement) => {
            const studentId = String(studentElement.studentId._id);
            const existingStudent = existingStudentsMap.get(studentId);
            const studentBase = createStudentStructure((student = studentElement));
            if (existingStudent) {
                return {
                    ...existingStudent,
                    ...studentBase,
                    course_group_status: updateCourseGroupStatus(
                        existingStudent.course_group_status,
                        courseIds,
                    ),
                };
            }
            return {
                ...studentBase,
                course_group_status: courseIds.map((courseId) => ({
                    _course_id: courseId,
                    status: PUBLISHED,
                })),
            };
        });
    return Array.from(
        new Map(
            transformedStudents.map((studentElement) => [
                String(studentElement._student_id),
                studentElement,
            ]),
        ).values(),
    );
};

const getGroupedStudentStructure = ({
    studentIds,
    importStudents,
    existingStudents,
    courseIdStrings = [],
}) => {
    const existingStudentIds = new Set(
        existingStudents?.map((studentElement) => String(studentElement?._student_id)) || [],
    );
    const importStudentsMap = new Map(
        importStudents?.map((studentElement) => [
            String(studentElement.studentId._id),
            studentElement,
        ]) || [],
    );
    const groupedStudents = [];
    studentIds?.forEach((studentIdElement) => {
        if (existingStudentIds.has(String(studentIdElement))) return;
        const importStudent = importStudentsMap.get(String(studentIdElement));
        if (importStudent) {
            const studentStructure = createStudentStructure(importStudent);
            groupedStudents.push({
                ...studentStructure,
                course_group_status: courseIdStrings.map((courseId) => ({
                    _course_id: convertToMongoObjectId(courseId),
                    status: PUBLISHED,
                })),
            });
        }
    });
    return groupedStudents;
};

const updatedStudentGroup = async ({
    studentGroupData,
    updatedGroup,
    isUpdate = false,
    courseIdStrings,
    level,
    importStudents,
}) => {
    const courseSettingBulkWrite = [];
    studentGroupData.groups?.forEach((groupElement) => {
        const levelWiseStudentSet = new Set();
        groupElement?.courses?.forEach((groupCourseElement) => {
            if (!courseIdStrings.includes(String(groupCourseElement._course_id))) return;

            const setting = groupCourseElement.setting ? [...groupCourseElement.setting] : [];

            updatedGroup?.forEach((deliveryGroupElement) => {
                const session_setting = [];
                let currentExistingSetting;

                deliveryGroupElement.deliveryTypes?.forEach((typeElement) => {
                    typeElement.selectedType?.forEach((selectedElement) => {
                        const deliveryGroupName = `${groupElement.group_name}-${groupCourseElement.course_no}-G-${selectedElement.deliverySymbol}`;
                        const existingSetting = findMatchingSessionSetting({
                            settings: setting,
                            gender: deliveryGroupElement.gender,
                            groupName: deliveryGroupName,
                            deliveryType: typeElement.typeName,
                            deliverySymbol: selectedElement.deliverySymbol,
                        });
                        currentExistingSetting = existingSetting;
                        if (existingSetting) {
                            const existingSessionSetting = existingSetting.session_setting.find(
                                (sessionElement) =>
                                    sessionElement.group_name === deliveryGroupName &&
                                    sessionElement.delivery_type === typeElement.typeName &&
                                    sessionElement.session_type === selectedElement.deliverySymbol,
                            );
                            if (existingSessionSetting) {
                                const existingGroupsMap = new Map(
                                    existingSessionSetting.groups.map((groupElement) => [
                                        groupElement.group_no,
                                        groupElement,
                                    ]),
                                );
                                const updatedGroups = selectedElement.groups.map((groupElement) => {
                                    const existingGroup =
                                        existingGroupsMap.get(groupElement.groupNo) || {};

                                    const existingStudentIds = (
                                        existingGroup._student_ids || []
                                    ).map(String);
                                    const newStudentIds = groupElement.studentIds.map(String);
                                    const finalStudentIds = isUpdate
                                        ? updateStudentIds({
                                              existingIds: existingStudentIds,
                                              newIds: newStudentIds,
                                          })
                                        : existingStudentIds.filter(
                                              (existingStudentIdElement) =>
                                                  !newStudentIds.includes(existingStudentIdElement),
                                          );
                                    finalStudentIds?.forEach((studentIdElement) =>
                                        levelWiseStudentSet.add(studentIdElement),
                                    );
                                    return {
                                        _id: existingGroup._id,
                                        group_no: groupElement.groupNo,
                                        group_name: `${deliveryGroupName}-${groupElement.groupNo}`,
                                        _student_ids: finalStudentIds,
                                    };
                                });
                                session_setting.push({
                                    ...existingSessionSetting,
                                    groups: updatedGroups,
                                });
                            }
                        } else {
                            const groups = selectedElement.groups.map((groupElement) => {
                                const studentIds = isUpdate
                                    ? groupElement.studentIds.map(String)
                                    : [];
                                studentIds?.forEach((studentIdElement) =>
                                    levelWiseStudentSet.add(studentIdElement),
                                );
                                return {
                                    group_no: groupElement.groupNo,
                                    group_name: `${groupElement.group_name}-${groupElement.groupNo}`,
                                    _student_ids: studentIds,
                                };
                            });
                            session_setting.push({
                                group_name: groupElement.group_name,
                                delivery_type: typeElement.typeName,
                                session_type: selectedElement.deliverySymbol,
                                no_of_group: selectedElement.noOfGroups,
                                groups,
                            });
                        }
                    });
                });
                const studentIds = Array.from(levelWiseStudentSet);
                const groupsStudent = getGroupedStudentStructure({
                    studentIds,
                    importStudents,
                    existingStudents: groupElement.students,
                    courseIdStrings,
                });
                const updatedStudents = [...(groupElement.students || []), ...groupsStudent];
                if (session_setting.length) {
                    const updateOperation = {
                        updateOne: {
                            filter: {
                                _id: convertToMongoObjectId(studentGroupData._id),
                                'groups.term': groupElement.term,
                                ...(level && { 'groups.level': level }),
                                'groups.courses._course_id': convertToMongoObjectId(
                                    groupCourseElement._course_id,
                                ),
                            },
                            update: {
                                $set: {
                                    ...(currentExistingSetting
                                        ? {
                                              'groups.$[groupTerm].courses.$[courseId].setting.$[setting].session_setting':
                                                  session_setting,
                                          }
                                        : {
                                              'groups.$[groupTerm].courses.$[courseId].setting': [
                                                  ...setting,
                                                  {
                                                      ungrouped: [],
                                                      gender: deliveryGroupElement.gender,
                                                      session_setting,
                                                  },
                                              ],
                                          }),
                                    'groups.$[groupTerm].students': updatedStudents,
                                },
                            },
                            arrayFilters: [
                                {
                                    'groupTerm.term': groupElement.term,
                                    ...(level && { 'groupTerm.level': level }),
                                },
                                {
                                    'courseId._course_id': convertToMongoObjectId(
                                        groupCourseElement._course_id,
                                    ),
                                },
                                ...(currentExistingSetting
                                    ? [{ 'setting.gender': deliveryGroupElement.gender }]
                                    : []),
                            ],
                        },
                    };
                    courseSettingBulkWrite.push(updateOperation);
                }
            });
        });
    });
    let updatedStudent;
    if (courseSettingBulkWrite.length) {
        updatedStudent = await studentGroupSchema.bulkWrite(courseSettingBulkWrite);
    }
    return updatedStudent;
};

const getOldStudentGroupingData = async ({
    institutionCalendarId,
    programId,
    year,
    term,
    level,
    courseIds,
}) => {
    try {
        return await studentGroupSchema
            .findOne(
                {
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    'master._program_id': convertToMongoObjectId(programId),
                    'master.year': { $regex: year, $options: 'i' },
                    ...(level && { 'groups.level': { $regex: level, $options: 'i' } }),
                    'groups.term': { $regex: term, $options: 'i' },
                    'groups.courses._course_id': {
                        $in: courseIds.map((courseElement) =>
                            convertToMongoObjectId(courseElement),
                        ),
                    },
                },
                {
                    'groups.level': 1,
                    'groups.group_name': 1,
                    'groups.term': 1,
                    'groups.courses._course_id': 1,
                    'groups.courses.course_no': 1,
                    'groups.courses.setting': 1,
                    'groups.students': 1,
                },
            )
            .lean();
    } catch (error) {
        console.error('Error in moveToStudentsUpdatedOldStudentGroup:', error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const moveToStudentsUpdatedOldStudentGroup = async ({
    institutionCalendarId,
    programId,
    year,
    term,
    level,
    courseIds,
    selectedType,
    deliveryGroups,
    updatedDeliveryGroups,
    importStudents,
}) => {
    try {
        const courseIdStrings = courseIds?.map(String);
        const removedStudentGroupedData = await getOldStudentGroupingData({
            institutionCalendarId,
            programId,
            year,
            term,
            level,
            courseIds,
        });
        const removedStudent = await updatedStudentGroup({
            studentGroupData: removedStudentGroupedData,
            updatedGroup: deliveryGroups,
            isUpdate: false,
            courseIdStrings,
            level,
        });
        logger.info('studentGroupMaster -> Removed Student Operation Result', removedStudent);
        const addStudentGroupedData = await getOldStudentGroupingData({
            institutionCalendarId,
            programId,
            year,
            term,
            level,
            courseIds,
        });
        const addedStudent = await updatedStudentGroup({
            studentGroupData: addStudentGroupedData,
            updatedGroup: updatedDeliveryGroups,
            isUpdate: true,
            courseIdStrings,
            level,
            importStudents,
        });
        logger.info('studentGroupMaster -> Added Student Operation Result', addedStudent);
    } catch (error) {
        console.error('Error in moveToStudentsUpdatedOldStudentGroup:', error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const mergeStudentsWithoutDuplicates = (existingStudents, newStudents) => {
    const studentMap = new Map();

    existingStudents.forEach((student) => {
        studentMap.set(String(student._id), student);
    });

    newStudents.forEach((student) => {
        studentMap.set(String(student._id), student);
    });

    return Array.from(studentMap.values());
};

const studentPushInExistingSchedule = async ({
    _institution_id,
    groupSettingId,
    studentIds = [],
    deliveryGroupDetails,
    attendanceStatus,
    isAllStudent = false,
    groupingType = AUTO,
}) => {
    try {
        const settingData = await studentGroupSettingSchema
            .findOne(
                {
                    _id: convertToMongoObjectId(groupSettingId),
                    isPublished: true,
                },
                {
                    _institution_id: 1,
                    institutionCalendarId: 1,
                    programId: 1,
                    selectedType: 1,
                    term: 1,
                    year: 1,
                    level: 1,
                    courseIds: 1,
                    isPublished: 1,
                    'deliveryGroups.gender': 1,
                    'deliveryGroups.deliveryTypes.selectedType.deliveryType': 1,
                    'deliveryGroups.deliveryTypes.selectedType.deliverySymbol': 1,
                    'deliveryGroups.deliveryTypes.selectedType.groups.groupNo': 1,
                    'deliveryGroups.deliveryTypes.selectedType.groups.studentIds': 1,
                },
            )
            .lean();
        if (!settingData) {
            return {};
        }
        const deliveryGroupData = settingData.deliveryGroups
            .flatMap((deliveryGroupElement) => {
                const matchingGroups = [];
                deliveryGroupElement.deliveryTypes.forEach((type) => {
                    type.selectedType.forEach((selected) => {
                        if (!deliveryGroupDetails || deliveryGroupDetails.length === 0) {
                            selected.groups.forEach((group) => {
                                if (group.studentIds && group.studentIds.length > 0) {
                                    if (isAllStudent && groupingType === AUTO) {
                                        group.studentIds.forEach((id) => {
                                            if (!studentIds.includes(String(id))) {
                                                studentIds.push(String(id));
                                            }
                                        });
                                    }
                                    matchingGroups.push({
                                        deliverySymbol: selected.deliverySymbol,
                                        groupNo: group.groupNo,
                                        gender: deliveryGroupElement.gender,
                                        studentIds: group.studentIds,
                                    });
                                }
                            });
                            return;
                        }

                        const matchingDetail = deliveryGroupDetails.find(
                            (detail) => detail.deliveryType === selected.deliveryType,
                        );
                        if (!matchingDetail) return;
                        const matchingGroup = selected.groups.find(
                            (group) => group.groupNo === matchingDetail.groupNo,
                        );

                        if (matchingGroup) {
                            if (isAllStudent && groupingType === AUTO) {
                                matchingGroup.studentIds.forEach((id) => {
                                    if (!studentIds.includes(String(id))) {
                                        studentIds.push(String(id));
                                    }
                                });
                                matchingGroups.push({
                                    deliverySymbol: selected.deliverySymbol,
                                    groupNo: matchingGroup.groupNo,
                                    gender: deliveryGroupElement.gender,
                                    studentIds: matchingGroup.studentIds,
                                });
                            } else {
                                const hasMatchingStudent = matchingGroup.studentIds.filter((id) =>
                                    studentIds.some(
                                        (studentId) => String(id) === String(studentId),
                                    ),
                                );

                                if (hasMatchingStudent.length > 0) {
                                    matchingGroups.push({
                                        deliverySymbol: selected.deliverySymbol,
                                        groupNo: matchingDetail.groupNo,
                                        gender: deliveryGroupElement.gender,
                                        studentIds: hasMatchingStudent,
                                    });
                                }
                            }
                        }
                    });
                });

                return matchingGroups;
            })
            .filter(Boolean);
        const { institutionCalendarId, programId, term, year, level, courseIds } = settingData;
        const existingStudentGroup = await studentGroupSchema
            .find(
                {
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    'master._program_id': convertToMongoObjectId(programId),
                },
                {
                    _id: 1,
                    'master.year': 1,
                    'groups.level': 1,
                    'groups.term': 1,
                    'groups.courses._course_id': 1,
                    'groups.courses.setting.gender': 1,
                    'groups.courses.setting.session_setting.session_type': 1,
                    'groups.courses.setting.session_setting.groups.group_no': 1,
                    'groups.courses.setting.session_setting.groups._id': 1,
                    // 'groups.students._student_id': 1,
                    // 'groups.students.name': 1,
                    // 'groups.students.academic_no': 1,
                },
            )
            .lean();

        const deliveryGroupFilterQuery = [];
        const otherDeliveryGroupFilterQuery = [];
        const studentStatusData = [];
        const studentPushData = [];
        const studentPullScheduleQuery = [];
        const studentIdSet = new Set(studentIds.map(String));
        const studentIdsWithDeliveryGroup = new Map();
        const studentData = await userSchema
            .find(
                {
                    _id: { $in: studentIds },
                },
                { name: 1, user_id: 1 },
            )
            .lean();

        const studentMap = new Map();
        studentData.forEach((studentElement) => {
            const studentId = String(studentElement._id);
            if (studentIdSet.has(studentId) && !studentMap.has(studentId)) {
                const studentData = {
                    _id: studentElement._id,
                    name: studentElement.name,
                    academic_no: studentElement.user_id,
                };
                studentMap.set(studentId, studentData);
                studentPushData.push(studentData);
                studentStatusData.push({
                    ...studentData,
                    status: attendanceStatus,
                });
            }
        });
        existingStudentGroup.forEach((studentGroupElement) => {
            studentGroupElement.groups.forEach((groupElement) => {
                // groupElement?.students?.forEach((studentElement) => {
                //     const studentId = String(studentElement._student_id);
                //     if (studentIdSet.has(studentId) && !studentMap.has(studentId)) {
                //         const studentData = {
                //             _id: studentElement._student_id,
                //             name: studentElement.name,
                //             academic_no: studentElement.academic_no,
                //         };
                //         studentMap.set(studentId, studentData);
                //         studentPushData.push(studentData);
                //         studentStatusData.push({
                //             ...studentData,
                //             status: attendanceStatus,
                //         });
                //     }
                // });

                groupElement.courses.forEach((courseElement) => {
                    const courseId = String(courseElement._course_id);
                    if (!courseIds.some((id) => String(id) === courseId)) return;

                    courseElement.setting.forEach((settingElement) => {
                        settingElement.session_setting.forEach((sessionElement) => {
                            sessionElement.groups.forEach((group) => {
                                const matchingDeliveryGroup = deliveryGroupData.find(
                                    (deliveryGroup) =>
                                        String(deliveryGroup.groupNo) === String(group.group_no) &&
                                        sessionElement.session_type ===
                                            deliveryGroup.deliverySymbol,
                                );

                                if (matchingDeliveryGroup) {
                                    const filterQuery = {
                                        _course_id: convertToMongoObjectId(
                                            courseElement._course_id,
                                        ),
                                        year_no: studentGroupElement.master.year,
                                        level_no: groupElement.level,
                                        term: groupElement.term,
                                    };
                                    deliveryGroupFilterQuery.push({
                                        ...filterQuery,
                                        'student_groups.session_group.session_group_id':
                                            convertToMongoObjectId(group._id),
                                    });
                                    otherDeliveryGroupFilterQuery.push({
                                        ...filterQuery,
                                        'student_groups.session_group.session_group_id': {
                                            $ne: convertToMongoObjectId(group._id),
                                        },
                                    });
                                    studentPullScheduleQuery.push({
                                        ...filterQuery,
                                        deliverySymbol: matchingDeliveryGroup.deliverySymbol,
                                    });
                                    studentIdsWithDeliveryGroup.set(
                                        String(group._id),
                                        matchingDeliveryGroup.studentIds.map(String),
                                    );
                                }
                            });
                        });
                    });
                });
            });
        });

        if (!studentPushData.length) {
            return {};
        }

        const combinedCourseScheduleData = await courseScheduleSchema
            .find(
                {
                    ...commonQuery,
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    _program_id: convertToMongoObjectId(programId),
                    $or: deliveryGroupFilterQuery,
                },
                {
                    _course_id: 1,
                    year_no: 1,
                    level_no: 1,
                    term: 1,
                    'students._id': 1,
                    status: 1,
                    'session._session_id': 1,
                    'student_groups.session_group.session_group_id': 1,
                },
            )
            .lean();
        const studentScheduleWithOutDeliveryGroup = attendanceStatus
            ? await courseScheduleSchema
                  .find(
                      {
                          ...commonQuery,
                          _institution_id: convertToMongoObjectId(_institution_id),
                          _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                          _program_id: convertToMongoObjectId(programId),
                          $or: otherDeliveryGroupFilterQuery,
                          'students._id': { $in: studentIds },
                          status: { $in: [COMPLETED, ONGOING] },
                      },
                      {
                          _course_id: 1,
                          year_no: 1,
                          level_no: 1,
                          term: 1,
                          'session._session_id': 1,
                          'students._id': 1,
                          'student_groups.session_group.session_group_id': 1,
                      },
                  )
                  .lean()
            : [];

        const courseScheduleBulkWrite = [];
        const courseScheduleMap = new Map();
        const completedScheduleMap = new Map();
        const bulkWriteMap = new Map();

        combinedCourseScheduleData.forEach((schedule) => {
            const key = `${schedule._course_id}-${schedule.year_no}-${schedule.level_no}-${schedule.term}`;
            if (!courseScheduleMap.has(key)) {
                courseScheduleMap.set(key, []);
            }
            courseScheduleMap.get(key).push(schedule);
        });

        studentScheduleWithOutDeliveryGroup.forEach((schedule) => {
            const key = `${schedule._course_id}-${schedule.year_no}-${schedule.level_no}-${schedule.term}`;
            if (!completedScheduleMap.has(key)) {
                completedScheduleMap.set(key, []);
            }
            completedScheduleMap.get(key).push(schedule);
        });

        studentPullScheduleQuery.forEach((schedulePullElement) => {
            // Pull students from PENDING/MISSED schedules for this specific delivery group
            courseScheduleBulkWrite.push({
                updateMany: {
                    filter: {
                        ...commonQuery,
                        _institution_id: convertToMongoObjectId(_institution_id),
                        _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                        _program_id: convertToMongoObjectId(programId),
                        term: schedulePullElement.term,
                        year_no: schedulePullElement.year_no,
                        level_no: schedulePullElement.level_no,
                        _course_id: convertToMongoObjectId(schedulePullElement._course_id),
                        type: REGULAR,
                        'session.delivery_symbol': schedulePullElement.deliverySymbol,
                        'students._id': { $in: studentIds },
                        status: { $in: [PENDING, MISSED] },
                    },
                    update: {
                        $pull: {
                            students: { _id: { $in: studentIds } },
                        },
                    },
                },
            });
        });

        deliveryGroupFilterQuery.forEach((deliveryGroupElement) => {
            const key = `${deliveryGroupElement._course_id}-${deliveryGroupElement.year_no}-${deliveryGroupElement.level_no}-${deliveryGroupElement.term}`;
            const courseScheduleData = courseScheduleMap.get(key) || [];
            const courseCompletedScheduleData = completedScheduleMap.get(key) || [];

            courseScheduleData.forEach((courseScheduleElement) => {
                const { status, students, _id, session, student_groups } = courseScheduleElement;
                const existingStudentIds = new Set(students.map((student) => String(student._id)));

                const allDeliveryGroupStudentIds = [];
                student_groups?.forEach((studentGroup) => {
                    studentGroup.session_group?.forEach((sessionGroup) => {
                        const deliveryGroupStudentIds = studentIdsWithDeliveryGroup.get(
                            String(sessionGroup.session_group_id),
                        );
                        if (deliveryGroupStudentIds) {
                            allDeliveryGroupStudentIds.push(...deliveryGroupStudentIds);
                        }
                    });
                });

                if ((status === PENDING || status === MISSED) && studentPushData.length) {
                    const filteredStudentPushData = studentPushData.filter((student) =>
                        allDeliveryGroupStudentIds.includes(String(student._id)),
                    );

                    const newStudentsWithoutStatus = filteredStudentPushData.filter(
                        ({ _id }) => !existingStudentIds.has(String(_id)),
                    );

                    if (newStudentsWithoutStatus.length) {
                        const scheduleId = String(_id);
                        if (bulkWriteMap.has(scheduleId)) {
                            const existingOperation = bulkWriteMap.get(scheduleId);
                            const mergedStudents = mergeStudentsWithoutDuplicates(
                                existingOperation.updateMany.update.$push.students,
                                newStudentsWithoutStatus,
                            );
                            existingOperation.updateMany.update.$push.students = mergedStudents;
                        } else {
                            const operation = {
                                updateMany: {
                                    filter: { _id },
                                    update: { $push: { students: newStudentsWithoutStatus } },
                                },
                            };
                            bulkWriteMap.set(scheduleId, operation);
                        }
                    }
                } else if (
                    attendanceStatus &&
                    (status === COMPLETED || status === ONGOING) &&
                    studentStatusData.length
                ) {
                    const sessionId = session._session_id.toString();
                    const existingSession = courseCompletedScheduleData.find(
                        (schedule) => schedule.session._session_id.toString() === sessionId,
                    );

                    const filteredStudentStatusData = studentStatusData.filter((student) =>
                        allDeliveryGroupStudentIds.includes(String(student._id)),
                    );

                    const newStudentsWithStatus = filteredStudentStatusData.filter(({ _id }) => {
                        const studentId = String(_id);
                        return (
                            !existingStudentIds.has(studentId) &&
                            (!existingSession ||
                                !existingSession.students.some(
                                    (student) => String(student._id) === studentId,
                                ))
                        );
                    });

                    if (newStudentsWithStatus.length) {
                        const scheduleId = String(_id);
                        if (bulkWriteMap.has(scheduleId)) {
                            const existingOperation = bulkWriteMap.get(scheduleId);
                            const mergedStudents = mergeStudentsWithoutDuplicates(
                                existingOperation.updateMany.update.$push.students,
                                newStudentsWithStatus,
                            );
                            existingOperation.updateMany.update.$push.students = mergedStudents;
                        } else {
                            const operation = {
                                updateMany: {
                                    filter: { _id },
                                    update: { $push: { students: newStudentsWithStatus } },
                                },
                            };
                            bulkWriteMap.set(scheduleId, operation);
                        }
                    }
                }
            });
        });

        courseScheduleBulkWrite.push(...bulkWriteMap.values());

        if (courseScheduleBulkWrite.length) {
            console.log(
                await courseScheduleSchema.bulkWrite(courseScheduleBulkWrite),
                'Student Schedule Push Service',
            );
        }

        return {
            courseScheduleBulkWrite,
            studentStatusData,
            combinedCourseScheduleData,
            studentScheduleWithOutDeliveryGroup,
            deliveryGroupFilterQuery,
            deliveryGroupData,
            settingData,
            existingStudentGroup,
            deliveryGroupDetails,
            studentIds,
        };
    } catch (error) {
        console.log(error);
        throw error;
    }
};

const updateOldStudentGroupData = async ({
    _institution_id,
    term,
    programId,
    level,
    courseIds,
    institutionCalendarId,
    year,
    deliveryGroups,
    students,
    groupSettingId,
    isPublished,
}) => {
    try {
        const studentGroupData = await studentGroupSchema
            .findOne(
                {
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    'master._program_id': convertToMongoObjectId(programId),
                    'master.year': { $regex: year, $options: 'i' },
                    ...(level && { 'groups.level': { $regex: level, $options: 'i' } }),
                    'groups.term': { $regex: term, $options: 'i' },
                    'groups.courses._course_id': {
                        $in: courseIds.map((courseElement) =>
                            convertToMongoObjectId(courseElement),
                        ),
                    },
                },
                {
                    'groups.level': 1,
                    'groups.term': 1,
                    'groups.group_name': 1,
                    'groups.courses._course_id': 1,
                    'groups.courses.course_no': 1,
                    'groups.courses.setting': 1,
                    'groups.students': 1,
                },
            )
            .lean();
        const courseSettingBulkWrite = [];
        const courseIdStrings = courseIds?.map(String);
        const deliveryGroupDetails = [];
        studentGroupData?.groups?.forEach((groupElement) => {
            const groupStudentData = groupedStudentStructure({
                deliveryGroups,
                students,
                groupedStudents: groupElement?.students || [],
                courseIds,
            });
            let termLevelUpdate = false;
            groupElement?.courses.forEach((groupCourseElement) => {
                if (!courseIdStrings?.includes(String(groupCourseElement._course_id))) return;
                const setting = groupCourseElement?.setting ? [...groupCourseElement.setting] : [];
                deliveryGroups?.forEach((deliveryGroupElement) => {
                    const session_setting = [];
                    let currentExistingSetting;
                    deliveryGroupElement?.deliveryTypes?.forEach((typeElement) => {
                        typeElement.selectedType?.forEach((selectedElement) => {
                            selectedElement.groups.forEach((groupElement) => {
                                if (groupElement.studentIds?.length > 0) {
                                    deliveryGroupDetails.push({
                                        deliveryType: selectedElement.deliveryType,
                                        groupNo: groupElement.groupNo,
                                    });
                                }
                            });
                            const existingSetting = findMatchingSessionSetting({
                                settings: setting,
                                gender: deliveryGroupElement.gender,
                                groupName: `${groupElement.group_name}-${groupCourseElement.course_no}-G-${selectedElement.deliverySymbol}`,
                                deliveryType: typeElement.typeName,
                                deliverySymbol: selectedElement.deliverySymbol,
                            });
                            currentExistingSetting = existingSetting;
                            if (existingSetting) {
                                const existingSessionSetting = existingSetting.session_setting.find(
                                    (session) =>
                                        session.group_name ===
                                            `${groupElement.group_name}-${groupCourseElement.course_no}-G-${selectedElement.deliverySymbol}` &&
                                        session.delivery_type === typeElement.typeName &&
                                        session.session_type === selectedElement.deliverySymbol,
                                );
                                if (existingSessionSetting) {
                                    const updatedGroups = selectedElement.groups.map(
                                        (group, index) => {
                                            const existingGroup =
                                                existingSessionSetting.groups[index];
                                            return {
                                                _id: existingGroup?._id,
                                                group_no: group.groupNo,
                                                group_name: `${groupElement.group_name}-${groupCourseElement.course_no}-G-${selectedElement.deliverySymbol}-${group.groupNo}`,
                                                _student_ids: updateStudentIds({
                                                    existingIds: existingGroup?._student_ids,
                                                    newIds: group.studentIds,
                                                }),
                                            };
                                        },
                                    );
                                    session_setting.push({
                                        ...existingSessionSetting,
                                        groups: updatedGroups,
                                    });
                                }
                            } else {
                                const groups = selectedElement.groups.map((group) => ({
                                    group_no: group.groupNo,
                                    group_name: `${groupElement.group_name}-${groupCourseElement.course_no}-G-${selectedElement.deliverySymbol}-${group.groupNo}`,
                                    _student_ids: group.studentIds,
                                }));
                                session_setting.push({
                                    group_name: `${groupElement.group_name}-${groupCourseElement.course_no}-G-${selectedElement.deliverySymbol}`,
                                    delivery_type: typeElement.typeName,
                                    session_type: selectedElement.deliverySymbol,
                                    no_of_group: selectedElement.noOfGroups,
                                    groups,
                                });
                            }
                        });
                    });
                    if (session_setting?.length) {
                        const updateOperation = {
                            updateOne: {
                                filter: {
                                    _id: convertToMongoObjectId(studentGroupData._id),
                                    'groups.term': groupElement.term,
                                    'groups.level': groupElement.level,
                                    'groups.courses._course_id': convertToMongoObjectId(
                                        groupCourseElement._course_id,
                                    ),
                                },
                                update: {
                                    $set: {
                                        ...(currentExistingSetting
                                            ? {
                                                  'groups.$[groupTerm].courses.$[courseId].setting.$[setting].session_setting':
                                                      session_setting,
                                              }
                                            : {
                                                  'groups.$[groupTerm].courses.$[courseId].setting':
                                                      [
                                                          ...setting,
                                                          {
                                                              ungrouped: [],
                                                              gender: deliveryGroupElement.gender,
                                                              session_setting,
                                                          },
                                                      ],
                                              }),
                                    },
                                },
                                arrayFilters: [
                                    {
                                        'groupTerm.term': groupElement.term,
                                        'groupTerm.level': groupElement.level,
                                    },
                                    {
                                        'courseId._course_id': convertToMongoObjectId(
                                            groupCourseElement._course_id,
                                        ),
                                    },
                                    ...(currentExistingSetting
                                        ? [{ 'setting.gender': deliveryGroupElement.gender }]
                                        : []),
                                ],
                            },
                        };
                        courseSettingBulkWrite.push(updateOperation);
                        termLevelUpdate = true;
                    }
                });
            });
            if (groupStudentData?.length && termLevelUpdate) {
                courseSettingBulkWrite.push({
                    updateOne: {
                        filter: {
                            _id: convertToMongoObjectId(studentGroupData._id),
                            'groups.term': groupElement.term,
                            'groups.level': groupElement.level,
                        },
                        update: { $set: { 'groups.$[groupTerm].students': groupStudentData } },
                        arrayFilters: [
                            {
                                'groupTerm.term': groupElement.term,
                                'groupTerm.level': groupElement.level,
                            },
                        ],
                    },
                });
            }
        });
        if (courseSettingBulkWrite.length) {
            const updatedStudentGroup = await studentGroupSchema.bulkWrite(courseSettingBulkWrite);
            await studentGroupSettingSchema.updateOne(
                { _id: convertToMongoObjectId(groupSettingId) },
                { $set: { isPublished: true } },
            );
            logger.info('studentGroupMaster -> publishedStudentGrouping -> End');
            if (updatedStudentGroup?.modifiedCount) {
                if (!isPublished) {
                    await studentPushInExistingSchedule({
                        _institution_id,
                        groupSettingId,
                        deliveryGroupDetails,
                    });
                }
                return { statusCode: 200, message: 'GROUP_SUCCESSFULLY_PUBLISHED' };
            }
            return { statusCode: 404, message: 'UNABLE_TO_UPDATE' };
        }
        logger.info('studentGroupMaster -> publishedStudentGrouping -> End');
        return { statusCode: 200, message: 'GROUP_SUCCESSFULLY_PUBLISHED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const notRegisterUserDetails = async ({ studentRegister, importedStudents }) => {
    try {
        const notRegisters = [];
        importedStudents.forEach((studentElement) => {
            if (
                !studentRegister.some(
                    (registerElement) => registerElement.user_id === studentElement.academicId,
                )
            ) {
                notRegisters.push(studentElement);
            }
        });
        return { statusCode: 404, message: 'STUDENTS_ARE_NOT_REGISTERED', data: notRegisters };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const alreadyInStudentGroup = async ({ groupSettingId, academicId }) => {
    return await studentGroupSettingSchema
        .findOne(
            {
                _id: convertToMongoObjectId(groupSettingId),
                'students.academicId': { $regex: `^${academicId}$`, $options: 'i' },
            },
            {
                _id: 1,
            },
        )
        .lean();
};

const orderWiseListStudentData = async ({ groupingMethod, students }) => {
    try {
        switch (groupingMethod) {
            case ALPHABETICAL:
                students.sort((a, b) => {
                    const nameA = a.studentId?.name?.first?.toLowerCase() || '';
                    const nameB = b.studentId?.name?.first?.toLowerCase() || '';
                    return nameA.localeCompare(nameB);
                });
                break;
            case ACADEMIC:
                students.sort((a, b) => {
                    const academicIdA = a.academicId?.toLowerCase() || '';
                    const academicIdB = b.academicId?.toLowerCase() || '';
                    return academicIdA.localeCompare(academicIdB);
                });
                break;
            case MARK:
                students.sort((a, b) => parseFloat(b.mark) - parseFloat(a.mark));
                break;
            case CLUSTER:
                students.sort((a, b) => parseFloat(a.mark) - parseFloat(b.mark));
                break;
            default:
                break;
        }
        const orderStudentIds = [];
        students?.forEach((studentElement) => {
            orderStudentIds.push(String(studentElement?.studentId?._id));
        });
        return orderStudentIds;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getGenderCount = (filterGroupedStudentList) => {
    let maleCount = 0;
    let femaleCount = 0;
    filterGroupedStudentList.forEach((studentElement) => {
        const gender = studentElement.studentId?.gender;
        if (gender === FEMALE) femaleCount++;
        else if (gender === MALE) maleCount++;
    });
    const mixedCount = maleCount + femaleCount;
    return { maleCount, femaleCount, mixedCount };
};

const groupWiseListStudent = async ({
    students,
    deliveryGroups,
    searchKey,
    genderType,
    groupType,
    query,
    studentCount,
    moveTo,
    studentGender,
}) => {
    try {
        const groupedStudents = [];
        const ungroupedStudents = [];
        students.forEach((studentElement) => {
            const deliverySetting = [];
            const genderGroup = deliveryGroups.find((groupElement) => {
                if (!genderType || genderType === MIXED || genderType === BOTH) {
                    return true;
                }
                return groupElement.gender === studentElement.studentId.gender;
            });
            let totalDeliveryTypes = 0;
            let assignedDeliveryTypes = 0;
            if (genderGroup) {
                genderGroup.deliveryTypes.forEach((deliveryTypeElement) => {
                    deliveryTypeElement.selectedType.forEach((selectedTypeElement) => {
                        totalDeliveryTypes++;
                        const studentGroup = selectedTypeElement.groups.find((groupElement) =>
                            groupElement.studentIds.some(
                                (studentIdElement) =>
                                    String(studentIdElement) ===
                                    String(studentElement.studentId._id),
                            ),
                        );
                        if (studentGroup) {
                            assignedDeliveryTypes++;
                        }
                        deliverySetting.push({
                            deliverySymbol: selectedTypeElement.deliverySymbol,
                            groupNo: studentGroup ? studentGroup.groupNo : null,
                        });
                    });
                });
            }
            const studentWithSettings = {
                ...studentElement,
                deliverySetting,
            };
            if (totalDeliveryTypes > 0 && assignedDeliveryTypes === totalDeliveryTypes) {
                groupedStudents.push(studentWithSettings);
            } else {
                ungroupedStudents.push(studentWithSettings);
            }
        });
        if (moveTo) {
            return groupedStudents;
        }
        const unGroupedCount = ungroupedStudents.length;
        const groupedCount = groupedStudents.length;
        const { maleCount, femaleCount, mixedCount } = getGenderCount(
            groupType === GROUPED ? groupedStudents : ungroupedStudents,
        );
        if (studentCount) {
            return {
                statusCode: 200,
                message: 'DATA_RETRIEVED',
                data: {
                    unGroupedCount,
                    groupedCount,
                    maleCount,
                    femaleCount,
                    mixedCount,
                },
            };
        }
        const filterGroupedStudentList =
            groupType === GROUPED
                ? groupedStudents
                : groupType === UNGROUPED
                ? ungroupedStudents
                : [...groupedStudents, ...ungroupedStudents];
        const regex = searchKey ? new RegExp(searchKey, 'i') : null;
        const studentList = [];

        filterGroupedStudentList.forEach((studentElement) => {
            const gender = studentElement.studentId.gender;
            const isGenderMatch = studentGender
                ? studentGender === gender
                : !genderType || genderType === MIXED || gender === genderType;
            const isSearchMatch =
                !regex ||
                regex.test(studentElement.academicId) ||
                regex.test(studentElement.studentId.name?.first) ||
                regex.test(studentElement.studentId.name?.last) ||
                regex.test(studentElement.studentId.name?.family) ||
                regex.test(studentElement.studentId.name?.middle);
            if (isGenderMatch && isSearchMatch) {
                studentList.push({
                    userId: studentElement.studentId._id,
                    name: studentElement.studentId.name,
                    gender,
                    academicId: studentElement.academicId,
                    mark: studentElement.mark,
                    importedBy: studentElement.importedId?.name,
                    importedAt: studentElement.importedDate,
                    groups: studentElement.deliverySetting,
                });
            }
        });
        const pagination = getPaginationValues(query);
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: {
                totalCount: studentList.length,
                unGroupedCount,
                groupedCount,
                maleCount,
                femaleCount,
                mixedCount,
                studentList: studentList.slice(pagination.skip, pagination.skip + pagination.limit),
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updatedGroupingStudentStatus = async ({
    groupSettingId,
    deliveryGroups,
    groupedStudents,
}) => {
    try {
        groupedStudents.forEach((studentElement) => {
            const genderGroup = deliveryGroups.find((groupElement) => {
                if (groupElement.gender === MIXED || groupElement.gender === BOTH) {
                    return true;
                }
                return groupElement.gender === studentElement.studentId.gender;
            });
            let totalDeliveryTypes = 0;
            let assignedDeliveryTypes = 0;
            if (genderGroup) {
                genderGroup.deliveryTypes.forEach((deliveryTypeElement) => {
                    deliveryTypeElement.selectedType.forEach((selectedTypeElement) => {
                        totalDeliveryTypes++;
                        const studentGroup = selectedTypeElement.groups.find((groupElement) =>
                            groupElement.studentIds.some(
                                (studentIdElement) =>
                                    String(studentIdElement) ===
                                    String(studentElement.studentId._id),
                            ),
                        );
                        if (studentGroup) {
                            assignedDeliveryTypes++;
                        }
                    });
                });
            }
            studentElement.isGrouped = !!(
                totalDeliveryTypes > 0 && assignedDeliveryTypes === totalDeliveryTypes
            );
        });
        const students = groupedStudents.map((studentElement) => ({
            ...studentElement,
            studentId: studentElement.studentId._id,
        }));
        await studentGroupSettingSchema.updateOne(
            { _id: convertToMongoObjectId(groupSettingId) },
            {
                $set: {
                    students,
                },
            },
        );
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updatedStudentGrouping = async ({
    _institution_id,
    groupSettingId,
    genderType,
    studentIds = [],
    deliveryTypes,
    groupingMethod,
    groupingType,
    selectedDeliveryType,
    isAllStudent,
    removedStudentIds,
    moveTo = false,
    studentAttendanceStatus,
}) => {
    try {
        const studentGroupingSettingData = await studentGroupSettingSchema
            .findOne(
                {
                    _id: convertToMongoObjectId(groupSettingId),
                },
                {
                    institutionCalendarId: 1,
                    students: 1,
                    deliveryGroups: 1,
                    term: 1,
                    programId: 1,
                    selectedType: 1,
                    year: 1,
                    courseIds: 1,
                    isPublished: 1,
                    level: 1,
                },
            )
            .populate({ path: 'students.studentId', select: { name: 1, gender: 1 } })
            .lean();
        if (!studentGroupingSettingData?.students?.length) {
            return { statusCode: 404, message: 'NO_DATA_FOUND' };
        }
        let groupedStudentIds;
        const allStudents = studentGroupingSettingData.students;
        let filterStudent = [];
        if (isAllStudent) {
            filterStudent = moveTo
                ? await groupWiseListStudent({
                      students: studentGroupingSettingData.students,
                      deliveryGroups: studentGroupingSettingData.deliveryGroups,
                      genderType,
                      moveTo,
                  })
                : allStudents;
        } else {
            filterStudent = studentGroupingSettingData.students.filter((studentElement) =>
                studentIds.includes(String(studentElement?.studentId?._id)),
            );
        }
        // removed the student
        if (removedStudentIds?.length) {
            filterStudent = filterStudent.filter(
                (student) => !removedStudentIds.includes(String(student.studentId._id)),
            );
        }
        let orderStudentIds = [];
        if (groupingType === AUTO) {
            orderStudentIds = await orderWiseListStudentData({
                groupingMethod,
                students: filterStudent,
            });
        } else {
            filterStudent?.forEach((studentElement) => {
                if (studentElement?.studentId?._id) {
                    orderStudentIds.push(String(studentElement.studentId._id));
                }
            });
        }
        const deliveryGroups = studentGroupingSettingData?.deliveryGroups;
        const updatedDeliveryGroups = JSON.parse(JSON.stringify(deliveryGroups));
        if (moveTo) {
            const movedStudentId = filterStudent.map((studentElement) =>
                String(studentElement.studentId._id),
            );
            updatedDeliveryGroups?.forEach((groupElement) => {
                if (groupElement.gender !== genderType) return;
                groupElement.deliveryTypes?.forEach((typeElement) => {
                    typeElement.selectedType?.forEach((selectedTypeElement) => {
                        const matchingDeliveryType = selectedDeliveryType?.find(
                            (selectedElement) =>
                                selectedElement.deliveryType === selectedTypeElement.deliveryType,
                        );
                        if (matchingDeliveryType) {
                            selectedTypeElement.groups?.forEach((groupElement) => {
                                groupElement.studentIds = groupElement.studentIds.filter(
                                    (studentIdElement) =>
                                        !movedStudentId.includes(String(studentIdElement)),
                                );
                            });
                            let targetGroup = selectedTypeElement.groups.find(
                                (groupElement) =>
                                    groupElement.groupNo === matchingDeliveryType.groupNo,
                            );
                            if (!targetGroup) {
                                targetGroup = {
                                    groupNo: matchingDeliveryType.groupNo,
                                    studentIds: [],
                                    _id: convertToMongoObjectId(),
                                };
                                selectedTypeElement.groups.push(targetGroup);
                            }
                            const existingStudentIds = new Set(targetGroup.studentIds.map(String));
                            movedStudentId.forEach((studentIdElement) => {
                                if (!existingStudentIds.has(studentIdElement)) {
                                    targetGroup.studentIds.push(studentIdElement);
                                }
                            });
                        }
                    });
                });
            });
            if (studentGroupingSettingData?.isPublished) {
                const {
                    institutionCalendarId,
                    programId,
                    year,
                    term,
                    level,
                    courseIds,
                    selectedType,
                    deliveryGroups,
                } = studentGroupingSettingData;
                await moveToStudentsUpdatedOldStudentGroup({
                    institutionCalendarId,
                    programId,
                    year,
                    term,
                    level,
                    courseIds,
                    selectedType,
                    deliveryGroups,
                    updatedDeliveryGroups,
                });
            }
            if (updatedDeliveryGroups?.length) {
                await studentGroupSettingSchema.updateOne(
                    {
                        _id: convertToMongoObjectId(groupSettingId),
                    },
                    {
                        $set: {
                            deliveryGroups: updatedDeliveryGroups,
                        },
                    },
                );
            }
            await updatedGroupingStudentStatus({
                groupSettingId,
                deliveryGroups: updatedDeliveryGroups,
                groupedStudents: allStudents,
            });
            await studentPushInExistingSchedule({
                _institution_id,
                groupSettingId,
                studentIds,
                deliveryGroupDetails: selectedDeliveryType,
                attendanceStatus: studentAttendanceStatus,
                isAllStudent,
            });
            await studentGroupRedisKeyUpdates({
                programId: studentGroupingSettingData.programId,
                year: studentGroupingSettingData.year,
                level: studentGroupingSettingData.level,
                term: studentGroupingSettingData.term,
                institutionCalendarId: studentGroupingSettingData.institutionCalendarId,
            });
            return {
                statusCode: 200,
                message: 'STUDENTS_MOVED_SUCCESSFULLY',
            };
        }
        if (groupingType === AUTO) {
            updatedDeliveryGroups?.forEach((groupElement) => {
                if (groupElement.gender !== genderType) return;
                groupElement.deliveryTypes?.forEach((typeElement) => {
                    typeElement.selectedType?.forEach((selectedTypeElement) => {
                        if (!deliveryTypes.includes(selectedTypeElement.deliveryType)) return;
                        const noOfGroups = selectedTypeElement.noOfGroups;
                        const groupSize = Math.ceil(orderStudentIds.length / noOfGroups);
                        const existingGroups = Array.isArray(selectedTypeElement.groups)
                            ? selectedTypeElement.groups
                            : [];
                        const groupMap = new Map();
                        existingGroups.forEach((groupElement) => {
                            groupMap.set(groupElement.groupNo, {
                                ...groupElement,
                                studentIds: [...new Set(groupElement.studentIds.map(String))],
                            });
                        });
                        for (let i = 1; i <= noOfGroups; i++) {
                            if (!groupMap.has(i)) {
                                groupMap.set(i, { groupNo: i, studentIds: [] });
                            }
                        }
                        const groups = Array.from(groupMap.values());
                        const allAssignedStudents = new Set();
                        groups.forEach((group) => {
                            group.studentIds.forEach((studentIdElement) =>
                                allAssignedStudents.add(studentIdElement),
                            );
                        });
                        if (groupingMethod === CLUSTER) {
                            let groupIndex = 0;
                            orderStudentIds.forEach((studentId) => {
                                const strStudentId = String(studentId);
                                if (allAssignedStudents.has(strStudentId)) return;
                                const group = groups[groupIndex];
                                group.studentIds.push(strStudentId);
                                allAssignedStudents.add(strStudentId);
                                groupIndex = (groupIndex + 1) % noOfGroups;
                            });
                            selectedTypeElement.groups = groups;
                        } else {
                            const totalStudents = orderStudentIds.length;
                            const noOfGroups = selectedTypeElement.noOfGroups;
                            const groups = selectedTypeElement.groups || [];
                            for (let i = 0; i < noOfGroups; i++) {
                                groups[i] = groups[i] || { groupNo: i + 1, studentIds: [] };
                            }
                            const baseGroupSize = Math.floor(totalStudents / noOfGroups);
                            let extraStudents = totalStudents % noOfGroups;
                            let currentIndex = 0;
                            for (let i = 0; i < noOfGroups; i++) {
                                const groupSize = baseGroupSize + (extraStudents > 0 ? 1 : 0);
                                if (extraStudents > 0) extraStudents--;
                                const groupStudents = orderStudentIds.slice(
                                    currentIndex,
                                    currentIndex + groupSize,
                                );
                                groups[i].studentIds.push(...groupStudents);
                                currentIndex += groupSize;
                            }
                            selectedTypeElement.groups = groups;
                        }
                    });
                });
            });
        } else {
            if (selectedDeliveryType?.length) {
                const deliveryTypes = selectedDeliveryType.map(
                    (selectedDeliveryElement) => selectedDeliveryElement.deliveryType,
                );
                updatedDeliveryGroups?.forEach((groupElement) => {
                    if (groupElement.gender !== genderType) return;
                    groupElement.deliveryTypes?.forEach((typeElement) => {
                        typeElement.selectedType?.forEach((selectedTypeElement) => {
                            if (!deliveryTypes.includes(selectedTypeElement.deliveryType)) return;
                            const noOfGroups = selectedTypeElement.noOfGroups;
                            const groups = selectedTypeElement?.groups || [];
                            selectedDeliveryType.forEach((selectedDeliveryElement) => {
                                if (
                                    selectedDeliveryElement.deliveryType ===
                                    selectedTypeElement.deliveryType
                                ) {
                                    const groupToUpdate = groups.find(
                                        (groupElement) =>
                                            Number(groupElement.groupNo) ===
                                            Number(selectedDeliveryElement.groupNo),
                                    );

                                    if (groupToUpdate && Array.isArray(orderStudentIds)) {
                                        orderStudentIds.forEach((studentIdElement) => {
                                            const alreadyInAnyGroup = groups.some((groupElement) =>
                                                groupElement.studentIds.includes(
                                                    String(studentIdElement),
                                                ),
                                            );
                                            if (!alreadyInAnyGroup) {
                                                groupToUpdate.studentIds.push(
                                                    String(studentIdElement),
                                                );
                                                studentIds.push(String(studentIdElement));
                                            }
                                        });
                                    }
                                }
                            });
                            selectedTypeElement.groups = groups;
                        });
                    });
                });
            }
        }
        // is grouped student
        const updatedStudents = allStudents?.map((studentElement) => {
            const isGrouped = orderStudentIds.includes(String(studentElement?.studentId?._id));
            return {
                ...studentElement,
                isGrouped,
            };
        });
        console.log('updatedStudents', updatedStudents);
        console.log('deliveryGroups', deliveryGroups);
        if (deliveryGroups?.length)
            await studentGroupSettingSchema.updateOne(
                {
                    _id: convertToMongoObjectId(groupSettingId),
                },
                {
                    $set: {
                        ...(updatedDeliveryGroups &&
                            updatedDeliveryGroups.length && {
                                deliveryGroups: updatedDeliveryGroups,
                            }),
                        students: updatedStudents,
                    },
                },
            );

        if (studentGroupingSettingData?.isPublished) {
            const {
                institutionCalendarId,
                programId,
                year,
                term,
                level,
                courseIds,
                selectedType,
                deliveryGroups,
            } = studentGroupingSettingData;
            await moveToStudentsUpdatedOldStudentGroup({
                institutionCalendarId,
                programId,
                year,
                term,
                level,
                courseIds,
                selectedType,
                deliveryGroups,
                updatedDeliveryGroups,
                importStudents: updatedStudents,
            });
        }
        await updatedGroupingStudentStatus({
            groupSettingId,
            deliveryGroups: updatedDeliveryGroups,
            groupedStudents: allStudents,
        });
        await studentGroupRedisKeyUpdates({
            programId: studentGroupingSettingData.programId,
            year: studentGroupingSettingData.year,
            level: studentGroupingSettingData.level,
            term: studentGroupingSettingData.term,
            institutionCalendarId: studentGroupingSettingData.institutionCalendarId,
        });
        await studentPushInExistingSchedule({
            _institution_id,
            groupSettingId,
            studentIds,
            deliveryGroupDetails: selectedDeliveryType,
            attendanceStatus: studentAttendanceStatus,
            isAllStudent,
            groupingType,
        });
        console.log(
            await usersCoursesRedisCacheRemove({
                userIds: studentIds,
                institutionCalendarId: studentGroupingSettingData?.institutionCalendarId,
            }),
            'studentRemoveFromCourseSchedule -> Users Courses Redis Cache Remove',
        );
        logger.info('studentGroupMaster -> groupingStudent -> End');
        return {
            statusCode: 200,
            message: 'GROUPED_STUDENT_SUCCESSFULLY',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const groupingDeliveryData = async ({ groupSettingData }) => {
    try {
        groupSettingData.forEach((settingElement) => {
            settingElement.deliveryGroups.forEach((deliveryGroupElement) => {
                deliveryGroupElement.deliveryTypes.forEach((typeElement) => {
                    typeElement.selectedType.forEach((selectedTypeElement) => {
                        const hasStudents = selectedTypeElement?.groups?.some(
                            (groupElement) => groupElement?.studentIds?.length,
                        );
                        selectedTypeElement.isGrouped = hasStudents;
                        delete selectedTypeElement.groups;
                    });
                });
            });
        });
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: groupSettingData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const studentGroupDashboardService = async ({
    _institution_id,
    institutionCalendarId,
    programId,
}) => {
    try {
        const programCalendarData = await programCalendarSchema
            .findOne(
                {
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    _program_id: convertToMongoObjectId(programId),
                },
                {
                    _institution_calendar_id: 1,
                    _program_id: 1,
                    'level.term': 1,
                    'level.year': 1,
                    'level.level_no': 1,
                    'level.curriculum': 1,
                    'level.rotation': 1,
                    'level.rotation_count': 1,
                    'level.course._course_id': 1,
                    'level.course.courses_name': 1,
                    'level.course.courses_number': 1,
                    'level.course.model': 1,
                    'level.course.versionNo': 1,
                    'level.course.versioned': 1,
                    'level.course.versionName': 1,
                    'level.course.versionedFrom': 1,
                    'level.rotation_course.rotation_count': 1,
                    'level.rotation_course.course._course_id': 1,
                    'level.rotation_course.course.courses_name': 1,
                    'level.rotation_course.course.courses_number': 1,
                    'level.rotation_course.course.model': 1,
                    'level.rotation_course.course.versionNo': 1,
                    'level.rotation_course.course.versioned': 1,
                    'level.rotation_course.course.versionName': 1,
                },
            )
            .populate([
                {
                    path: '_program_id',
                    select: { name: 1, code: 1, program_type: 1 },
                },
                {
                    path: '_institution_calendar_id',
                    select: { calendar_name: 1 },
                },
            ])
            .lean();

        if (!programCalendarData) {
            return [];
        }

        const courseIds = programCalendarData.level.reduce((ids, level) => {
            const courses = level.rotation === YES ? level.rotation_course[0].course : level.course;
            return [...ids, ...courses.map((course) => String(course._course_id))];
        }, []);

        const sessionTypeDeliveryData = await sessionTypeDeliverySchema
            .find(
                {
                    isDeleted: false,
                    isActive: true,
                    _program_id: convertToMongoObjectId(programId),
                },
                {
                    _id: 0,
                    session_name: 1,
                    'delivery_types._id': 1,
                    'delivery_types.delivery_symbol': 1,
                },
            )
            .lean();

        const deliveryTypeMap = sessionTypeDeliveryData.reduce((map, session) => {
            session.delivery_types.forEach((delivery) => {
                map[String(delivery._id)] = {
                    session_type: session.session_name,
                    symbol: delivery.delivery_symbol,
                };
            });
            return map;
        }, {});

        const existingStudentGroup = await studentGroupSchema
            .find(
                {
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    'master._program_id': convertToMongoObjectId(programId),
                },
                {
                    _id: 1,
                    'master.year': 1,
                    'groups.level': 1,
                    'groups.term': 1,
                    'groups.courses._course_id': 1,
                    'groups.courses.session_types': 1,
                },
            )
            .lean();

        const courseSessionOrderData = await courseSessionOrderSchema
            .find(
                {
                    isDeleted: false,
                    isActive: true,
                    _course_id: { $in: courseIds },
                },
                {
                    _id: 0,
                    _course_id: 1,
                    'session_flow_data._delivery_id': 1,
                },
            )
            .lean();

        const courseDeliveryMap = courseSessionOrderData.reduce((map, course) => {
            const uniqueDeliveryIds = [
                ...new Set(course.session_flow_data.map((flow) => String(flow._delivery_id))),
            ];
            map[String(course._course_id)] = uniqueDeliveryIds.map(
                (id) => deliveryTypeMap[id] || { session_type: '', symbol: '' },
            );
            return map;
        }, {});

        const studentGroupWriteData = [];
        if (existingStudentGroup?.length) {
            existingStudentGroup.forEach((studentGroupElement) => {
                studentGroupElement.groups.forEach((groupElement) => {
                    const levelCourses =
                        programCalendarData.level.find(
                            (levelElement) =>
                                levelElement.level_no === groupElement.level &&
                                levelElement.term === groupElement.term &&
                                levelElement.year === studentGroupElement.master.year,
                        )?.course || [];
                    levelCourses.forEach((courseElement) => {
                        const calendarCourse = groupElement.courses.find(
                            (levelCourseElement) =>
                                String(levelCourseElement._course_id) ===
                                String(courseElement._course_id),
                        );
                        if (!calendarCourse) {
                            studentGroupWriteData.push({
                                updateOne: {
                                    filter: {
                                        _id: convertToMongoObjectId(studentGroupElement._id),
                                    },
                                    update: {
                                        $push: {
                                            'groups.$[levelIndex].courses': {
                                                _course_id: courseElement._course_id,
                                                course_name: courseElement.courses_name,
                                                course_no: courseElement.courses_number,
                                                course_type: courseElement.model,
                                                versionNo: courseElement.versionNo || 1,
                                                versioned: courseElement.versioned || false,
                                                versionName: courseElement.versionName || '',
                                                versionedFrom: courseElement.versionedFrom || null,
                                                session_types:
                                                    courseDeliveryMap[
                                                        String(courseElement._course_id)
                                                    ] || [],
                                            },
                                            $addToSet: {
                                                'groups.$[levelIndex].students.$[].course_group_status':
                                                    {
                                                        _course_id: courseElement._course_id,
                                                        status: 'pending',
                                                    },
                                            },
                                        },
                                    },
                                    arrayFilters: [
                                        {
                                            'levelIndex.level': groupElement.level,
                                            'levelIndex.term': groupElement.term,
                                        },
                                    ],
                                },
                            });
                        } else if (
                            calendarCourse.session_types.length === 0 &&
                            courseDeliveryMap[String(courseElement._course_id)]?.length
                        ) {
                            studentGroupWriteData.push({
                                updateOne: {
                                    filter: {
                                        _id: convertToMongoObjectId(studentGroupElement._id),
                                    },
                                    update: {
                                        $set: {
                                            'groups.$[levelIndex].courses.$[courseIndex].session_types':
                                                courseDeliveryMap[String(courseElement._course_id)],
                                        },
                                    },
                                    arrayFilters: [
                                        {
                                            'levelIndex.level': groupElement.level,
                                            'levelIndex.term': groupElement.term,
                                        },
                                        {
                                            'courseIndex._course_id': convertToMongoObjectId(
                                                courseElement._course_id,
                                            ),
                                        },
                                    ],
                                },
                            });
                        }
                    });
                });
            });
        } else {
            const yearGroups = new Map();
            const { calendarName, programName } = {
                calendarName: programCalendarData?._institution_calendar_id?.calendar_name || '',
                programName: programCalendarData?._program_id[0]?.name || '',
            };

            programCalendarData.level.forEach((levelElement) => {
                const levelCourses =
                    levelElement.rotation === YES
                        ? levelElement.rotation_course[0].course
                        : levelElement.course;

                const groupComponents = {
                    programInitial: programName.charAt(0) || '',
                    termInitial: levelElement.term.charAt(0).toUpperCase(),
                    yearNumber: levelElement.year.replace('year', ''),
                    levelNumber: levelElement.level_no.replace('Level ', ''),
                };

                const group_name = [
                    calendarName,
                    `${groupComponents.programInitial}P`,
                    `${groupComponents.termInitial}T`,
                    `${groupComponents.yearNumber}Y`,
                    `${groupComponents.programInitial}P:${levelElement.curriculum}`,
                    `${groupComponents.levelNumber}L`,
                ]
                    .filter(Boolean)
                    .join('-');

                const levelData = {
                    group_name,
                    group_mode:
                        levelElement.rotation === YES
                            ? ROTATION
                            : programCalendarData?._program_id[0]?.program_type === PREREQUISITE
                            ? FYD
                            : COURSE,
                    term: levelElement.term,
                    curriculum: levelElement.curriculum,
                    level: levelElement.level_no,
                    rotation: levelElement.rotation,
                    rotation_count: levelElement.rotation_count,
                    courses: levelCourses.map((course) => ({
                        _course_id: course._course_id,
                        course_name: course.courses_name,
                        course_no: course.courses_number,
                        course_type: course.model,
                        versionNo: course.versionNo || 1,
                        versioned: course.versioned || false,
                        versionName: course.versionName || '',
                        versionedFrom: course.versionedFrom || null,
                        session_types: courseDeliveryMap[String(course._course_id)] || [],
                    })),
                };

                if (!yearGroups.has(levelElement.year)) {
                    yearGroups.set(levelElement.year, []);
                }
                yearGroups.get(levelElement.year).push(levelData);
            });

            yearGroups.forEach((levelData, year) => {
                studentGroupWriteData.push({
                    insertOne: {
                        document: {
                            _institution_id,
                            _institution_calendar_id: institutionCalendarId,
                            master: {
                                _program_id: programId,
                                program_no: programCalendarData?._program_id[0]?.code,
                                program_name: programCalendarData?._program_id[0]?.name,
                                year,
                            },
                            groups: levelData,
                        },
                    },
                });
            });
        }
        if (studentGroupWriteData.length) {
            console.log(
                await studentGroupSchema.bulkWrite(studentGroupWriteData),
                studentGroupWriteData,
                'studentGroupDashboardService -> Student Group Write Data',
            );
        }
        return true;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        }
        throw new Error(error);
    }
};

const studentRemoveFromCourseSchedule = async ({
    _institution_id,
    institutionCalendarId,
    programId,
    term,
    yearNo,
    levelNo,
    courseIds,
    studentIds,
    rotationNo,
    removeCompleteCourse,
}) => {
    try {
        const scheduleUpdateQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
            _program_id: convertToMongoObjectId(programId),
            term,
            year_no: yearNo,
            ...(levelNo && { level_no: levelNo }),
            ...(courseIds && { _course_id: { $in: courseIds } }),
            ...(rotationNo && { rotation_count: rotationNo }),
            ...(!removeCompleteCourse && {
                $or: [
                    {
                        status: PENDING,
                    },
                    {
                        status: MISSED,
                    },
                ],
            }),
            isActive: true,
            isDeleted: false,
        };
        const scheduleUpdateCondition = {
            $pull: {
                students: { _id: { $in: studentIds } },
            },
        };
        console.log(
            await courseScheduleSchema.updateMany(scheduleUpdateQuery, scheduleUpdateCondition),
            scheduleUpdateQuery,
            scheduleUpdateCondition,
            'studentRemoveFromCourseSchedule -> Schedule Update',
        );
        console.log(
            await removedStudentCheckExistAssignment({
                _institution_id,
                institutionCalendarId,
                term,
                yearNo,
                level_no: levelNo,
                _course_id: courseIds,
                studentIds,
            }),
            'studentRemoveFromCourseSchedule -> Removed Student Check Exist Assignment',
        );
        console.log(
            await usersCoursesRedisCacheRemove({ userIds: studentIds, institutionCalendarId }),
            'studentRemoveFromCourseSchedule -> Users Courses Redis Cache Remove',
        );
        return true;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        }
        throw new Error(error);
    }
};

const getTotalSchedulingCount = async ({
    _institution_id,
    institutionCalendarId,
    term,
    year,
    level,
    programId,
    courseIds,
    selectedDeliveryType,
    gender,
}) => {
    try {
        const scheduleQuery = [];
        selectedDeliveryType.forEach((deliveryTypeElement) => {
            courseIds.forEach((courseIdElement) => {
                scheduleQuery.push({
                    _course_id: convertToMongoObjectId(courseIdElement),
                    'session.delivery_symbol': deliveryTypeElement.deliverySymbol,
                    'student_groups.session_group.group_no': deliveryTypeElement.groupNo,
                });
            });
        });
        return await courseScheduleSchema.countDocuments({
            _institution_id: convertToMongoObjectId(_institution_id),
            _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
            term,
            year_no: year,
            ...(level && { level_no: level }),
            _program_id: convertToMongoObjectId(programId),
            'student_groups.gender': gender,
            type: REGULAR,
            $or: scheduleQuery,
            status: COMPLETED,
            isDeleted: false,
            isActive: true,
        });
    } catch (error) {
        logger.error('getTotalSchedulingCount -> Error:', error);
        if (error instanceof Error) {
            throw error;
        }
        throw new Error(error);
    }
};

const getUserSelectedTypeBaseFilter = ({
    studentGroupSettings,
    userSelectedType,
    level,
    courseIds,
}) => {
    switch (userSelectedType) {
        case YEAR:
            return studentGroupSettings.filter(
                (groupSetting) => groupSetting.selectedType === YEAR,
            );
        case LEVEL:
            return studentGroupSettings.filter(
                (groupSetting) =>
                    (groupSetting.selectedType === LEVEL && groupSetting.level === level) ||
                    groupSetting.selectedType === YEAR,
            );
        case COURSE:
            return studentGroupSettings.filter((groupSetting) => {
                const hasMatchingCourses = courseIds?.some((courseId) =>
                    groupSetting.courseIds?.some(
                        (settingCourseId) => String(settingCourseId) === String(courseId),
                    ),
                );
                return (
                    groupSetting.selectedType === YEAR ||
                    (groupSetting.selectedType === LEVEL && groupSetting.level === level) ||
                    (groupSetting.selectedType === COURSE &&
                        groupSetting.level === level &&
                        hasMatchingCourses)
                );
            });
        default:
            return studentGroupSettings;
    }
};

const groupWiseListStudentMultiCourse = async ({
    studentGroupSettings,
    groupType,
    genderType,
    studentGroupSettingId,
    studentGender,
}) => {
    try {
        const studentMap = new Map();
        studentGroupSettings.forEach((groupedSettingElement) => {
            groupedSettingElement.students.forEach((studentElement) => {
                const studentId = String(studentElement.studentId._id);
                if (!studentMap.has(studentId)) {
                    studentMap.set(studentId, {
                        ...studentElement,
                        studentGroupingSettingId: groupedSettingElement._id,
                        deliverySettings: new Map(),
                    });
                }
                const student = studentMap.get(studentId);
                groupedSettingElement.deliveryGroups.forEach((deliveryGroupElement) => {
                    const gender = studentElement.studentId.gender;
                    const isGenderMatch =
                        !genderType ||
                        genderType === MIXED ||
                        genderType === BOTH ||
                        deliveryGroupElement.gender === gender;
                    if (isGenderMatch || genderType === gender) {
                        deliveryGroupElement.deliveryTypes?.forEach((deliveryTypeElement) => {
                            deliveryTypeElement.selectedType?.forEach((selectedTypeElement) => {
                                const studentGroup = selectedTypeElement.groups.find(
                                    (groupElement) =>
                                        groupElement.studentIds.some(
                                            (studentIdElement) =>
                                                String(studentIdElement) === studentId,
                                        ),
                                );
                                if (isGenderMatch) {
                                    if (!student.deliverySettings.has(groupedSettingElement._id)) {
                                        student.deliverySettings.set(groupedSettingElement._id, []);
                                    }
                                    student.deliverySettings.get(groupedSettingElement._id).push({
                                        deliverySymbol: selectedTypeElement.deliverySymbol,
                                        groupNo: studentGroup ? studentGroup.groupNo : null,
                                    });
                                }
                            });
                        });
                    }
                });
            });
        });
        const groupedStudents = [];
        const ungroupedStudents = [];
        studentMap.forEach((studentElement) => {
            const studentWithSettings = {
                ...studentElement,
                deliverySettings: Array.from(studentElement.deliverySettings.values()).flat(),
                isFullyGrouped: Array.from(studentElement.deliverySettings.values())
                    .flat()
                    .every((settingElement) => settingElement.groupNo !== null),
            };
            if (
                studentWithSettings?.deliverySettings?.length &&
                studentWithSettings.isFullyGrouped
            ) {
                groupedStudents.push(studentWithSettings);
            } else {
                ungroupedStudents.push(studentWithSettings);
            }
        });

        let unGroupedCount = 0;
        let groupedCount = 0;
        let maleCount = 0;
        let femaleCount = 0;
        let mixedCount = 0;
        const filteredGroupedStudents = groupedStudents?.filter(
            (groupedStudentElement) =>
                String(groupedStudentElement.studentGroupingSettingId) ===
                String(studentGroupSettingId),
        );
        const filteredUngroupedStudents = ungroupedStudents?.filter(
            (groupedStudentElement) =>
                String(groupedStudentElement.studentGroupingSettingId) ===
                String(studentGroupSettingId),
        );
        unGroupedCount = filteredUngroupedStudents.length;
        groupedCount = filteredGroupedStudents.length;
        const studentsToCount =
            groupType === GROUPED
                ? filteredGroupedStudents
                : groupType === UNGROUPED
                ? filteredUngroupedStudents
                : [...filteredGroupedStudents, ...filteredUngroupedStudents];
        studentsToCount.forEach((studentElement) => {
            const gender = studentElement.studentId.gender;
            if (gender === FEMALE) femaleCount++;
            else if (gender === MALE) maleCount++;
        });
        mixedCount = maleCount + femaleCount;
        const filterGroupedStudentList =
            groupType === GROUPED
                ? groupedStudents
                : groupType === UNGROUPED
                ? ungroupedStudents
                : [...groupedStudents, ...ungroupedStudents];
        const studentList = [];
        filterGroupedStudentList.forEach((studentElement) => {
            const uniqueGroups = new Map();
            studentElement.deliverySettings.forEach((settingElement) => {
                if (
                    settingElement.deliverySymbol &&
                    !uniqueGroups.has(settingElement.deliverySymbol)
                ) {
                    uniqueGroups.set(settingElement.deliverySymbol, {
                        deliverySymbol: settingElement.deliverySymbol,
                        groupNo: settingElement.groupNo,
                    });
                }
            });
            if (!studentGender || studentGender === studentElement.studentId.gender)
                studentList.push({
                    userId: studentElement.studentId._id,
                    name: studentElement.studentId.name,
                    gender: studentElement.studentId.gender,
                    academicId: studentElement.academicId,
                    mark: studentElement.mark,
                    importedBy: studentElement.importedId?.name,
                    importedAt: studentElement.importedDate,
                    groups: Array.from(uniqueGroups.values()),
                    studentGroupingSettingId: studentElement.studentGroupingSettingId,
                });
        });
        logger.info('studentGroupMaster -> yearWiseImportedStudentList -> End');
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: {
                totalCount: studentList.length,
                unGroupedCount,
                groupedCount,
                maleCount,
                femaleCount,
                mixedCount,
                studentList,
            },
        };
    } catch (error) {
        logger.error('groupWiseListStudentMultiCourse -> Error:', error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const groupWiseListStudentList = async ({
    studentGroupSettings,
    groupType,
    genderType,
    studentGender,
    studentGroupSettingId,
}) => {
    try {
        const studentMap = new Map();
        studentGroupSettings?.students?.forEach((studentElement) => {
            const studentId = String(studentElement.studentId._id);
            if (!studentMap.has(studentId)) {
                studentMap.set(studentId, {
                    ...studentElement,
                    deliverySettings: new Map(),
                });
            }
            const student = studentMap.get(studentId);
            if (!studentGroupSettings?.deliveryGroups?.length) return;
            studentGroupSettings?.deliveryGroups.forEach((deliveryGroupElement) => {
                const gender = studentElement.studentId.gender;
                const isGenderMatch =
                    !genderType ||
                    genderType === MIXED ||
                    genderType === BOTH ||
                    deliveryGroupElement.gender === gender;
                if (isGenderMatch || genderType === gender) {
                    deliveryGroupElement.deliveryTypes?.forEach((deliveryTypeElement) => {
                        deliveryTypeElement.selectedType?.forEach((selectedTypeElement) => {
                            const studentGroup = selectedTypeElement.groups.find((groupElement) =>
                                groupElement.studentIds.some(
                                    (studentIdElement) => String(studentIdElement) === studentId,
                                ),
                            );
                            if (isGenderMatch) {
                                if (!student.deliverySettings.has(studentGroupSettingId)) {
                                    student.deliverySettings.set(studentGroupSettingId, []);
                                }
                                student.deliverySettings.get(studentGroupSettingId).push({
                                    deliverySymbol: selectedTypeElement.deliverySymbol,
                                    groupNo: studentGroup ? studentGroup.groupNo : null,
                                });
                            }
                        });
                    });
                }
            });
        });
        const groupedStudents = [];
        const ungroupedStudents = [];
        studentMap.forEach((studentElement) => {
            const studentWithSettings = {
                ...studentElement,
                deliverySettings: Array.from(studentElement.deliverySettings.values()).flat(),
                isFullyGrouped: Array.from(studentElement.deliverySettings.values())
                    .flat()
                    .every((settingElement) => settingElement.groupNo !== null),
            };
            if (
                studentWithSettings?.deliverySettings?.length &&
                studentWithSettings.isFullyGrouped
            ) {
                groupedStudents.push(studentWithSettings);
            } else {
                ungroupedStudents.push(studentWithSettings);
            }
        });
        let unGroupedCount = 0;
        let groupedCount = 0;
        let maleCount = 0;
        let femaleCount = 0;
        let mixedCount = 0;
        unGroupedCount = ungroupedStudents.length;
        groupedCount = groupedStudents.length;
        const studentsToCount = groupType === GROUPED ? groupedStudents : ungroupedStudents;
        studentsToCount.forEach((studentElement) => {
            const gender = studentElement.studentId.gender;
            if (gender === FEMALE) femaleCount++;
            else if (gender === MALE) maleCount++;
        });
        mixedCount = maleCount + femaleCount;
        const filterGroupedStudentList =
            groupType === GROUPED
                ? groupedStudents
                : groupType === UNGROUPED
                ? ungroupedStudents
                : [...groupedStudents, ...ungroupedStudents];
        const studentList = [];
        filterGroupedStudentList.forEach((studentElement) => {
            const uniqueGroups = new Map();
            studentElement.deliverySettings.forEach((settingElement) => {
                if (
                    settingElement.deliverySymbol &&
                    !uniqueGroups.has(settingElement.deliverySymbol)
                ) {
                    uniqueGroups.set(settingElement.deliverySymbol, {
                        deliverySymbol: settingElement.deliverySymbol,
                        groupNo: settingElement.groupNo,
                    });
                }
            });
            if (!studentGender || studentGender === studentElement.studentId.gender)
                studentList.push({
                    userId: studentElement.studentId._id,
                    name: studentElement.studentId.name,
                    gender: studentElement.studentId.gender,
                    academicId: studentElement.academicId,
                    mark: parseFloat(studentElement.mark),
                    importedBy: studentElement.importedId?.name,
                    importedAt: studentElement.importedDate,
                    groups: Array.from(uniqueGroups.values()),
                    studentGroupingSettingId: studentGroupSettingId,
                });
        });
        logger.info('studentGroupMaster -> yearWiseImportedStudentList -> End');
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: {
                totalCount: studentList.length,
                unGroupedCount,
                groupedCount,
                maleCount,
                femaleCount,
                mixedCount,
                studentList,
            },
        };
    } catch (error) {
        logger.error('groupWiseListStudentList -> Error:', error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getYearWiseImportedStudentList = async ({
    _institution_id,
    institutionCalendarId,
    programId,
    term,
    year,
    level,
    courseIds,
    studentGroupSettingId,
    userSelectedType,
    genderType,
    groupType,
    studentGender,
}) => {
    try {
        // const studentGroupSettingData = await studentGroupSettingSchema
        //     .find(
        //         {
        //             _institution_id: convertToMongoObjectId(_institution_id),
        //             institutionCalendarId: convertToMongoObjectId(institutionCalendarId),
        //             programId: convertToMongoObjectId(programId),
        //             term,
        //             year,
        //         },
        //         {
        //             selectedType: 1,
        //             level: 1,
        //             courseIds: 1,
        //             deliveryGroups: 1,
        //             students: 1,
        //         },
        //     )
        //     .populate({ path: 'students.studentId', select: { name: 1, gender: 1 } })
        //     .populate({ path: 'students.importedId', select: { name: 1 } })
        //     .lean();
        const studentGroupSettingData = await studentGroupSettingSchema
            .findOne(
                {
                    _id: convertToMongoObjectId(studentGroupSettingId),
                },
                {
                    selectedType: 1,
                    level: 1,
                    courseIds: 1,
                    deliveryGroups: 1,
                    students: 1,
                },
            )
            .populate({ path: 'students.studentId', select: { name: 1, gender: 1 } })
            .populate({ path: 'students.importedId', select: { name: 1 } })
            .lean();

        if (!studentGroupSettingData) {
            return { statusCode: 404, message: 'NO_DATA_FOUND' };
        }
        return groupWiseListStudentList({
            studentGroupSettings: studentGroupSettingData,
            studentGroupSettingId,
            groupType,
            genderType,
            studentGender,
        });

        //To do Hierarchy-based student listing according to user flow: year → level → course

        // const filteredStudentGroupSettings = getUserSelectedTypeBaseFilter({
        //     studentGroupSettings: studentGroupSettingData,
        //     level,
        //     courseIds,
        //     userSelectedType,
        // });
        // return groupWiseListStudentMultiCourse({
        //     studentGroupSettings: studentGroupSettingData,
        //     studentGroupSettingId,
        //     groupType,
        //     courseIds,
        //     genderType,
        //     studentGender,
        // });
    } catch (error) {
        logger.error('getYearWiseImportedStudentList -> Error:', error);
        if (error instanceof Error) {
            throw error;
        }
        throw new Error(error);
    }
};

const checkImportedStudentDetails = async ({
    studentDetails,
    existingStudentAcademicId,
    importedStudents,
    userId,
    groupSettingId,
}) => {
    try {
        const studentDuplicateCheck = [];
        const students = [];
        const duplicateAcademicIds = new Set();
        const studentDetailsMap = new Map(
            studentDetails.map((studentElement) => [studentElement.user_id, studentElement]),
        );
        importedStudents.forEach((importedStudentElement) => {
            const academicId = importedStudentElement?.academicId;
            if (
                !academicId &&
                !studentDuplicateCheck?.some(
                    (studentDuplicateElement) =>
                        studentDuplicateElement?.message === 'STUDENT_ACADEMIC_ID_IS_REQUIRED',
                )
            ) {
                studentDuplicateCheck.push({
                    message: 'STUDENT_ACADEMIC_ID_IS_REQUIRED',
                });
            }
            const studentDetail = studentDetailsMap.get(academicId);
            if (groupSettingId && existingStudentAcademicId.includes(academicId)) {
                if (studentDetail && studentDetail.user_type === STUDENT) {
                    studentDuplicateCheck.push({
                        user_id: academicId,
                        name: studentDetail.name,
                        gender: studentDetail.gender,
                        message: 'STUDENT_IS_ALREADY_EXIST_IN_THIS_GROUP',
                    });
                }
            }

            const mark = Math.round(parseFloat(importedStudentElement.mark) * 100) / 100;
            if (academicId && duplicateAcademicIds.has(academicId)) {
                studentDuplicateCheck.push({
                    user_id: academicId,
                    message: 'DUPLICATE_ACADEMIC_ID',
                    isDuplicate: true,
                });
            }
            duplicateAcademicIds.add(academicId);
            if (academicId && !studentDetail) {
                studentDuplicateCheck.push({
                    user_id: academicId,
                    message: 'USER_IS_NOT_REGISTERED',
                    unRegister: true,
                });
            } else if (studentDetail?.user_type === STAFF) {
                studentDuplicateCheck.push({
                    user_id: academicId,
                    message: 'IT_IS_STAFF_NOT_TO_REGISTER',
                    unRegister: true,
                });
            } else if (academicId && !mark) {
                studentDuplicateCheck.push({
                    user_id: academicId,
                    message: 'STUDENT_MARK_IS_REQUIRED',
                });
            } else if (mark < 1 || mark > Number(SG_MAX_MARK)) {
                studentDuplicateCheck.push({
                    user_id: academicId,
                    message: `CGPA_VALUE_SHOULD_BE_BETWEEN_1_AND_${SG_MAX_MARK}`,
                });
            } else if (studentDetail) {
                students.push({
                    studentId: studentDetail._id,
                    academicId: studentDetail.user_id,
                    mark,
                    importedId: userId,
                    importedDate: new Date(),
                });
            }
        });
        return { studentDuplicateCheck, students };
    } catch (error) {
        logger.error('checkImportedStudentDetails -> Error:', error);
        if (error instanceof Error) {
            throw error;
        }
        throw new Error(error);
    }
};

const checkStudentsAreImported = async ({
    groupSettingId,
    institutionCalendarId,
    programId,
    term,
    selectedType,
    year,
    level,
    importedStudents,
    courseIds,
    userId,
}) => {
    try {
        const studentGroupSettingData = await studentGroupSettingSchema
            .find(
                {
                    _id: convertToMongoObjectId(groupSettingId),
                    institutionCalendarId: convertToMongoObjectId(institutionCalendarId),
                    programId: convertToMongoObjectId(programId),
                    term,
                    year,
                },
                {
                    selectedType: 1,
                    level: 1,
                    courseIds: 1,
                    'students.academicId': 1,
                },
            )
            .lean();
        const existingStudentAcademicId = [];
        let existingStudentList;
        if (groupSettingId && studentGroupSettingData?.length) {
            existingStudentList = await getUserSelectedTypeBaseFilter({
                studentGroupSettings: studentGroupSettingData,
                userSelectedType: selectedType,
                level,
                courseIds,
            });
            existingStudentList.forEach((existingElement) => {
                existingElement.students.forEach((studentElement) => {
                    if (studentElement.academicId) {
                        existingStudentAcademicId.push(studentElement.academicId);
                    }
                });
            });
        }
        const studentAcademicIds = importedStudents?.map(
            (studentElement) => studentElement.academicId,
        );
        const studentDetails = await checkStudentRegister({ studentAcademicIds });
        const checkImportedStudent = await checkImportedStudentDetails({
            studentDetails,
            existingStudentAcademicId,
            importedStudents,
            userId,
            groupSettingId,
        });
        return checkImportedStudent;
    } catch (error) {
        logger.error('checkStudentsAreImported -> Error:', error);
        if (error instanceof Error) {
            throw error;
        }
        throw new Error(error);
    }
};

const createDeliveryGroups = ({ deliveryGroups }) => {
    deliveryGroups.forEach((deliveryGroupElement) => {
        deliveryGroupElement?.deliveryTypes?.forEach((deliveryTypeElement) => {
            deliveryTypeElement?.selectedType?.forEach((selectedTypeElement) => {
                if (!Array.isArray(selectedTypeElement.groups)) {
                    selectedTypeElement.groups = [];
                }
                if (selectedTypeElement.isGrouped) {
                    const currentCount = selectedTypeElement.groups.length;
                    for (let i = currentCount; i < selectedTypeElement.noOfGroups; i++) {
                        selectedTypeElement.groups.push({
                            groupNo: i + 1,
                            studentIds: [],
                            _id: convertToMongoObjectId(),
                        });
                    }
                } else {
                    selectedTypeElement.groups = [];
                    for (let i = 0; i < selectedTypeElement.noOfGroups; i++) {
                        selectedTypeElement.groups.push({
                            groupNo: i + 1,
                            studentIds: [],
                            _id: convertToMongoObjectId(),
                        });
                    }
                }
            });
        });
    });
    return deliveryGroups;
};

const removedImportedStudentsIds = async ({ removedImportedGroupingIds }) => {
    try {
        return await studentGroupSettingSchema.updateMany(
            {
                _id: {
                    $in: removedImportedGroupingIds.map((groupingIdElement) =>
                        convertToMongoObjectId(groupingIdElement),
                    ),
                },
            },
            {
                $set: { students: [] },
            },
        );
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getAutoGenerateGroupName = async ({
    _institution_id,
    institutionCalendarId,
    programId,
    term,
    year,
    level,
    courseIds,
}) => {
    try {
        const programCalendarData = await programCalendarSchema
            .findOne(
                {
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    _program_id: convertToMongoObjectId(programId),
                },
                {
                    _institution_calendar_id: 1,
                    _program_id: 1,
                    'level.term': 1,
                    'level.year': 1,
                    'level.level_no': 1,
                    'level.curriculum': 1,
                    'level.course._course_id': 1,
                    'level.course.courses_number': 1,
                },
            )
            .populate({ path: '_institution_calendar_id', select: { calendar_name: 1 } })
            .populate({ path: '_program_id', select: { name: 1 } })
            .lean();

        const programYearLevelData = programCalendarData.level.find(
            (levelElement) =>
                levelElement.term === term &&
                levelElement.year === year &&
                (level ? levelElement.level_no === level : true),
        );
        const group_name =
            programCalendarData._institution_calendar_id.calendar_name +
            '-' +
            programCalendarData._program_id[0].name.substring(0, 1) +
            'P' +
            '-' +
            (programYearLevelData.term.substring(0, 1).toUpperCase() + 'T') +
            '-' +
            programYearLevelData.year.replace('year', '') +
            'Y' +
            '-' +
            programCalendarData._program_id[0].name.substring(0, 1) +
            'P:' +
            programYearLevelData.curriculum +
            (level ? '-' + programYearLevelData.level_no.replace('Level ', '') + 'L' : '');
        // (courseIds?.length ? '-' + programLevelCourseData.courses_number + 'C' : '');
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: group_name };

        // const studentGroupData = await studentGroupSchema
        //     .findOne(
        //         {
        //             _institution_id: convertToMongoObjectId(_institution_id),
        //             _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
        //             'master._program_id': convertToMongoObjectId(programId),
        //             'groups.term': term,
        //             'master.year': year,
        //             ...(level && { 'groups.level': level }),
        //             ...(courseIds?.length && {
        //                 'groups.courses._course_id': {
        //                     $in: courseIds.map((courseIdElement) =>
        //                         convertToMongoObjectId(courseIdElement),
        //                     ),
        //                 },
        //             }),
        //         },
        //         {
        //             'groups.term': 1,
        //             'groups.level': 1,
        //             'groups.courses._course_id': 1,
        //             'groups.group_name': 1,
        //         },
        //     )
        //     .lean();
        // const courseIdStrings = courseIds?.map(String);
        // const groupNameSet = new Set();
        // studentGroupData?.groups?.forEach((groupElement) => {
        //     if (
        //         groupElement?.term?.toLowerCase() === term?.toLowerCase() &&
        //         (!level || groupElement?.level?.toLowerCase() === level?.toLowerCase())
        //     ) {
        //         groupElement?.courses?.forEach((groupCourseElement) => {
        //             if (courseIdStrings?.includes(String(groupCourseElement._course_id))) {
        //                 groupNameSet.add(groupElement.group_name);
        //             }
        //         });
        //     }
        // });
        // return { statusCode: 200, message: 'DATA_RETRIEVED', data: Array.from(groupNameSet)[0] };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getSingleStudentDetails = async ({ query = {} }) => {
    try {
        const { groupSettingId, academicId } = query;
        const queryParsing = {
            groupSettingId,
            academicId,
        };
        logger.info(queryParsing, 'studentGroupMaster -> getSingleStudentDetails -> Start');
        // Use regex for case-insensitive search
        const academicIdRegex = new RegExp(`^${academicId}$`, 'i');
        const studentRegister = await checkStudentRegister({
            studentAcademicIds: [academicIdRegex],
        });
        if (!studentRegister?.length) {
            return { statusCode: 404, message: 'STUDENT_ACADEMIC_NO_NOT_FOUND' };
        }
        // Ensure alreadyInStudentGroup is also case-insensitive
        const existingInStudentGroup = await alreadyInStudentGroup({
            groupSettingId,
            academicId: academicIdRegex,
        });
        logger.info(queryParsing, 'studentGroupMaster -> getSingleStudentDetails -> End');
        if (existingInStudentGroup) {
            return { statusCode: 404, message: 'STUDENT_IS_ALREADY_EXIST_IN_THIS_GROUP' };
        }
        return { statusCode: 200, message: 'YOU_CAN_ADD_STUDENT', data: studentRegister[0] };
    } catch (error) {
        logger.error({ error }, 'studentGroupMaster -> getSingleStudentDetails -> Error');
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = {
    getYearLevelList,
    programList,
    programYearLevelList,
    getCourseBasedDelivery,
    checkStudentRegister,
    getProgramCalenderCourseIds,
    notRegisterUserDetails,
    updateOldStudentGroupData,
    alreadyInStudentGroup,
    getAutoGenerateGroupName,
    updatedStudentGrouping,
    groupingDeliveryData,
    groupWiseListStudent,
    studentGroupDashboardService,
    studentRemoveFromCourseSchedule,
    getTotalSchedulingCount,
    getYearWiseImportedStudentList,
    checkStudentsAreImported,
    studentPushInExistingSchedule,
    createDeliveryGroups,
    removedImportedStudentsIds,
    getSingleStudentDetails,
};
