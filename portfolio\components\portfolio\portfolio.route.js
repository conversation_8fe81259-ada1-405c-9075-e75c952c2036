const router = require('express').Router();
const { validate } = require('../../common/middlewares/validation');
const catchAsync = require('../../../lib/utility/catch-async');
const PortfolioController = require('./portfolio.controller');
const FormController = require('../form/form.controller');
const {
    getPortfolioSchema,
    updatePortfolioSchema,
    deletePortfolioSchema,
    publishPortfolioSchema,
    getComponentFormSchema,
    assignStudentSchema,
    getStudentsByCourseSchema,
    updateStudentReviewSchema,
    getPortfolioForAssignSchema,
    validatePortfolioChildSchema,
    updateFormInPortfolioSchema,
    validateGetInsight,
} = require('./portfolio.validation');

router.get('/', validate(getPortfolioSchema), catchAsync(PortfolioController.getPortfolio));

router.put('/', validate(updatePortfolioSchema), catchAsync(PortfolioController.updatePortfolio));

router.delete(
    '/',
    validate(deletePortfolioSchema),
    catchAsync(PortfolioController.deletePortfolio),
);

router.put(
    '/publish',
    validate(publishPortfolioSchema),
    catchAsync(PortfolioController.publishPortfolio),
);

router.get(
    '/form',
    validate(getComponentFormSchema),
    catchAsync(PortfolioController.getComponentForm),
);

router.get('/dashboard', catchAsync(PortfolioController.getPortfolioDashboard));

router.delete('/detach', catchAsync(PortfolioController.detachPortfolioFrom));

router.put(
    '/assign',
    validate(assignStudentSchema),
    catchAsync(PortfolioController.assignStudentToPortfolio),
);

router.get(
    '/student',
    validate(getStudentsByCourseSchema),
    catchAsync(PortfolioController.getStudentsByCourse),
);

router.put(
    '/review',
    validate(updateStudentReviewSchema),
    catchAsync(PortfolioController.updateStudentReview),
);

router.get(
    '/assign',
    validate(getPortfolioForAssignSchema),
    catchAsync(PortfolioController.getPortfolioForAssignEvaluator),
);

router.get('/form-list', catchAsync(PortfolioController.getForms));

router.put(
    '/role',
    validate(validatePortfolioChildSchema),
    catchAsync(PortfolioController.updateRolesInPortfolio),
);

router.put(
    '/form',
    validate(updateFormInPortfolioSchema),
    catchAsync(PortfolioController.updateFormInPortfolio),
);

router.put(
    '/attach-form',
    validate(validatePortfolioChildSchema),
    catchAsync(PortfolioController.attachFormToPortfolio),
);

router.get('/dashboard/count', catchAsync(PortfolioController.getCountForPortfolioDashboard));

router.get(
    '/dashboard/insight',
    validate(validateGetInsight),
    catchAsync(PortfolioController.getInsights),
);

module.exports = router;
