const router = require('express').Router();
const external_staff = require('../../lib/external_staff/external_staff_route');
const qapcSetting_v2 = require('../../lib/q360/qapcSetting/qapcSetting.route');
const qapcCategory_v2 = require('../../lib/q360/categories/qapcCategory.route');
const rolePermission = require('../../lib/q360/rolePermission/rolePermission.route');
const formApprover = require('../../lib/q360/formApprover/formApprover.route');
const formInitiator = require('../../lib/q360/formInitiator/formInitiator.route');
const dcExternalSync = require('../../lib/dcExternalSync/dcExternalSync.route');
const qapcCategoryForm_v2 = require('../../lib/q360/categoryForm/categoryForm.route');
const qapcDashboard = require('../../lib/q360/dashboard/qapcDashboard.route');
const leader_board = require('../../lib/leader_board/leader_board.route');
const announcement = require('../../lib/announcement/announcement_route');
const announcement_setting = require('../../lib/announcement_setting/announcement_setting_route');
const country = require('../../lib/country/country_route');
const university = require('../../lib/university/university_route');
const institution = require('../../lib/institution/institution_route');
const infrastructure = require('../../lib/Infrastructures/infrastructure_route');
// const infrastructure_delivery_type = require('../../lib/Infrastructure_delivery_type/infrastructure_delivery_type_route');
const role = require('../../lib/role/role_route');
const institution_calendar = require('../../lib/institution_calendar/institution_calendar_route');
const institution_calendar_event = require('../../lib/calendar_event/calendar_event_route');
const program_calendar = require('../../lib/program_calendar/program_calendar_route');
const user = require('../../lib/user_management/user_route');
// const user_history = require('../../lib/user_history/user_history_route');
const program_calendar_event = require('../../lib/program_calendar_event/program_calendar_event_route');
const program_calendar_course = require('../../lib/program_calendar_course/program_calendar_course_route');
const role_management = require('../../lib/role_management/role_management_route');
const program_calendar_review = require('../../lib/program_calendar_review/program_calendar_review_route');
const student_group = require('../../lib/student_group/student_group_route');
const student_group_gender_merge = require('../../lib/student_group_gender_merge/student_group_route');
// const student_role_route = require('../../lib/student_role/student_role_route');
// const student_role_allotment_route = require('../../lib/student_role_allotment/student_role_allotment_route');
// const infrastructure_event_route = require('../../lib/infrastructure_events/infrastructure_event_route');
// const staff_committee = require('../../lib/staff_committee/staff_committee_route');
// const day_group = require('../../lib/day_group/day_group_route');
const time_group = require('../../lib/time_group/time_group_route');
// const college_building = require('../../lib/college_building/college_building_route');
// const hospital = require('../../lib/hospital/hospital_route');
const infrastructure_management = require('../../lib/infrastructure_management/infrastructure_management_route');
const lms = require('../../lib/lms/lms_route');
const lms_review = require('../../lib/lms_review/lms_review_route');
const lmsStudentSetting = require('../../lib/lmsStudentSetting/lmsStudentSetting.route');
const lmsStudent = require('../../lib/lmsStudent/lmsStudent.route');
const lmsWarning = require('../../lib/lmsWarning/lmsWarning.route');
const lmsDenial = require('../../lib/lms_denial/lms_denial_route');
const lmsAttendanceConfig = require('../../lib/lmsAttendanceConfig/lmsAttendanceConfig.route');
// const roles_offices = require('../../lib/roles_offices_list/roles_offices_route');
// const roles_permissions = require('../../lib/roles_permissions/roles_permissions_route');
// const infrastructure_event_exam = require('../../lib/infrastructure_event_exam/infrastructure_event_exam_route');
// const infrastructure_delivery_medium = require('../../lib/infrastructure_delivery_medium/infrastructure_delivery_medium_route');
const student_leave_register = require('../../lib/student_leave_register/student_leave_register_route');
// const role_assign_to_staff = require('../../lib/role_assign_to_staff/role_assign_to_staff_route');

// const course_staff_allocation = require('../../lib/course_staff_allocation/course_staff_allocation_route');
const course_schedule = require('../../lib/course_schedule/course_schedule_route');
// const course_management_setting = require('../../lib/course_management_setting/course_management_setting_route');

const modules = require('../../lib/module/module_route');
const role_assign = require('../../lib/role_assign/role_assign_route');
//New Program Inputs
const digi_institute = require('../../lib/digi_institute/digi_institute_route');
const digi_program = require('../../lib/digi_program/digi_program_route');
const digi_department_subject = require('../../lib/digi_department_subject/digi_department_subject_route');
const digi_session_delivery_types = require('../../lib/digi_session_delivery_types/digi_session_delivery_types_route');
const digi_curriculum = require('../../lib/digi_curriculum/digi_curriculum_route');
const digi_college = require('../../lib/digi_college/digi_college_route');
const digi_university = require('../../lib/digi_university/digi_university_route');
const digi_course = require('../../lib/digi_course/digi_course_route');
const digi_course_group = require('../../lib/digi_course_group/digi_course_group_route');
const digi_course_assign = require('../../lib/digi_course_assign/digi_course_assign_route');
const courseVersioning = require('../../lib/course_versioning/courseVersioning_routes');
// Mapping moduleS
const digi_framework = require('../../lib/digi_framework/route');
const digi_impact_mapping_type = require('../../lib/digi_impact_mapping_type/route');
const digi_content_mapping_type = require('../../lib/digi_content_mapping_type/route');
const digi_mapping = require('../../lib/digi_mapping/route');
// cs dashboard module
const digi_dashboard_route = require('../../lib/digi_dashboard/route');
//Course Schedule
const course_schedule_setting = require('../../lib/course_schedule_setting/course_schedule_setting_route');
const course_coordinator = require('../../lib/course_coordinator/course_coordinator_route');
const schedule_events_and_support_session = require('../../lib/schedule_events_and_support_session/schedule_events_and_support_session_route');
const reports_analytics = require('../../lib/reports_analytics/reports_analytics_route');
const dc_reports_analytics = require('../../lib/digi_class/reports/report_route');
//Vaccination
const vaccination = require('../../lib/vaccination/vaccination_route');
const user_vaccination_details = require('../../lib/user_vaccination_details/user_vaccination_details_route');
// digi chat module
const digi_chat = require('../../lib/digi_class/digi_chat/chat_route');
const digi_chat_v2 = require('../../lib/digi_class/digi_chat_v2/chat_route');
const programReportSetting = require('../../lib/program_report_setting/program_report_setting_route');
// Digi Survey
const survey = require('../../lib/digi_class/survey/surveyRoute');
const studentCriteria = require('../../lib/studentRegister/studentRegisterRoute');
const learningOutcome = require('../../lib/learning_outcome/learning_outcome_route');
// Display label update module
// const label = require('../../lib/label_management/label_route');
const courseScheduleSettings = require('../../lib/course_schedule_settings/course_schedule_route');

// Institution Daily Session Report
const sessionReport = require('../../lib/session-report/session-report.route');

//  create Day sessions
const globalSessionSettings = require('../../lib/global_session_settings/global_session_settings_route');

//DigiClass APIs
const user_announcement = require('../../lib/digi_class/user_announcement/announcement_route');
const class_user = require('../../lib/digi_class/user_management/user_route');
const sessions = require('../../lib/digi_class/sessions/sessions_route');
const notification_manager = require('../../lib/digi_class/notification/notification_route');
const course_session = require('../../lib/digi_class/course_session/course_session_route');
const document_manager = require('../../lib/digi_class/document_manager/document_manager_route');
const course_admin = require('../../lib/digi_class/course_admin/course_admin_route');
const activities = require('../../lib/digi_class/activities/activities_route');
const activities_v2 = require('../../lib/digi_class/activities_version2/activities_v2_route');
const taxonomy = require('../../lib/digi_class/taxonomy/taxonomy_route');
const dashboard = require('../../lib/digi_class/dashboard/dashboard_route');
const learningOutcomes = require('../../lib/digi_class/learning_outcomes/learning_outcomes.route');
const appVersion = require('../../lib/digi_class/app_version_control/app_version_route');
const notificationManagerService = require('../../lib/digi_class/notification/notification_route');
const scheduleAttendance = require('../../lib/digi_class/schedule-attendance/schedule-attendance.route');
// const emailSettings = require('../../lib/email_settings/email_settings_route');
const scheduleMultiDeviceAttendance = require('../../lib/digi_class/schedule-multi-device-attendance/schedule-multi-device-attendance.route');
const userGlobalSearch = require('../../lib/userGlobalSearch/userGlobalSearch.route');
const discussions = require('../../lib/digi_class/discussions/discussions_route');
const courseReport = require('../../lib/courseReport/courseReport.route');
const hebaAI = require('../../lib/hebaAI/hebaAI.route');
// const globalSessionSettings = require('../../lib/global_session_settings/global_session_settings_route');
const studentSessionSettings = require('../../lib/session_status_management/session_status_management_route');
const digiSurvey = require('../../lib/digi_class/digiSurvey/digiSurvey.route');
const userModulePermission = require('../../lib/user_module_permission/userModulePermission.route');
const tagMaster = require('../../lib/tag_master/tagMaster.route');
const digiSurveyBank = require('../../lib/digi_class/digiSurveyBank/digiSurveyBank.route');
const faceReRegister = require('../../lib/digi_class/faceRegister/faceRegister.route');
const userRegistration = require('../../lib/userRegister/userRegistration.route');
//DisciplinaryRemarks
const disciplinaryRemarksRouter = require('../../lib/disciplinary_remarks/disciplinaryRemarks.route');
const digiSurveyResponse = require('../../lib/digi_class/digiSurveyResponse/digiSurveyResponse.route');
const digiSurveyTagReports = require('../../lib/digi_class/digiSurveyTagReports/digiSurveyTagReports.route');
const automationService = require('../../lib/automationService/automationService.route');
const staffMultiSchedule = require('../../lib/digi_class/staffMultiSchedule/staffMultiSchedule.route');
const { authMiddleware, serviceAuth /* , apiLimiter */ } = require('../../middleware');
const { checkDigiChatV2Enabled } = require('../../lib/digi_class/digi_chat_v2/chat_validator');
router.use('/externalStaff', authMiddleware, external_staff);
router.use('/qapcCategoryForm_v2', authMiddleware, qapcCategoryForm_v2);
router.use('/dcExternalSync', serviceAuth, dcExternalSync);
router.use('/qapcCategory_v2', authMiddleware, qapcCategory_v2);
router.use('/qapcDashboard', authMiddleware, qapcDashboard);
router.use('/rolePermission', authMiddleware, rolePermission);
router.use('/formApprover', authMiddleware, formApprover);
router.use('/formInitiator', authMiddleware, formInitiator);
router.use('/qapcSetting_v2', authMiddleware, qapcSetting_v2);
router.use('/leader_board', authMiddleware, leader_board);
router.use('/announcement', authMiddleware, announcement);
router.use('/announcementSetting', authMiddleware, announcement_setting);
router.use('/country', authMiddleware, country);
router.use('/university', authMiddleware, university);
router.use('/institution', authMiddleware, institution);
router.use('/infrastructures', authMiddleware, infrastructure);
// router.use('/infrastructureevent', authMiddleware, infrastructure_event_route);
// router.use('/infrastructure_delivery_type', authMiddleware, infrastructure_delivery_type);
router.use('/institution_calendar', authMiddleware, institution_calendar);
router.use('/institution_calendar_event', authMiddleware, institution_calendar_event);

// router.use('/day_group', authMiddleware, day_group);
router.use('/time_group', authMiddleware, time_group);
// router.use('/college_building', authMiddleware, college_building);
// router.use('/hospital', authMiddleware, hospital);

router.use('/infrastructure_management', authMiddleware, infrastructure_management);
router.use('/lms', authMiddleware, lms);
router.use('/lms_review', authMiddleware, lms_review);
router.use('/lmsStudentSetting', authMiddleware, lmsStudentSetting);
router.use('/lmsStudent', authMiddleware, lmsStudent);
router.use('/lmsWarning', authMiddleware, lmsWarning);
router.use('/lmsDenial', authMiddleware, lmsDenial);
router.use('/lmsAttendanceConfig', authMiddleware, lmsAttendanceConfig);
router.use('/user', user);
// router.use('/user_history', authMiddleware, user_history);
router.use('/program_calendar', authMiddleware, program_calendar);
router.use('/program_calendar_event', authMiddleware, program_calendar_event);
router.use('/program_calendar_course', authMiddleware, program_calendar_course);
router.use('/role_management', authMiddleware, role_management);
router.use('/program_calendar_review', authMiddleware, program_calendar_review);
router.use('/student_leave_register', authMiddleware, student_leave_register);
router.use('/student_group', authMiddleware, student_group);
router.use('/student_group_gender_merge', authMiddleware, student_group_gender_merge);
// router.use('/student_roles', authMiddleware, student_role_route);
// router.use('/student_role_allotments', authMiddleware, student_role_allotment_route);
// router.use('/infrastructure_events', authMiddleware, infrastructure_event_route);
// router.use('/staff_committee', authMiddleware, staff_committee);
// router.use('/roles_offices', authMiddleware, roles_offices);
// router.use('/roles_permissions', authMiddleware, roles_permissions);
// router.use('/role_assign_to_staff', authMiddleware, role_assign_to_staff);
// router.use('/infrastructure_event_exam', authMiddleware, infrastructure_event_exam);
// router.use('/infrastructure_delivery_medium', authMiddleware, infrastructure_delivery_medium);
// router.use('/course_staff_allocation', authMiddleware, course_staff_allocation);
router.use('/course_schedule', authMiddleware, course_schedule);
// router.use('/course_management_setting', authMiddleware, course_management_setting);
router.use('/role', authMiddleware, role);
router.use('/module', authMiddleware, modules);
router.use('/role_assign', authMiddleware, role_assign);

//New Program Input
router.use('/digi_institute', authMiddleware, digi_institute);
router.use('/digi_program', authMiddleware, digi_program);
router.use('/digi_department_subject', authMiddleware, digi_department_subject);
router.use('/digi_session_delivery_types', authMiddleware, digi_session_delivery_types);
router.use('/digi_curriculum', authMiddleware, digi_curriculum);
router.use('/digi_college', authMiddleware, digi_college);
router.use('/digi_university', authMiddleware, digi_university);
router.use('/digi_course', authMiddleware, digi_course);
router.use('/digi_course_group', authMiddleware, digi_course_group);
router.use('/digi_course_assign', authMiddleware, digi_course_assign);
router.use('/courseVersioning', authMiddleware, courseVersioning);
//course_schedule_setting
router.use('/course_schedule_setting', authMiddleware, course_schedule_setting);
router.use('/course_coordinator', authMiddleware, course_coordinator);

//Vaccination
router.use('/vaccination', authMiddleware, vaccination);
router.use('/user_vaccination_details', authMiddleware, user_vaccination_details);

// Mapping module
router.use('/digi_framework', authMiddleware, digi_framework);
router.use('/digi_impact_mapping_type', authMiddleware, digi_impact_mapping_type);
router.use('/digi_content_mapping_type', authMiddleware, digi_content_mapping_type);
router.use('/digi_mapping', authMiddleware, digi_mapping);
router.use('/program-report-setting', authMiddleware, programReportSetting);

// cs_dashboard module
router.use('/dashboard', authMiddleware, digi_dashboard_route);

// course schedule
router.use('/course-schedule-settings/', authMiddleware, courseScheduleSettings);
router.use(
    '/schedule_events_and_support_session',
    authMiddleware,
    schedule_events_and_support_session,
);
router.use('/reports_analytics', authMiddleware, reports_analytics);
router.use('/learning-outcomes', authMiddleware, learningOutcome);
router.use('/session-report', authMiddleware, sessionReport);
router.use('/digiclass/announcement', authMiddleware, user_announcement);
router.use('/digiclass/user', class_user);
router.use('/sessions', authMiddleware, sessions);
router.use('/digiclass/notification', authMiddleware, notification_manager);
router.use('/digiclass/course_session', authMiddleware, course_session);
router.use('/digiclass/document', authMiddleware, document_manager);
router.use('/digiclass/course-admin', authMiddleware, course_admin);
router.use('/activities', authMiddleware, activities);
router.use('/activities_v2', authMiddleware, activities_v2);
router.use('/taxonomy', authMiddleware, taxonomy);
router.use('/digiclass/dashboard', authMiddleware, dashboard);
router.use('/digiclass/learning-outcomes', authMiddleware, learningOutcomes);
router.use('/digiclass/reports-analytics', authMiddleware, dc_reports_analytics);
router.use('/digiclass/appVersion', appVersion);
router.use('/digiclass/digichat', authMiddleware, digi_chat);
router.use('/digiclass/digichat_v2', [authMiddleware, checkDigiChatV2Enabled], digi_chat_v2);
// router.use('/label', label);
router.use('/digiclass/survey', authMiddleware, survey);
router.use('/studentCriteria', authMiddleware, studentCriteria);
router.use('/digiclass/notificationService', serviceAuth, notificationManagerService);
router.use('/digiclass/schedule-attendance', authMiddleware, scheduleAttendance);
// router.use('/emailSettings', authMiddleware, emailSettings);
router.use(
    '/digiclass/schedule-multi-device-attendance',
    authMiddleware,
    scheduleMultiDeviceAttendance,
);
router.use('/global-session-settings', authMiddleware, globalSessionSettings);
router.use('/userGlobalSearch', authMiddleware, userGlobalSearch);
router.use('/digiclass/discussions', authMiddleware, discussions);
router.use('/digiclass/faceReRegister', authMiddleware, faceReRegister);
router.use('/courseReport', /* apiLimiter, */ serviceAuth, courseReport);
router.use('/hebaAI', /* apiLimiter, */ serviceAuth, hebaAI);
router.use('/session-status-management', authMiddleware, studentSessionSettings);
router.use('/digiSurvey', authMiddleware, digiSurvey);
router.use('/userModulePermission', authMiddleware, userModulePermission);
router.use('/globalConfiguration/tagMaster', authMiddleware, tagMaster);
router.use('/digiclass/digiSurveyBank', authMiddleware, digiSurveyBank);
router.use('/digiclass/disciplinary_remarks', authMiddleware, disciplinaryRemarksRouter);
router.use('/digiclass/digiSurveyResponse', authMiddleware, digiSurveyResponse);
router.use('/digiclass/digiSurveyTagReports', authMiddleware, digiSurveyTagReports);
router.use('/userRegistration', /*authMiddleware ,*/ userRegistration);
router.use('/automationService', authMiddleware, automationService);
router.use('/digiclass/staffMultiSchedule', authMiddleware, staffMultiSchedule);
router.use(
    '/digiclass/courseSettings',
    authMiddleware,
    require('../../lib/courseSettings/courseWiseSetting.route'),
);
module.exports = {
    v1Routes: router,
};
