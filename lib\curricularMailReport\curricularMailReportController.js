const curricularMailReport = require('../models/curricularMailReport');
const { redisClient } = require('../../config/redis-connection');
const { logger } = require('../utility/util_keys');
const roleAssign = require('../models/role_assign');
const { convertToMongoObjectId } = require('../utility/common');
const {
    CURRICULAR_MAIL_REPORT,
    DS_UPDATE,
    PM,
    AM,
    DAYS_MODE,
    REDIS_CRON_EVENTS,
} = require('../utility/constants');

const SCHEDULE_PROJECTION = {
    _id: 1,
    userId: 1,
    userEmail: 1,
    deliveryFrequency: 1,
    sendTime: 1,
    dayOfWeek: 1,
    isActive: 1,
};

const updateRedisSchedule = async (schedule) => {
    try {
        const redisKey = `${CURRICULAR_MAIL_REPORT}:${schedule._id}`;
        const scheduleData = {
            id: schedule._id.toString(),
            userId: schedule.userId,
            deliveryFrequency: schedule.deliveryFrequency,
            sendTime: schedule.sendTime,
            dayOfWeek: schedule.dayOfWeek,
            isActive: schedule.isActive,
        };

        await redisClient.Client.set(redisKey, JSON.stringify(scheduleData));

        redisClient.Client.duplicate().publish(
            REDIS_CRON_EVENTS,
            JSON.stringify({
                type: DS_UPDATE,
                job: scheduleData,
                jobId: scheduleData.id,
            }),
        );

        const now = new Date();
        const [timeStr, meridian] = schedule.sendTime.split(' ');
        const [hourStr, minuteStr] = timeStr.split(':');
        let hour = parseInt(hourStr);
        const minute = parseInt(minuteStr);

        if (meridian === PM && hour < 12) hour += 12;
        if (meridian === AM && hour === 12) hour = 0;

        const nextSendTime = new Date(now);
        nextSendTime.setHours(hour, minute, 0, 0);

        if (schedule.deliveryFrequency === DAYS_MODE.WEEKLY && schedule.dayOfWeek) {
            const dayMap = {
                sunday: 0,
                monday: 1,
                tuesday: 2,
                wednesday: 3,
                thursday: 4,
                friday: 5,
                saturday: 6,
            };

            const targetDay = dayMap[schedule.dayOfWeek.toLowerCase()];
            if (targetDay !== undefined) {
                const currentDay = now.getDay();
                let daysToAdd = targetDay - currentDay;

                if (daysToAdd <= 0 && nextSendTime <= now) {
                    daysToAdd += 7;
                }

                nextSendTime.setDate(nextSendTime.getDate() + daysToAdd);
            }
        } else {
            if (nextSendTime <= now) {
                nextSendTime.setDate(nextSendTime.getDate() + 1);
            }
        }

        const ttlSeconds = Math.floor((nextSendTime - now) / 1000) - 20;

        await redisClient.Client.expire(redisKey, ttlSeconds);
    } catch (error) {
        logger.error('Error updating Redis schedule:', error);
        throw error;
    }
};

const batchUpdateRedisSchedules = async (schedules) => {
    try {
        const pipeline = redisClient.Client.pipeline();
        const publishPromises = [];

        for (const scheduleElement of schedules) {
            const redisKey = `${CURRICULAR_MAIL_REPORT}:${scheduleElement._id}`;
            const scheduleData = {
                id: scheduleElement._id.toString(),
                userId: scheduleElement.userId,
                deliveryFrequency: scheduleElement.deliveryFrequency,
                sendTime: scheduleElement.sendTime,
                dayOfWeek: scheduleElement.dayOfWeek,
                isActive: scheduleElement.isActive,
            };

            pipeline.set(redisKey, JSON.stringify(scheduleData));

            const now = new Date();
            const [timeStr, meridian] = scheduleElement.sendTime.split(' ');
            const [hourStr, minuteStr] = timeStr.split(':');
            let hour = parseInt(hourStr);
            const minute = parseInt(minuteStr);

            if (meridian === PM && hour < 12) hour += 12;
            if (meridian === AM && hour === 12) hour = 0;

            const nextSendTime = new Date(now);
            nextSendTime.setHours(hour, minute, 0, 0);

            if (
                scheduleElement.deliveryFrequency === DAYS_MODE.WEEKLY &&
                scheduleElement.dayOfWeek
            ) {
                const dayMap = {
                    sunday: 0,
                    monday: 1,
                    tuesday: 2,
                    wednesday: 3,
                    thursday: 4,
                    friday: 5,
                    saturday: 6,
                };
                const targetDay = dayMap[scheduleElement.dayOfWeek.toLowerCase()];
                if (targetDay !== undefined) {
                    const currentDay = now.getDay();
                    let daysToAdd = targetDay - currentDay;
                    if (daysToAdd <= 0 && nextSendTime <= now) {
                        daysToAdd += 7;
                    }
                    nextSendTime.setDate(nextSendTime.getDate() + daysToAdd);
                }
            } else {
                if (nextSendTime <= now) {
                    nextSendTime.setDate(nextSendTime.getDate() + 1);
                }
            }

            const ttlSeconds = Math.floor((nextSendTime - now) / 1000) - 20;
            pipeline.expire(redisKey, ttlSeconds);

            publishPromises.push(
                redisClient.Client.duplicate().publish(
                    REDIS_CRON_EVENTS,
                    JSON.stringify({
                        type: DS_UPDATE,
                        job: scheduleData,
                        jobId: scheduleData.id,
                    }),
                ),
            );
        }

        await pipeline.exec();

        await Promise.all(publishPromises);
    } catch (error) {
        logger.error('Error batch updating Redis schedules:', error);
        throw error;
    }
};

const removeRedisSchedule = async (scheduleId) => {
    try {
        const actualRedisKey = `${CURRICULAR_MAIL_REPORT}:${scheduleId}`;
        await redisClient.Client.del(actualRedisKey);

        const deleteMarkerKey = `${CURRICULAR_MAIL_REPORT}:delete:${scheduleId}`;
        await redisClient.Client.set(deleteMarkerKey, JSON.stringify(scheduleId), 'EX', 2);

        logger.info(`Redis schedule deleted for: ${scheduleId}`);
    } catch (error) {
        logger.error('Error removing Redis schedule:', error);
        throw error;
    }
};

const createSchedule = async ({ headers = {}, body = {} }) => {
    try {
        const { userId, deliveryFrequency, sendTime, dayOfWeek, userEmail } = body;
        const { user_id } = headers;
        const scheduleData = {
            userId,
            deliveryFrequency,
            sendTime,
            dayOfWeek,
            userEmail,
            createdBy: convertToMongoObjectId(user_id),
            isActive: true,
            isDeleted: false,
        };
        const existingSchedule = await curricularMailReport.findOne(
            {
                userId: scheduleData.userId,
                isDeleted: false,
            },
            SCHEDULE_PROJECTION,
        );
        if (existingSchedule) {
            const updatedSchedule = await curricularMailReport
                .findByIdAndUpdate(
                    existingSchedule._id,
                    {
                        ...scheduleData,
                        updatedAt: new Date(),
                    },
                    { new: true, runValidators: true },
                )
                .select(SCHEDULE_PROJECTION);

            await updateRedisSchedule(updatedSchedule);
            logger.info(`Report schedule updated for user: ${scheduleData.userId}`);
            return {
                statusCode: 200,
                message: 'Report schedule updated successfully',
                data: updatedSchedule,
            };
        }
        const schedule = await curricularMailReport.create(scheduleData);
        const scheduleReturnData = await curricularMailReport.findById(
            schedule._id,
            SCHEDULE_PROJECTION,
        );
        await updateRedisSchedule(schedule);
        logger.info(`Report schedule created for user: ${scheduleData.userId}`);
        return {
            statusCode: 201,
            message: 'Report schedule created successfully',
            data: scheduleReturnData,
        };
    } catch (error) {
        throw new Error('Internal server error');
    }
};

const getAllSchedules = async () => {
    try {
        const schedules = await curricularMailReport.find(
            {
                isDeleted: false,
            },
            SCHEDULE_PROJECTION,
        );
        return {
            statusCode: 200,
            message: 'Schedules retrieved successfully',
            data: schedules,
        };
    } catch (error) {
        logger.error('Error fetching report schedules:', error);
        throw new Error('Internal server error');
    }
};

const deleteSchedule = async ({ query = {} }) => {
    try {
        const { id } = query;
        const schedule = await curricularMailReport
            .findByIdAndUpdate(id, { isDeleted: true, isActive: false }, { new: true })
            .select(SCHEDULE_PROJECTION);

        if (!schedule) {
            const error = new Error('Report schedule not found');
            error.status = 404;
            throw error;
        }

        await removeRedisSchedule(schedule._id.toString());

        logger.info(`Report schedule deleted for user: ${schedule.userId}`);

        return {
            statusCode: 200,
            message: 'Report schedule deleted successfully',
        };
    } catch (error) {
        logger.error('Error deleting report schedule:', error);
        throw error;
    }
};

const bulkUpdateSchedules = async ({ headers = {}, body = {} }) => {
    try {
        const { user_id } = headers;
        const { schedules } = body;
        const userIds = schedules.map((schedule) => convertToMongoObjectId(schedule.userId));
        const existingSchedules = await curricularMailReport
            .find(
                {
                    userId: { $in: userIds },
                    isDeleted: false,
                },
                SCHEDULE_PROJECTION,
            )
            .lean();
        const existingSchedulesMap = new Map(
            existingSchedules.map((schedule) => [schedule.userId.toString(), schedule]),
        );
        const bulkOps = [];
        for (const scheduleDataElement of schedules) {
            const scheduleUserId = convertToMongoObjectId(scheduleDataElement.userId);
            const existingSchedule = existingSchedulesMap.get(scheduleUserId.toString());

            if (existingSchedule) {
                bulkOps.push({
                    updateOne: {
                        filter: { _id: existingSchedule._id },
                        update: {
                            ...scheduleDataElement,
                            updatedAt: new Date(),
                        },
                    },
                });
            } else {
                bulkOps.push({
                    insertOne: {
                        document: {
                            ...scheduleDataElement,
                            createdBy: convertToMongoObjectId(user_id),
                            isActive: true,
                            isDeleted: false,
                        },
                    },
                });
            }
        }

        const bulkResult = await curricularMailReport.bulkWrite(bulkOps, { ordered: false });
        const updatedSchedules = await curricularMailReport
            .find(
                {
                    userId: { $in: userIds },
                    isDeleted: false,
                },
                SCHEDULE_PROJECTION,
            )
            .sort({ createdAt: -1 });
        if (updatedSchedules.length > 0) {
            await batchUpdateRedisSchedules(updatedSchedules);
        }
        const updatedUserIds = existingSchedules.map((schedule) => schedule.userId.toString());
        const createdSchedules = updatedSchedules.filter(
            (schedule) => !updatedUserIds.includes(schedule.userId.toString()),
        );
        const modifiedSchedules = updatedSchedules.filter((schedule) =>
            updatedUserIds.includes(schedule.userId.toString()),
        );

        return {
            statusCode: 200,
            message: 'Bulk update completed successfully',
            data: {
                insertedCount: bulkResult.insertedCount,
                modifiedCount: bulkResult.modifiedCount,
                totalSchedules: updatedSchedules.length,
                createdSchedules: createdSchedules.length,
                updatedSchedules: modifiedSchedules.length,
                schedules: updatedSchedules,
            },
        };
    } catch (error) {
        logger.error('Error in bulk update:', error);
        throw new Error('Internal server error');
    }
};

const getAdminStaffList = async () => {
    try {
        const filter = {
            isDeleted: false,
            isActive: true,
            'roles.isAdmin': true,
        };

        const projection = {
            _user_id: 1,
            user_name: 1,
            roles: 1,
        };

        const staffList = await roleAssign
            .find(filter, projection)
            .populate({
                path: '_user_id',
                select: { _id: 1, user_id: 1, gender: 1, email: 1, name: 1 },
                match: { isDeleted: { $ne: true } },
            })
            .lean();

        const adminStaffList = staffList
            .filter((staffElement) => staffElement._user_id)
            .map((staffElement) => {
                const user = staffElement._user_id;
                const adminRoles = staffElement.roles?.filter((role) => role.isAdmin);

                const staffName = staffElement.user_name
                    ? [
                          staffElement.user_name.first,
                          staffElement.user_name.middle,
                          staffElement.user_name.last,
                      ]
                          .filter(Boolean)
                          .join(' ')
                    : '';

                return {
                    id: user._id,
                    academicId: user.user_id || '',
                    gender: user.gender || '',
                    staff_name: staffName,
                    adminRoles: adminRoles.map((role) => ({
                        roleId: role._role_id,
                        roleName: role.role_name,
                        isDefault: role.isDefault,
                    })),
                    email: user.email || '',
                };
            });

        return {
            statusCode: 200,
            message: 'Admin staff list retrieved successfully',
            data: {
                adminStaffList,
                count: adminStaffList.length,
            },
        };
    } catch (error) {
        logger.error('Error fetching admin staff list:', error);
        throw new Error('Internal server error');
    }
};

module.exports = {
    createSchedule,
    getAllSchedules,
    deleteSchedule,
    bulkUpdateSchedules,
    getAdminStaffList,
};
