const router = require('express').Router();
const { validate } = require('../../common/middlewares/validation');
const catchAsync = require('../../../lib/utility/catch-async');
const StudentResponseController = require('./student-response.controller');
const {
    startFormSchema,
    updateStudentResponseSchema,
    submitFormSchema,
    getStudentResponseSchema,
    uploadStudentAttachmentSchema,
    deleteStudentAttachmentSchema,
    validateChildIdSchema,
    uploadSimilarityCheckAttachmentSchema,
    deleteSimilarityCheckAttachmentSchema,
    updateSimilarityCheckSchema,
    addReviewSchema,
    getReviewSchema,
    updateReviewSchema,
    addExternalEvaluatorSchema,
    verifyVerificationCodeSchema,
    updateFacialRecognitionSchema,
    getStudentDetailsSchema,
} = require('./student-response.validation');

const {
    getInsightsWithRubricsAndMarksSchema,
    updateEvaluatorSchema,
    updateApproveOrRejectStatusSchema,
} = require('../evaluator/evaluator.validation');

router.post('/start', validate(startFormSchema), catchAsync(StudentResponseController.startForm));

router.put(
    '/response',
    validate(updateStudentResponseSchema),
    catchAsync(StudentResponseController.updateStudentResponse),
);

router.put('/submit', validate(submitFormSchema), catchAsync(StudentResponseController.submitForm));

router.get(
    '/response',
    validate(getStudentResponseSchema),
    catchAsync(StudentResponseController.getStudentResponse),
);

router.post(
    '/attachment',
    validate(uploadStudentAttachmentSchema),
    catchAsync(StudentResponseController.uploadStudentAttachment),
);

router.delete(
    '/attachment',
    validate(deleteStudentAttachmentSchema),
    catchAsync(StudentResponseController.deleteStudentAttachment),
);

router.post(
    '/similarity-check-attachment',
    validate(uploadSimilarityCheckAttachmentSchema),
    catchAsync(StudentResponseController.uploadSimilarityCheckAttachment),
);

router.delete(
    '/similarity-check-attachment',
    validate(deleteSimilarityCheckAttachmentSchema),
    catchAsync(StudentResponseController.deleteSimilarityCheckAttachment),
);

router.get(
    '/evaluator',
    validate(validateChildIdSchema),
    catchAsync(StudentResponseController.getEvaluatorAndApproverForStudent),
);

router.put(
    '/similarity-check',
    validate(updateSimilarityCheckSchema),
    catchAsync(StudentResponseController.updateSimilarityCheck),
);

router.post('/review', validate(addReviewSchema), catchAsync(StudentResponseController.addReview));

router.get('/review', validate(getReviewSchema), catchAsync(StudentResponseController.getReview));

router.put(
    '/review-update-and-delete',
    validate(updateReviewSchema),
    catchAsync(StudentResponseController.updateAndDeleteReview),
);

router.put(
    '/external-evaluator',
    validate(addExternalEvaluatorSchema),
    catchAsync(StudentResponseController.addExternalEvaluator),
);

router.put(
    '/resend-verification-code',
    validate(verifyVerificationCodeSchema),
    catchAsync(StudentResponseController.reSendVerificationCode),
);

router.put(
    '/verify-verification-code',
    validate(verifyVerificationCodeSchema),
    catchAsync(StudentResponseController.verifyVerificationCode),
);

router.get(
    '/evaluation/insights',
    validate(getInsightsWithRubricsAndMarksSchema),
    catchAsync(StudentResponseController.getInsightsWithRubricsAndMarks),
);

router.put(
    '/evaluation/marks-and-rubrics',
    validate(updateEvaluatorSchema),
    catchAsync(StudentResponseController.updateStudentMarksAndRubrics),
);

router.put(
    '/facial',
    validate(updateFacialRecognitionSchema),
    catchAsync(StudentResponseController.updateFacialRecognition),
);

router.put(
    '/evaluation/approve-or-reject',
    validate(updateApproveOrRejectStatusSchema),
    catchAsync(StudentResponseController.updateApproveOrRejectStatus),
);

router.get(
    '/student-details',
    validate(getStudentDetailsSchema),
    catchAsync(StudentResponseController.getStudentDetails),
);

module.exports = router;
