const courseScheduleSchema = require('../../models/course_schedule');
const activitySchema = require('../../models/activities');
const courseScheduleSettingSchema = require('../../models/course_schedule_setting');
const { convertToMongoObjectId } = require('../../utility/common');
const { DC_STAFF, SCHEDULE, DRAFT, ACTIVITIES } = require('../../utility/constants');
const { getGroupName } = require('../../utility/common_functions');

exports.getDateBasedScheduleList = async ({ query = {}, headers = {} }) => {
    try {
        const { userId, startDate, endDate, userType } = query;
        const { _institution_id } = headers;
        const userObjectId = convertToMongoObjectId(userId);
        const isCountNeed = query.isCountNeed === 'true';
        const start = new Date(startDate);
        start.setHours(0, 0, 0, 0);
        const end = new Date(endDate);
        end.setHours(23, 59, 59, 999);
        const courseScheduleQuery = {
            isDeleted: false,
            scheduleStartDateAndTime: { $gte: start, $lte: end },
            scheduleEndDateAndTime: { $gte: start, $lte: end },
            ...(userType === DC_STAFF
                ? {
                      $or: [
                          { 'staffs._staff_id': userObjectId },
                          { 'attendanceTakingStaff.staffId': userObjectId },
                      ],
                  }
                : { 'students._id': userObjectId }),
        };

        const courseScheduleProject = isCountNeed
            ? { _id: 1, scheduleStartDateAndTime: 1 }
            : {
                  type: 1,
                  program_name: 1,
                  course_name: 1,
                  course_code: 1,
                  term: 1,
                  year_no: 1,
                  level_no: 1,
                  mode: 1,
                  schedule_date: 1,
                  status: 1,
                  scheduleStartDateAndTime: 1,
                  scheduleEndDateAndTime: 1,
                  classModeType: 1,
                  merge_status: 1,
                  'merge_with.schedule_id': 1,
                  session: 1,
                  _program_id: 1,
                  title: 1,
                  'student_groups.session_group.group_name': 1,
                  'student_groups.group_name': 1,
                  _student_group_id: 1,
                  sub_type: 1,
              };

        const schedules = await courseScheduleSchema
            .find(courseScheduleQuery, courseScheduleProject)
            .lean();

        const programIds = [];
        schedules.forEach((scheduleElement) => {
            programIds.push(scheduleElement._program_id);
            scheduleElement.studentGroupName = getGroupName({
                groupName: scheduleElement.student_groups,
            });
        });

        const activityQuery = {
            isDeleted: false,
            status: { $ne: DRAFT },
            type: SCHEDULE,
            'schedule.startDateAndTime': { $gte: start, $lte: end },
            'schedule.endDateAndTime': { $gte: start, $lte: end },
        };

        const baseProject = {
            schedule: 1,
            name: 1,
            quizType: 1,
            status: 1,
            year_no: 1,
            level_no: 1,
            term: 1,
            rotation: 1,
            type: 1,
            sessionId: 1,
            _institution_calendar_id: 1,
            scheduleIds: 1,
            isNewQuiz: 1,
        };

        const activityProject = isCountNeed
            ? { schedule: 1, scheduleIds: 1 }
            : {
                  ...baseProject,
                  courseId: 1,
                  course_name: '$courseData.course_name',
                  course_code: '$courseData.course_code',
                  _program_id: 1,
                  program_name: '$programData.name',
                  totalStudentAnsweredCount: {
                      $size: { $ifNull: ['$studentCompletedQuiz', []] },
                  },
                  totalStudentCount: {
                      $sum: {
                          $map: {
                              input: '$scheduleData',
                              as: 'course_schedules',
                              in: { $size: '$$course_schedules.students' },
                          },
                      },
                  },
                  totalQuestion: {
                      $size: { $ifNull: ['$questions', []] },
                  },
              };

        const scheduledactivity = await activitySchema.aggregate([
            { $match: activityQuery },
            {
                $lookup: {
                    from: 'digi_courses',
                    localField: 'courseId',
                    foreignField: '_id',
                    as: 'courseData',
                },
            },
            { $unwind: { path: '$courseData', preserveNullAndEmptyArrays: true } },
            {
                $lookup: {
                    from: 'digi_programs',
                    localField: '_program_id',
                    foreignField: '_id',
                    as: 'programData',
                },
            },
            { $unwind: { path: '$programData', preserveNullAndEmptyArrays: true } },
            {
                $lookup: {
                    from: 'course_schedules',
                    localField: 'scheduleIds',
                    foreignField: '_id',
                    as: 'scheduleData',
                },
            },
            { $project: activityProject },
        ]);
        let filteredActivities = [];
        if (scheduledactivity.length) {
            const activityScheduleIds = scheduledactivity.flatMap(
                (activityElement) => activityElement.scheduleIds || [],
            );

            const matchingSchedules = await courseScheduleSchema
                .find(
                    {
                        _id: { $in: activityScheduleIds },
                        $or: [
                            { 'staffs._staff_id': userObjectId },
                            { 'students._id': userObjectId },
                        ],
                    },
                    { _id: 1 },
                )
                .lean();

            const matchedScheduleIdSet = new Set(matchingSchedules.map(({ _id }) => String(_id)));

            filteredActivities = scheduledactivity.filter((activity) =>
                (activity.scheduleIds || []).some((id) => matchedScheduleIdSet.has(String(id))),
            );
        }

        if (!schedules.length && !filteredActivities.length) {
            return { statusCode: 200, data: [] };
        }

        const scheduleSettingQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            isActive: true,
            isDeleted: false,
            'programs._program_id': {
                $in: programIds,
            },
        };

        const courseScheduleSetting = await courseScheduleSettingSchema
            .findOne(scheduleSettingQuery, {
                'programs.extraCurricularAndBreakTiming': 1,
                'programs._program_id.$': 1,
            })
            .lean();

        const breakSchedules = [];
        courseScheduleSetting?.programs.forEach(({ extraCurricularAndBreakTiming }) => {
            extraCurricularAndBreakTiming.forEach((breakElement) => {
                if (breakElement.isActive && !breakElement.isDeleted) {
                    breakSchedules.push({
                        days: breakElement.days,
                        startTime: breakElement.startTime,
                        endTime: breakElement.endTime,
                        title: breakElement.title,
                        gender: breakElement.gender,
                        type: breakElement.type,
                    });
                }
            });
        });

        if (isCountNeed) {
            const scheduleCounts = [];

            schedules.forEach(({ scheduleStartDateAndTime }) => {
                const day = new Date(scheduleStartDateAndTime).toISOString().split('T')[0];
                const matchedDate = scheduleCounts.find(({ date = '' }) => date === day);
                if (matchedDate) {
                    matchedDate.count += 1;
                } else {
                    scheduleCounts.push({ date: day, count: 1 });
                }
            });

            if (filteredActivities.length) {
                filteredActivities.forEach((activityElement) => {
                    const day = new Date(activityElement.schedule.startDateAndTime)
                        .toISOString()
                        .split('T')[0];
                    const matchedDate = scheduleCounts.find(({ date = '' }) => date === day);
                    if (matchedDate) {
                        matchedDate.count += 1;
                    } else {
                        scheduleCounts.push({ date: day, count: 1 });
                    }
                });
            }

            return { statusCode: 200, data: scheduleCounts };
        }

        const mergedSchedules = schedules.filter((scheduleElement) => scheduleElement.merge_status);

        const updatedSchedule = [
            ...breakSchedules,
            ...filteredActivities.map((activityElement) => ({
                ...activityElement,
                type: ACTIVITIES,
            })),
            ...schedules.filter((scheduleElement) => !scheduleElement.merge_status),
        ];

        const mergeScheduleIds = [];
        mergedSchedules.forEach((scheduleElement) => {
            if (!mergeScheduleIds.includes(scheduleElement._id.toString())) {
                const scheduleIds = scheduleElement.merge_with.map(({ schedule_id }) =>
                    schedule_id.toString(),
                );
                mergeScheduleIds.push(...scheduleIds);
                updatedSchedule.push(scheduleElement);
            }
        });

        return { statusCode: 200, data: updatedSchedule };
    } catch (error) {
        throw error instanceof Error ? error : new Error(String(error));
    }
};

const getSchedule = async ({
    userId,
    userType,
    scheduleId,
    programId,
    yearNo,
    levelNo,
    term,
    type,
}) => {
    try {
        const courseScheduleQuery = {
            _id: convertToMongoObjectId(scheduleId),
            _program_id: convertToMongoObjectId(programId),
            year_no: yearNo,
            level_no: levelNo,
            term,
            type,
            ...(userType === DC_STAFF
                ? {
                      $or: [
                          { 'staffs._staff_id': convertToMongoObjectId(userId) },
                          {
                              'attendanceTakingStaff.staffId': convertToMongoObjectId(userId),
                          },
                      ],
                  }
                : { 'students._id': convertToMongoObjectId(userId) }),
        };
        const courseProject = {
            _course_id: 1,
            session: 1,
            _student_group_id: 1,
            'student_groups.group_name': 1,
            'student_groups.group_id': 1,
            'student_groups.session_group.group_name': 1,
            'student_groups.session_group.session_group_id': 1,
            year_no: 1,
            level_no: 1,
            _program_id: 1,
            schedule_date: 1,
            mode: 1,
            'subjects._subject_id': 1,
            'subjects.subject_name': 1,
            infra_name: 1,
            'staffs._staff_id': 1,
            'staffs.staff_name': 1,
            status: 1,
            'students._id': 1,
            _institution_calendar_id: 1,
            rotation: 1,
            rotation_count: 1,
            program_name: 1,
            course_name: 1,
            course_code: 1,
            type: 1,
            title: 1,
            merge_status: 1,
            merge_with: 1,
            topic: 1,
            sub_type: 1,
            term: 1,
            scheduleStartDateAndTime: 1,
            scheduleEndDateAndTime: 1,
            remotePlatform: 1,
            classModeType: 1,
            _infra_id: {
                _id: '$_infra_id',
            },
        };
        return await courseScheduleSchema.findOne(courseScheduleQuery, courseProject).lean();
    } catch (error) {
        throw error instanceof Error ? error : new Error(String(error));
    }
};

exports.getSingleSchedule = async ({ query = {}, headers = {} }) => {
    try {
        const { scheduleId, programId, yearNo, levelNo, term, type, userType } = query;
        const { _user_id } = headers;

        const schedule = await getSchedule({
            userId: _user_id,
            userType,
            scheduleId,
            programId,
            yearNo,
            levelNo,
            term,
            type,
        });

        return { statusCode: 200, data: schedule };
    } catch (error) {
        throw error instanceof Error ? error : new Error(String(error));
    }
};
