const { Joi } = require('../../common/middlewares/validation');
const { objectIdRQSchema } = require('../../../lib/utility/validationSchemas');

const getPortfolioSchema = Joi.object({
    query: Joi.object({
        programId: objectIdRQSchema,
        courseId: objectIdRQSchema,
        institutionCalendarId: objectIdRQSchema,
    }),
}).unknown(true);

const updatePortfolioSchema = Joi.object({
    query: Joi.object({
        portfolioId: objectIdRQSchema,
    }),
    body: Joi.object({
        components: Joi.array().required(),
        totalMarks: Joi.number().required(),
    }),
}).unknown(true);

const deletePortfolioSchema = Joi.object({
    query: Joi.object({
        portfolioId: objectIdRQSchema,
    }),
}).unknown(true);

const publishPortfolioSchema = Joi.object({
    query: Joi.object({
        portfolioId: objectIdRQSchema,
    }),
});

const getComponentFormSchema = Joi.object({
    query: Joi.object({
        formId: objectIdRQSchema,
    }),
}).unknown(true);

const assignStudentSchema = Joi.object({
    body: Joi.object({
        programId: objectIdRQSchema,
        courseId: objectIdRQSchema,
        institutionCalendarId: objectIdRQSchema,
        portfolioId: objectIdRQSchema,
        componentId: objectIdRQSchema,
        childrenId: objectIdRQSchema,
        students: Joi.array().min(1).required(),
    }),
}).unknown(true);

const getStudentsByCourseSchema = Joi.object({
    query: Joi.object({
        programId: objectIdRQSchema,
        year: Joi.string().required(),
        level: Joi.string().required(),
        rotation: Joi.string().optional(),
        rotationCount: Joi.string().optional(),
        term: Joi.string().required(),
        courseId: objectIdRQSchema,
        institutionCalendarId: objectIdRQSchema,
    }),
}).unknown(true);

const updateStudentReviewSchema = Joi.object({
    query: Joi.object({
        componentId: objectIdRQSchema,
        childrenId: objectIdRQSchema,
        studentId: objectIdRQSchema,
        userId: objectIdRQSchema,
    }),
    body: Joi.object({
        text: Joi.string().required(),
    }),
}).unknown(true);

const getPortfolioForAssignSchema = Joi.object({
    query: Joi.object({
        programId: objectIdRQSchema,
        courseId: objectIdRQSchema,
        institutionCalendarId: objectIdRQSchema,
    }),
}).unknown(true);

const validatePortfolioChildSchema = Joi.object({
    query: Joi.object({
        portfolioId: objectIdRQSchema,
        componentId: objectIdRQSchema,
        childrenId: objectIdRQSchema,
    }),
}).unknown(true);

const updateFormInPortfolioSchema = Joi.object({
    body: Joi.object({
        title: Joi.string().optional(),
        type: Joi.object().optional(),
        pages: Joi.array().optional(),
    }),
}).unknown(true);

const validateGetInsight = Joi.object({
    query: Joi.object({
        programId: objectIdRQSchema,
        institutionCalendarId: objectIdRQSchema,
    }),
}).unknown(true);

module.exports = {
    getPortfolioSchema,
    updatePortfolioSchema,
    deletePortfolioSchema,
    publishPortfolioSchema,
    getComponentFormSchema,
    assignStudentSchema,
    getStudentsByCourseSchema,
    updateStudentReviewSchema,
    getPortfolioForAssignSchema,
    validatePortfolioChildSchema,
    updateFormInPortfolioSchema,
    validateGetInsight,
};
