const {
    Schema,
    model,
    Types: { ObjectId },
} = require('mongoose');
const {
    COURSE_SCHEDULE_MULTI_SCHEDULE_ANALYSES,
    INSTITUTION_CALENDAR,
    DIGI_PROGRAM,
    DIGI_COURSE,
    INSTITUTION,
} = require('../utility/constants');

const courseScheduleMultiScheduleAnalysesSchema = new Schema(
    {
        _institution_id: { type: ObjectId, ref: INSTITUTION },
        institutionCalendarId: { type: ObjectId, ref: INSTITUTION_CALENDAR },
        programId: { type: ObjectId, ref: DIGI_PROGRAM },
        courseId: { type: ObjectId, ref: DIGI_COURSE },
        yearNo: { type: String },
        levelNo: { type: String },
        term: { type: String },
        multiScheduleRequest: { type: Object },
        scheduleList: [{ type: Object }],
    },
    {
        timestamps: true,
    },
);
module.exports = model(
    COURSE_SCHEDULE_MULTI_SCHEDULE_ANALYSES,
    courseScheduleMultiScheduleAnalysesSchema,
);
