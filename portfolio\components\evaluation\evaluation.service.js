const UserModel = require('../../../lib/models/user');
const PortfolioModel = require('../portfolio/portfolio.model');
const StudentResponseModel = require('../student-response/student-response.model');
const StudentPortfolioModel = require('../portfolio/student-portfolio.model');
const CourseScheduleModel = require('../../../lib/models/course_schedule');
const ProgramModel = require('../../../lib/models/digi_programs');
const CourseModel = require('../../../lib/models/digi_course');
const FormModel = require('../form/form.model');
const EvaluationMappingModel = require('./evaluation-mapping.model');
const InfrastructureManagementModel = require('../../../lib/models/infrastructure_management');
const VerificationModel = require('./verification.model');
const constant = require('../../../lib/utility/constants');

const { BadRequestError } = require('../../common/utils/api_error_util');
const { convertToMongoObjectId, isIDEquals, isNumber } = require('../../common/utils/common.util');
const {
    LB,
    NOT_STARTED,
    PUBLISHED,
    REJECTED,
    APPROVED,
    COMPLETED,
    ON_GOING,
} = require('../../common/utils/enums');
const {
    getStudentListFromStudentGroup,
} = require('../../../lib/digi_class/course_session/course_session_service');
const { getFacultyListByCourse } = require('../course/course.service');
const {
    calculateStudentAchievedPoints,
    calculateGlobalRubricPoint,
} = require('../rubric/rubric.helper');
const {
    calculateStudentTotalMarks,
    buildAssignEvaluationQuery,
    generate6DigitCode,
    generateVerificationEmailHTML,
} = require('./evaluation.helper');
const {
    ABSENT,
    PRESENT,
    REGULAR,
    ON_DUTY,
    LEAVE,
    PERMISSION,
    INFRASTRUCTURE,
    STAFF,
    STUDENT_GROUP,
    MALE_LABEL,
    MALE,
    FEMALE,
    STUDENT,
    STUDENT_LIST,
} = require('../../common/utils/constants');
const { send_email: sendEmail } = require('../../../lib/utility/common_functions');
const { _generateToken } = require('../../../lib/utility/token.util');
const { v4: uuidv4 } = require('uuid');
const { VERIFICATION_EXPIRY_SECONDS } = require('../../../lib/utility/util_keys');
const { getAssignedUserSections } = require('../form/form.helper');

const assignEvaluator = async ({
    portfolioId,
    componentId,
    childrenId,
    infrastructureId,
    deliveryTypes = [],
    deliveryType = {
        deliveryTypeId: '',
        deliveryTypeSymbol: '',
        deliveryTypeName: '',
        sessionId: '',
    },
    roles = [],
    typeOfEvaluation,
    students = [],
    userId,
    groups = [],
    prepareAndPublish = [],
    session = {},
}) => {
    const portfolio = await PortfolioModel.findOne(
        { _id: portfolioId },
        {
            'components.code': 1,
            'components.deliveryTypes': 1,
            'components._id': 1,
            'components.children._id': 1,
            'components.children.roles': 1,
            programId: 1,
            courseId: 1,
            institutionCalendarId: 1,
        },
    ).lean();
    if (!portfolio) throw new BadRequestError('PORTFOLIO_NOT_FOUND');

    const matchedComponent = portfolio.components?.find((component) =>
        isIDEquals(component._id, componentId),
    );

    if (typeOfEvaluation === INFRASTRUCTURE) {
        if (!matchedComponent || matchedComponent.code !== LB) {
            throw new BadRequestError('INFRASTRUCTURE_ONLY_FOR_LOGBOOK_COMPONENT');
        }
    }

    const matchedChildren = matchedComponent?.children?.find((child) =>
        isIDEquals(child._id, childrenId),
    );

    roles.forEach((role) => {
        const matchedRole = matchedChildren?.roles?.find((r) => isIDEquals(r.roleId, role.roleId));
        role.approver = matchedRole?.peerReview || false;
        role.evaluator = matchedRole?.evaluate?.isEnabled || false;
    });

    if (typeOfEvaluation === INFRASTRUCTURE) {
        const assignEvaluators = await EvaluationMappingModel.find(
            {
                portfolioId,
                componentId,
                childrenId,
                typeOfEvaluation,
                deliveryTypeId: {
                    $in: deliveryTypes.map((d) => convertToMongoObjectId(d.deliveryTypeId)),
                },
            },
            {
                portfolioId: 1,
                componentId: 1,
                childrenId: 1,
                deliveryTypeId: 1,
                deliveryTypeSymbol: 1,
                'infrastructures.infrastructureId': 1,
            },
        ).lean();

        const bulkUpdates = buildAssignEvaluationQuery({
            portfolioId,
            componentId,
            childrenId,
            institutionCalendarId: portfolio.institutionCalendarId,
            programId: portfolio.programId,
            courseId: portfolio.courseId,
            infrastructureId,
            typeOfEvaluation,
            deliveryTypes,
            roles,
            assignEvaluators,
            userId,
        });

        if (!bulkUpdates.length) return;

        await EvaluationMappingModel.bulkWrite(bulkUpdates).catch((error) => {
            throw new BadRequestError('ERROR_IN_ASSIGN_EVALUATOR', error);
        });

        return true;
    }
    const filter = {
        portfolioId: convertToMongoObjectId(portfolioId),
        componentId: convertToMongoObjectId(componentId),
        childrenId: convertToMongoObjectId(childrenId),
        typeOfEvaluation,
        ...(deliveryType?.deliveryTypeId && {
            deliveryTypeId: convertToMongoObjectId(deliveryType.deliveryTypeId),
        }),
    };

    const assignEvaluator = await EvaluationMappingModel.findOne(filter, {
        students: 1,
        groups: 1,
    }).lean();

    let mergedStudents = [];
    let mergedGroups = [];

    prepareAndPublish.forEach((prepareAndPublishEntry) => {
        prepareAndPublishEntry.session = session;
    });

    roles.forEach((role) => {
        const matchedPrepareAndPublish = prepareAndPublish.filter((p) =>
            isIDEquals(p.roleId, role.roleId),
        );
        if (matchedPrepareAndPublish.length) {
            role.prepareAndPublish = matchedPrepareAndPublish;
        }
    });

    if (assignEvaluator) {
        // Merge students
        const existingStudentsMap = new Map(
            assignEvaluator.students.map((s) => [s.studentId.toString(), s]),
        );

        students.forEach((newStudent) => {
            const id = newStudent.studentId.toString();
            if (existingStudentsMap.has(id)) {
                existingStudentsMap.get(id).roles = roles;
            } else {
                newStudent.roles = roles;
                existingStudentsMap.set(id, newStudent);
            }
        });

        mergedStudents = Array.from(existingStudentsMap.values());

        // Merge groups
        const existingGroupsMap = new Map(
            assignEvaluator.groups.map((g) => [g.groupId?.toString(), g]),
        );

        groups.forEach((newGroup) => {
            const id = newGroup.groupId?.toString();
            if (existingGroupsMap.has(id)) {
                existingGroupsMap.get(id).roles = roles; // Update roles
            } else {
                newGroup.roles = roles;
                existingGroupsMap.set(id, newGroup);
            }
        });

        mergedGroups = Array.from(existingGroupsMap.values());
    } else {
        // No existing evaluator, assign roles directly
        mergedStudents = students.map((s) => ({ ...s, roles }));
        mergedGroups = groups.map((g) => ({ ...g, roles }));
    }

    await EvaluationMappingModel.updateOne(
        filter,
        {
            $set: {
                students: mergedStudents,
                groups: mergedGroups,
                createdBy: { id: userId },
            },
            $setOnInsert: {
                institutionCalendarId: portfolio.institutionCalendarId,
                programId: portfolio.programId,
                courseId: portfolio.courseId,
                portfolioId,
                componentId,
                childrenId,
                typeOfEvaluation,
                ...(deliveryType.deliveryTypeId && {
                    deliveryTypeId: convertToMongoObjectId(deliveryType.deliveryTypeId),
                }),
                ...(deliveryType.deliveryTypeSymbol && {
                    deliveryTypeSymbol: deliveryType.deliveryTypeSymbol,
                }),
                ...(deliveryType.deliveryTypeName && {
                    deliveryTypeName: deliveryType.deliveryTypeName,
                }),
                ...(deliveryType.sessionId && { sessionId: deliveryType.sessionId }),
            },
        },
        { upsert: true },
    );
};

const getInfrastructuresForAssignEvaluator = async ({ portfolioId, componentId, childrenId }) => {
    const portfolio = await PortfolioModel.findOne(
        { _id: portfolioId },
        {
            programId: 1,
            courseId: 1,
            institutionCalendarId: 1,
            'components._id': 1,
            'components.code': 1,
            'components.deliveryTypes': 1,
        },
    ).lean();

    const matchedComponent = portfolio.components?.find(
        (component) => isIDEquals(component._id, componentId) && component.code === LB,
    );

    if (!matchedComponent || !matchedComponent?.deliveryTypes?.length) return [];

    const course = await CourseModel.findOne(
        { _id: portfolio.courseId, isDeleted: false },
        { participating: 1 },
    ).lean();

    if (!course?.participating?.length) return [];

    const infrastructureManagements = await InfrastructureManagementModel.find(
        {
            'program._program_id': convertToMongoObjectId(portfolio.programId),
            'subject._subject_id': course?.participating?.map(({ _subject_id }) =>
                convertToMongoObjectId(_subject_id),
            ),
        },
        {
            name: 1,
            room_no: 1,
            building_name: 1,
            floor_no: 1,
            usage: 1,
            delivery_type: 1,
        },
    ).lean();

    if (!infrastructureManagements.length) return [];

    const assigned = await EvaluationMappingModel.find(
        { portfolioId, componentId, childrenId, typeOfEvaluation: INFRASTRUCTURE },
        { infrastructures: 1, deliveryTypeId: 1 },
    ).lean();

    const count = {
        totalInfrastructures: 0,
        totalDeliveryTypes: 0,
        totalAssignedInfrastructures: 0,
        totalUnassignedInfrastructures: 0,
    };

    const infrastructures = infrastructureManagements.map((infra) => {
        const deliveryTypes = matchedComponent?.deliveryTypes?.filter((dt) =>
            infra.delivery_type.some((x) => isIDEquals(dt.deliveryTypeId, x._delivery_type_id)),
        );

        let assignedFlag = false;

        const formattedDelivery = deliveryTypes.map((dt) => {
            const assignedInfra = assigned.find((a) =>
                isIDEquals(a.deliveryTypeId, dt.deliveryTypeId),
            );
            const matched = assignedInfra?.infrastructures?.find((i) =>
                isIDEquals(i.infrastructureId, infra._id),
            );

            if (matched?.roles?.length) {
                assignedFlag = true;
                return { ...dt, isAssigned: true, roles: matched.roles };
            }

            return dt;
        });

        count.totalDeliveryTypes += deliveryTypes.length;
        if (assignedFlag) {
            count.totalAssignedInfrastructures += 1;
        } else {
            count.totalUnassignedInfrastructures += 1;
        }

        return {
            name: infra.name,
            roomNo: infra.room_no,
            buildingName: infra.building_name,
            floorNo: infra.floor_no,
            usage: infra.usage,
            deliveryTypes: formattedDelivery,
            isAssigned: assignedFlag,
            infrastructureId: infra._id,
        };
    });

    const remainingInfrastructures = infrastructures.filter((infra) => infra.deliveryTypes.length);
    count.totalInfrastructures = remainingInfrastructures.length;

    return {
        infrastructures: remainingInfrastructures,
        count,
    };
};

const switchEvaluationType = async ({ portfolioId, componentId, childrenId }) => {
    await EvaluationMappingModel.deleteMany({ portfolioId, componentId, childrenId });
};

const getEvaluators = async ({
    portfolioId,
    componentId,
    childrenId,
    isGeneralUser = false,
    limit,
    pageNo,
    skip,
    search,
}) => {
    const portfolio = await PortfolioModel.findOne(
        { _id: portfolioId },
        {
            'components._id': 1,
            'components.children._id': 1,
            'components.children.formId': 1,
            'components.children.roles': 1,
            programId: 1,
            courseId: 1,
            institutionCalendarId: 1,
        },
    ).lean();

    const matchedComponent = portfolio.components?.find((component) =>
        isIDEquals(component._id, componentId),
    );
    const matchedChildren = matchedComponent?.children?.find((child) =>
        isIDEquals(child._id, childrenId),
    );

    const evaluators = await getFacultyListByCourse({
        programId: portfolio.programId,
        courseId: portfolio.courseId,
        institutionCalendarId: portfolio.institutionCalendarId,
    });

    if (isGeneralUser) {
        const users = await UserModel.find(
            {
                isActive: true,
                isDeleted: false,
                user_type: STAFF,
                _id: { $nin: evaluators.map((e) => convertToMongoObjectId(e.userId)) },
                status: constant.COMPLETED,
            },
            { userId: '$_id', name: 1, employeeId: '$user_id', gender: 1, email: 1 },
        )
            .skip(skip || (pageNo - 1) * limit)
            .limit(limit)
            .lean();

        const totalCount = await UserModel.countDocuments({
            isActive: true,
            isDeleted: false,
            user_type: STAFF,
            _id: { $nin: evaluators.map((e) => convertToMongoObjectId(e.userId)) },
            status: constant.COMPLETED,
        });

        return {
            roles: matchedChildren?.roles?.filter((role) => role.type !== STUDENT) || [],
            evaluators: users,
            pagination: {
                total: totalCount,
                page: pageNo,
                limit,
            },
        };
    }

    return {
        roles: matchedChildren?.roles?.filter((role) => role.type !== STUDENT) || [],
        evaluators,
    };
};

const getStudentGroupsForAssignEvaluator = async ({
    portfolioId,
    componentId,
    childrenId,
    isStudentGroup,
    deliveryTypeSymbol,
    deliveryTypeId,
}) => {
    const portfolio = await PortfolioModel.findOne(
        { _id: portfolioId },
        {
            'components._id': 1,
            'components.children._id': 1,
            'components.children.formId': 1,
            institutionCalendarId: 1,
            programId: 1,
            courseId: 1,
            year: 1,
            level: 1,
            rotation: 1,
            rotationCount: 1,
            term: 1,
        },
    ).lean();
    if (!portfolio) return [];

    const studentGroup = await getStudentListFromStudentGroup({
        institutionCalendarId: portfolio.institutionCalendarId,
        programId: portfolio.programId,
        term: portfolio.term,
        year: portfolio.year,
        level: portfolio.level,
        ...(portfolio.rotation && { rotation: portfolio.rotation }),
        ...(portfolio.rotationCount && { rotationCount: portfolio.rotationCount }),
        courseId: portfolio.courseId,
    });

    const {
        sgStudentList: students = [],
        masterGroup: groups = [],
        femaleStudentCount = 0,
        maleStudentCount = 0,
        totalStudentCount = 0,
    } = studentGroup;

    if (!students.length) {
        return {
            students: [],
            groups: [],
            count: { total: 0, male: 0, female: 0 },
        };
    }

    const assignEvaluator = await EvaluationMappingModel.findOne(
        {
            portfolioId,
            componentId,
            childrenId,
            ...(deliveryTypeId && {
                deliveryTypeId: convertToMongoObjectId(deliveryTypeId),
            }),
        },
        {
            groups: 1,
            students: 1,
        },
    ).lean();

    if (!isStudentGroup) {
        return {
            students: students.map((student) => {
                const matchedStudent = assignEvaluator?.students?.find((s) =>
                    isIDEquals(s.studentId, student._student_id),
                );

                const matchedRole = matchedStudent?.roles?.find(
                    (role) => role.prepareAndPublish.length,
                );

                return {
                    studentId: student._student_id,
                    academicNo: student.user_id,
                    name: student.name,
                    gender:
                        student.gender.toLowerCase() === MALE_LABEL.toLowerCase() ? MALE : FEMALE,
                    groups: student.groups,
                    isAssigned: !!matchedStudent?.roles?.length,
                    roles: matchedStudent?.roles || [],
                    prepareAndPublish: matchedRole?.prepareAndPublish || [],
                };
            }),
            count: { total: students.length, male: maleStudentCount, female: femaleStudentCount },
        };
    }

    const filteredGroups = groups.filter((group) => group.delivery_symbol === deliveryTypeSymbol);
    if (!filteredGroups.length) {
        return {
            students: [],
            groups: [],
            count: { total: 0, male: 0, female: 0 },
        };
    }

    const formattedGroups = [];

    filteredGroups.forEach((group) => {
        group?.session_group?.forEach((sessionGroup) => {
            if (!sessionGroup?._student_ids?.length) return;

            const assigned = assignEvaluator?.groups?.find((g) =>
                isIDEquals(g.groupId, sessionGroup._id),
            );

            const groupInfo = {
                deliveryTypeSymbol: group.delivery_symbol,
                deliveryTypeName: group.delivery_type,
                gender: group.gender,
                masterGroupName: group.group_name,
                groupName: sessionGroup.group_name,
                groupId: sessionGroup._id,
                isAssigned: !!assigned?.roles?.length,
                roles: assigned?.roles || [],
            };

            students.forEach((student) => {
                if (sessionGroup._student_ids.some((id) => isIDEquals(id, student._student_id))) {
                    student.groups = student.groups || [];
                    student.groups.push(groupInfo);
                }
            });

            formattedGroups.push(groupInfo);
        });
    });

    return {
        students: students.map((student) => {
            const matchedStudent = assignEvaluator?.students?.find((s) =>
                isIDEquals(s.studentId, student._student_id),
            );

            return {
                studentId: student._student_id,
                academicNo: student.user_id,
                name: student.name,
                gender: student.gender.toLowerCase() === MALE_LABEL.toLowerCase() ? MALE : FEMALE,
                groups: student.groups,
                isAssigned: !!matchedStudent?.roles?.length,
                roles: matchedStudent?.roles || [],
            };
        }),
        groups: formattedGroups,
        count: { total: totalStudentCount, male: maleStudentCount, female: femaleStudentCount },
    };
};

const getDeliveryTypeForAssignEvaluator = async ({ portfolioId, componentId }) => {
    const portfolio = await PortfolioModel.findOne(
        { _id: portfolioId },
        {
            'components._id': 1,
            'components.deliveryTypes': 1,
            'components.code': 1,
        },
    ).lean();

    const component = portfolio.components?.find((c) => isIDEquals(c._id, componentId));
    return component?.code === LB ? component.deliveryTypes || [] : [];
};

const addExternalEvaluator = async ({
    email,
    name,
    roleId,
    isEvaluator = false,
    mobile,
    gender,
    componentId,
    childrenId,
    deliveryTypeId,
    studentId,
}) => {
    try {
        const query = { componentId, childrenId, ...(deliveryTypeId && { deliveryTypeId }) };

        const assignEvaluator = await EvaluationMappingModel.findOne(query, {
            students: 1,
            externalUsers: 1,
        }).lean();

        if (!assignEvaluator) {
            throw new BadRequestError('YOU_DONT_HAVE_ACCESS_TO_ADD_EXTERNAL_EVALUATOR');
        }

        const extUser = assignEvaluator.externalUsers?.find(
            (e) => isIDEquals(e.email, email) && isIDEquals(e.roleId, roleId),
        );

        if (extUser) {
            throw new BadRequestError('EXTERNAL_EVALUATOR_ALREADY_ASSIGNED');
        }

        const updateQuery = {
            $push: {
                externalUsers: {
                    email,
                    name,
                    mobile,
                    gender,
                    roleId,
                    approver: !isEvaluator,
                    evaluator: isEvaluator,
                },
            },
        };

        await EvaluationMappingModel.updateOne(query, updateQuery);

        const code = generate6DigitCode();
        await VerificationModel.updateOne(
            { email, componentId, childrenId, roleId, ...(deliveryTypeId && { deliveryTypeId }) },
            {
                $set: {
                    email,
                    code,
                    roleId,
                    studentId,
                    componentId,
                    childrenId,
                    ...(deliveryTypeId && { deliveryTypeId }),
                    expiresAt: new Date(Date.now() + VERIFICATION_EXPIRY_SECONDS * 1000),
                },
            },
            { upsert: true },
        );

        const mailContent = generateVerificationEmailHTML(code, VERIFICATION_EXPIRY_SECONDS);

        await sendEmail(email, 'Verification Code', mailContent);
    } catch (error) {
        console.error(error);
        throw error;
    }
};

const verifyVerificationCode = async ({
    email,
    code,
    roleId,
    componentId,
    childrenId,
    deliveryTypeId,
    studentId,
}) => {
    const verification = await VerificationModel.findOne(
        {
            email,
            code,
            roleId,
            studentId,
            componentId,
            childrenId,
            ...(deliveryTypeId && { deliveryTypeId }),
        },
        { _id: 1 },
    ).lean();
    if (!verification) {
        throw new BadRequestError('INVALID_VERIFICATION_CODE');
    }

    const token = await _generateToken({
        userId: email,
        _id: email,
        userType: constant.EXTERNAL_STAFF,
        sessionUUID: uuidv4(),
        userRoleData: [constant.EXTERNAL_STAFF],
    });

    await VerificationModel.deleteOne({ _id: verification._id });

    return token;
};

const updateStudentsForPrepareAndPublish = async ({
    portfolioId,
    componentId,
    childrenId,
    studentIds = [],
    session,
    userId,
    pages = [],
}) => {
    const evaluation = await EvaluationMappingModel.findOne(
        {
            portfolioId,
            componentId,
            childrenId,
            typeOfEvaluation: STUDENT_LIST,
        },
        {
            'students.roles.prepareAndPublish.userId': userId,
            'students.studentId': 1,
        },
    ).lean();

    const updateDoc = {};

    const filteredStudents = evaluation?.students?.filter((student) =>
        studentIds.some((id) => isIDEquals(id, student.studentId)),
    );

    filteredStudents.forEach((student, studentIndex) => {
        const matchedRole = student.roles?.find((r) =>
            r.prepareAndPublish.some((p) => isIDEquals(p.userId, userId)),
        );
        if (!matchedRole) return;

        matchedRole.prepareAndPublish?.forEach((pub, pubIndex) => {
            if (isIDEquals(pub.userId, userId)) {
                updateDoc[
                    `students.${studentIndex}.roles.${student.roles.indexOf(
                        matchedRole,
                    )}.prepareAndPublish.${pubIndex}.session`
                ] = session;
            }
        });
    });

    await EvaluationMappingModel.updateOne(
        { portfolioId, componentId, childrenId, typeOfEvaluation: STUDENT_LIST },
        { $set: updateDoc },
    );

    const studentResponses = await StudentResponseModel.find(
        {
            parentPortfolioId: portfolioId,
            componentId,
            childrenId,
            'student._id': { $in: studentIds.map((s) => convertToMongoObjectId(s)) },
            status: NOT_STARTED,
        },
        { prepareAndPublish: 1, 'student._id': 1, pages: 1 },
    ).lean();

    if (!studentResponses) return [];

    const bulkUpdates = studentResponses.map((studentResponse) => {
        const bulkUpdateDoc = {};
        const arrayFilters = [];

        studentResponse.pages.forEach((page) => {
            const matchedPage = pages.find((p) => isIDEquals(p._id, page._id));
            page.elements.forEach((element) => {
                const matchedElement = matchedPage?.elements?.find((e) =>
                    isIDEquals(e._id, element._id),
                );

                if (matchedElement) {
                    element.elements = matchedElement.elements;
                }
            });
        });

        // TODO: Uncomment this when prepare and publish is implemented
        // const matchedPrepareAndPublish = studentResponse?.prepareAndPublish?.find((p) =>
        //     isIDEquals(p.userId, userId),
        // );

        // if (matchedPrepareAndPublish) {
        //     bulkUpdateDoc.$set = {
        //         [`prepareAndPublish.$[prepare].pages`]: studentResponse.pages,
        //         [`prepareAndPublish.$[prepare].session`]: session,
        //     };
        //     arrayFilters.push({
        //         'prepare._id': convertToMongoObjectId(matchedPrepareAndPublish._id),
        //     });
        // } else {
        //     bulkUpdateDoc.$push = {
        //         prepareAndPublish: {
        //             userId,
        //             roleId,
        //             pages: studentResponse.pages,
        //             session,
        //         },
        //     };
        // }

        bulkUpdateDoc.$set = {
            pages: studentResponse.pages,
            session,
        };

        return {
            updateOne: {
                filter: {
                    _id: convertToMongoObjectId(studentResponse._id),
                },
                update: bulkUpdateDoc,
                ...(arrayFilters.length && { arrayFilters }),
            },
        };
    });

    await StudentResponseModel.bulkWrite(bulkUpdates).catch((err) => {
        throw new BadRequestError('FAILED_TO_UPDATE_STUDENT_RESPONSES');
    });

    return bulkUpdates;
};

const getChildrenForPrepareAndPublish = async ({
    programId,
    courseId,
    institutionCalendarId,
    term,
    year,
    level,
    curriculumId,
    rotation,
    rotationCount,
    userId,
}) => {
    const portfolio = await PortfolioModel.findOne(
        {
            programId,
            courseId,
            institutionCalendarId,
            term,
            year,
            level,
            ...(curriculumId && { curriculumId }),
            ...(rotation && { rotation }),
            ...(rotationCount && { rotationCount }),
            status: PUBLISHED,
        },
        {
            'components._id': 1,
            'components.isLogBook': 1,
            'components.name': 1,
            'components.children._id': 1,
            'components.children.evaluators': 1,
            'components.children.formId': 1,
            'components.children.session': 1,
            'components.children.name': 1,
        },
    ).lean();

    if (!portfolio) return [];

    const children = portfolio.components
        .filter((c) => !c.isLogBook)
        .flatMap((c) =>
            c.children.map((child) => ({
                ...child,
                childrenId: child._id,
                componentId: c._id,
                componentCode: c.code,
                componentName: c.name,
                portfolioId: portfolio._id,
            })),
        );

    const evaluations = await EvaluationMappingModel.find(
        {
            portfolioId: portfolio._id,
            typeOfEvaluation: STUDENT_LIST,
            'students.roles.prepareAndPublish.userId': userId,
        },
        {
            componentId: 1,
            childrenId: 1,
        },
    ).lean();

    const filteredChildren = children.filter((child) =>
        evaluations.some((e) => isIDEquals(e.childrenId, child.childrenId)),
    );

    return filteredChildren;
};

const getStudentsForPrepareAndPublish = async ({
    portfolioId,
    componentId,
    childrenId,
    userId,
}) => {
    const studentPortfolios = await StudentPortfolioModel.find(
        {
            portfolioId,
        },
        {
            student: 1,
            'components._id': 1,
            'components.code': 1,
            'components.name': 1,
            'components.children._id': 1,
            'components.children.evaluators': 1,
            'components.children.formId': 1,
            'components.children.session': 1,
            'components.children.name': 1,
        },
    ).lean();

    if (!studentPortfolios.length) return [];

    const evaluations = await EvaluationMappingModel.findOne(
        {
            portfolioId,
            componentId,
            childrenId,
            typeOfEvaluation: STUDENT_LIST,
            'students.roles.users.userId': userId,
        },
        {
            componentId: 1,
            childrenId: 1,
            'students.studentId': 1,
            'students.roles.prepareAndPublish': 1,
            'students.roles.roleId': 1,
        },
    ).lean();

    const students = [];

    for (const student of studentPortfolios) {
        const component = student?.components?.find((c) => isIDEquals(c._id, componentId));
        const child = component?.children?.find((ch) => isIDEquals(ch._id, childrenId));

        if (!component || !child) continue;

        const matchedStudent = evaluations?.students?.find((s) =>
            isIDEquals(s.studentId, student.student._id),
        );

        if (!matchedStudent || !Array.isArray(matchedStudent.roles)) continue;

        let prepareAndPublish = null;

        for (const role of matchedStudent.roles) {
            const match = role?.prepareAndPublish?.find((p) => isIDEquals(p.userId, userId));
            if (match) {
                prepareAndPublish = match;
                break;
            }
        }

        if (!prepareAndPublish) continue;

        students.push({
            student: student.student,
            component: {
                componentId: component._id,
                componentCode: component.code,
                componentName: component.name,
            },
            child: {
                childrenId: child._id,
                name: child.name,
                session: prepareAndPublish?.session || child.session,
            },
        });
    }

    const studentsResponses = await StudentResponseModel.find(
        {
            parentPortfolioId: portfolioId,
            componentId,
            childrenId,
            'student._id': { $in: students.map((s) => s.student._id) },
        },
        { status: 1, 'student._id': 1 },
    ).lean();

    students.forEach((student) => {
        const matchedStudentResponse = studentsResponses.find((s) =>
            isIDEquals(s.student._id, student.student._id),
        );

        student.status = matchedStudentResponse?.status || NOT_STARTED;
    });

    return students;
};

const getRolesFromChild = async ({ portfolioId, componentId, childrenId }) => {
    const portfolio = await PortfolioModel.findOne(
        { _id: portfolioId },
        {
            'components._id': 1,
            'components.children._id': 1,
            'components.children.roles.type': 1,
            'components.children.roles.roleId': 1,
            'components.children.roles.name': 1,
            'components.children.roles.evaluate.isEnabled': 1,
            'components.children.roles.peerReview': 1,
        },
    ).lean();

    const child = portfolio?.components
        .find((c) => isIDEquals(c._id, componentId))
        ?.children?.find((ch) => isIDEquals(ch._id, childrenId));

    if (!child?.roles?.length) return [];

    return child.roles.filter((r) => r.type !== STUDENT);
};

const getEvaluationType = async ({ portfolioId, componentId, childrenId }) => {
    const { typeOfEvaluation } =
        (await EvaluationMappingModel.findOne(
            { portfolioId, componentId, childrenId },
            { typeOfEvaluation: 1 },
        ).lean()) || {};

    return { evaluationType: typeOfEvaluation || INFRASTRUCTURE };
};

const deleteEvaluation = async ({
    portfolioId,
    componentId,
    childrenId,
    typeOfEvaluation,
    deliveryTypeId,
    infrastructureId,
    studentId,
    groupId,
}) => {
    const baseFilter = {
        portfolioId,
        componentId,
        childrenId,
        typeOfEvaluation,
        ...(deliveryTypeId && { deliveryTypeId }),
    };

    const pulls = {
        [STUDENT_GROUP]: {
            ...(groupId && { groups: { groupId: convertToMongoObjectId(groupId) } }),
            ...(studentId && { students: { studentId: convertToMongoObjectId(studentId) } }),
        },
        [INFRASTRUCTURE]: {
            infrastructures: { infrastructureId: convertToMongoObjectId(infrastructureId) },
        },
        [STUDENT_LIST]: { students: { studentId: convertToMongoObjectId(studentId) } },
    };

    if (pulls[typeOfEvaluation]) {
        await EvaluationMappingModel.updateOne(baseFilter, { $pull: pulls[typeOfEvaluation] });
    }
};

const reSendVerificationCode = async ({
    email,
    roleId,
    componentId,
    childrenId,
    deliveryTypeId,
    studentId,
}) => {
    try {
        const code = generate6DigitCode();

        await VerificationModel.updateOne(
            { email, componentId, childrenId, roleId, ...(deliveryTypeId && { deliveryTypeId }) },
            {
                $set: {
                    email,
                    code,
                    roleId,
                    studentId,
                    componentId,
                    childrenId,
                    ...(deliveryTypeId && { deliveryTypeId }),
                    expiresAt: new Date(Date.now() + VERIFICATION_EXPIRY_SECONDS * 1000),
                },
            },
            { upsert: true },
        );

        const mailContent = generateVerificationEmailHTML(code, VERIFICATION_EXPIRY_SECONDS);

        await sendEmail(email, 'Verification Code', mailContent);
    } catch (error) {
        console.log(error);
        throw error;
    }
};

const getAccessibleFormSectionsByChild = async ({
    portfolioId,
    componentId,
    childrenId,
    studentId,
    userId,
}) => {
    const portfolio = await PortfolioModel.findOne(
        { _id: portfolioId },
        {
            'components._id': 1,
            'components.children._id': 1,
            'components.children.roles': 1,
            'components.children.formId': 1,
        },
    ).lean();

    const component = portfolio?.components?.find((c) => isIDEquals(c._id, componentId));
    const child = component?.children?.find((ch) => isIDEquals(ch._id, childrenId));
    if (!component || !child) return [];

    let studentResponse = null;
    if (studentId) {
        studentResponse = await StudentResponseModel.findOne(
            {
                parentPortfolioId: portfolioId,
                componentId,
                childrenId,
                ...(studentId && { 'student._id': convertToMongoObjectId(studentId) }),
            },
            { pages: 1 },
        ).lean();
    }

    const form = await FormModel.findOne(
        { _id: child.formId },
        { pages: 1, type: 1, title: 1 },
    ).lean();
    if (!form) return [];

    const assignEvaluator = await EvaluationMappingModel.findOne(
        { portfolioId, componentId, childrenId, typeOfEvaluation: STUDENT_LIST },
        {
            'students.roles.roleId': 1,
            'students.roles.prepareAndPublish': 1,
            'students.studentId': 1,
        },
    ).lean();
    if (!assignEvaluator) return [];

    let roleId = '';
    let session = '';

    const students = studentId
        ? assignEvaluator?.students?.filter((s) => isIDEquals(s.studentId, studentId))
        : assignEvaluator?.students || [];

    for (const student of students) {
        for (const role of student.roles || []) {
            const match = role.prepareAndPublish?.find((p) => isIDEquals(p.userId, userId));
            if (match) {
                roleId = role.roleId;
                session = match.session || '';
                break;
            }
        }
        if (roleId) break;
    }

    const role = child?.roles?.find((r) => isIDEquals(r.roleId, roleId));
    if (!role) return [];

    form.pages = getAssignedUserSections({
        pages: studentResponse?.pages ? studentResponse.pages : form.pages,
        viewSections: role?.viewSections || [],
        modifySections: role?.modifySections || [],
    });

    return { form, session, roleId };
};

module.exports = {
    assignEvaluator,
    getInfrastructuresForAssignEvaluator,
    switchEvaluationType,
    getEvaluators,
    getStudentGroupsForAssignEvaluator,
    getDeliveryTypeForAssignEvaluator,
    addExternalEvaluator,
    verifyVerificationCode,
    updateStudentsForPrepareAndPublish,
    getChildrenForPrepareAndPublish,
    getStudentsForPrepareAndPublish,
    getRolesFromChild,
    getEvaluationType,
    deleteEvaluation,
    reSendVerificationCode,
    getAccessibleFormSectionsByChild,
};
