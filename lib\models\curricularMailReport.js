const {
    Schema,
    model,
    Types: { ObjectId },
} = require('mongoose');
const { CURRICULAR_MAIL_REPORT, USER } = require('../utility/constants');

const curricularMailReport = new Schema(
    {
        userId: {
            type: ObjectId,
            ref: USER,
        },
        userEmail: {
            type: String,
        },
        deliveryFrequency: {
            type: String,
        },
        sendTime: {
            type: String,
        },
        dayOfWeek: {
            type: String,
        },
        createdBy: {
            type: ObjectId,
        },
        lastSent: {
            type: Date,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
    },
    {
        timestamps: true,
    },
);

curricularMailReport.index({ userId: 1, isActive: 1, isDeleted: 1 });
curricularMailReport.index({ deliveryFrequency: 1, isActive: 1 });

module.exports = model(CURRICULAR_MAIL_REPORT, curricularMailReport);
