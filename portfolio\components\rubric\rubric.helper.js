const {
    POINT,
    PERCENTAGE,
    DIMENSIONAL,
    COMPONENTIAL,
    HEADER,
    SUB_HEADER,
} = require('../../common/utils/enums');
const {
    calculateAverage,
    getNestedValue,
    roundToTwoDecimalPlaces,
} = require('../../common/utils/common.util');
const { divide } = require('../../common/utils/number.util');
const { MAX_VALUE, AWARDED_VALUE } = require('../../common/utils/constants');

const calculatePercentageOfWeightage = (weightageValue, weightedPercentage) => {
    return divide(weightageValue, weightedPercentage) * 100;
};

const calculatePercentageValue = (value, percentage) => {
    return divide(value * percentage, 100);
};

const calculateSumByProperty = (arr, propertyName) => {
    let sum = 0;
    for (let i = 0; i < arr?.length; i++) {
        const value = getNestedValue(arr[i], propertyName.split('.'));
        if (typeof value === 'number') sum += value;
    }

    return sum;
};

const calculateMaxValue = ({ type = '', dimensions = [] }) => {
    let maxValue = 0;
    if (type === DIMENSIONAL) {
        maxValue = Math.max(...dimensions.map((dimension) => dimension.maxValue));
    } else if (type === COMPONENTIAL) {
        maxValue = calculateSumByProperty(dimensions, MAX_VALUE);
    }

    return maxValue;
};

const calculateAchievedMarks = ({ type = '', dimensions = [] }) => {
    let overallAwardedValue = 0;

    if (type === DIMENSIONAL) {
        overallAwardedValue = (dimensions || []).reduce(
            (total, { isSelected = false, maxValue = 0 }) =>
                isSelected ? total + maxValue : total,
            0,
        );
    } else if (type === COMPONENTIAL) {
        overallAwardedValue = calculateSumByProperty(dimensions, AWARDED_VALUE);
    }

    return overallAwardedValue;
};

const calculateRubricPoint = ({ rubrics = [] }) =>
    rubrics.reduce((total, { parameters = [], scoring, isGlobal = false }) => {
        if (isGlobal || scoring !== POINT) return total;

        const totalPoints = parameters
            .filter((p) => ![HEADER, SUB_HEADER].includes(p.type))
            .reduce((sum, p) => sum + calculateMaxValue(p), 0);

        return total + totalPoints;
    }, 0);

const calculateStudentAchievedPoints = ({ rubrics = [], totalMarks = 0 }) => {
    const achievedMarks = [];
    let totalRubricsPoint = 0;
    rubrics.forEach(({ parameters = [], scoring, isGlobal = false }) => {
        if (!isGlobal) {
            const achievedPercentages = [];
            let totalPoints = 0;
            let achievedPoints = 0;

            parameters
                .filter((parameter) => ![HEADER, SUB_HEADER].includes(parameter.type))
                .forEach((parameter) => {
                    const paramMaxValue = calculateMaxValue(parameter);
                    const paramAchievedPoints = calculateAchievedMarks(parameter);
                    if (scoring === POINT) {
                        totalPoints += paramMaxValue;
                        achievedPoints += paramAchievedPoints;
                    } else if (scoring === PERCENTAGE) {
                        achievedPercentages.push(
                            calculatePercentageOfWeightage(paramAchievedPoints, paramMaxValue),
                        );
                    }
                });

            if (scoring === POINT) {
                achievedMarks.push(divide(achievedPoints, totalPoints) * totalMarks);
            } else if (scoring === PERCENTAGE) {
                achievedMarks.push(
                    calculatePercentageValue(totalMarks, calculateAverage(achievedPercentages)),
                );
            }

            totalRubricsPoint += totalPoints;
        }
    });

    const awardedMarks = roundToTwoDecimalPlaces(calculateAverage(achievedMarks));

    return { awardedMarks, totalRubricsPoint };
};

const calculateGlobalRubricPoint = ({ rubrics = [] }) => {
    const achievedMarks = [];
    let totalRubricsPoint = 0;

    rubrics.forEach(({ parameters = [], scoring }) => {
        const percentages = [];
        let totalPoints = 0;
        let achievedPoints = 0;

        parameters
            .filter((p) => ![HEADER, SUB_HEADER].includes(p.type))
            .forEach((p) => {
                const max = calculateMaxValue(p);
                const achieved = calculateAchievedMarks(p);

                if (scoring === POINT) {
                    totalPoints += max;
                    achievedPoints += achieved;
                } else if (scoring === PERCENTAGE) {
                    percentages.push(calculatePercentageOfWeightage(achieved, max));
                }
            });

        if (scoring === POINT) {
            if (totalPoints > 0) {
                achievedMarks.push(roundToTwoDecimalPlaces(achievedPoints)); // Raw points only
                totalRubricsPoint += totalPoints;
            }
        } else if (scoring === PERCENTAGE) {
            achievedMarks.push(calculateAverage(percentages)); // Result is already in %
        }
    });

    return {
        awardedPoints: roundToTwoDecimalPlaces(calculateAverage(achievedMarks)),
        totalRubricsPoint,
    };
};

module.exports = {
    calculateRubricPoint,
    calculateStudentAchievedPoints,
    calculateGlobalRubricPoint,
};
