const express = require('express');
const router = express.Router();
const { validate } = require('../../middleware/validation');
const catchAsync = require('../utility/catch-async');
const {
    updateCourseWiseFacialSettings,
    getCourseWiseFacialData,
    getCourseWiseUserDetails,
    updateCourseWiseDeliveryType,
    getCourseWiseStaffDetails,
} = require('./courseWiseSetting.controller');
const { userPolicyAuthentication, defaultPolicy } = require('../../middleware/policy.middleware');
const {
    getCourseWiseFacialDataValidator,
    updateCourseWiseFacialSettingValidator,
    getCourseWiseStaffDetailsValidator,
    updateCourseWiseDeliveryTypeValidator,
    getCourseWiseUserDetailsValidator,
} = require('./courseWiseSetting.validator');

router.put(
    '/courseWise-setting',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([{ schema: updateCourseWiseFacialSettingValidator, property: 'body' }]),
    catchAsync(updateCourseWiseFacialSettings),
);
router.get(
    '/getCourseWise-settings',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([{ schema: getCourseWiseFacialDataValidator, property: 'query' }]),
    catchAsync(getCourseWiseFacialData),
);
router.get(
    '/getCourseWise-userDetails',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([{ schema: getCourseWiseUserDetailsValidator, property: 'query' }]),
    catchAsync(getCourseWiseUserDetails),
);
router.put(
    '/getCourseWise-updateCourseWiseDeliveryType',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([{ schema: updateCourseWiseDeliveryTypeValidator, property: 'body' }]),
    catchAsync(updateCourseWiseDeliveryType),
);
router.get(
    '/getCourseWise-staffDetails',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([{ schema: getCourseWiseStaffDetailsValidator, property: 'query' }]),
    catchAsync(getCourseWiseStaffDetails),
);

module.exports = router;
