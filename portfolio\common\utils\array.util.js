const { isIDEquals, checkIsEmpty } = require('./common.util');

const filterByConditions = ({ array = [], conditions = [], canReturnCount = false }) => {
    const filtered = array.filter((item) =>
        conditions.every(([key, value, { useIDEquals = false, useCallback = false } = {}]) => {
            if (useIDEquals) return isIDEquals(item[key], value);

            if (useCallback) {
                if (typeof value !== 'function') return false; // guard
                return value({ item });
            }

            return item[key] === value;
        }),
    );

    return canReturnCount ? filtered.length : filtered;
};

const arrayToMap = ({
    array = [],
    keyFn = (k) => k,
    valueFn = (v) => v,
    group = false,
    includeEmptyKey = false,
}) =>
    array.reduce((acc, item) => {
        const rawKey = keyFn(item);
        if (!includeEmptyKey && checkIsEmpty(rawKey)) return acc;

        const key = String(rawKey);
        const val = valueFn(item);

        if (group) (acc[key] ??= []).push(val);
        else acc[key] = val; // last one wins on duplicate keys

        return acc;
    }, {});

module.exports = {
    filterByConditions,
    arrayToMap,
};
