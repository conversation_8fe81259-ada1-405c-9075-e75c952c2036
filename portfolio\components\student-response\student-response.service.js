const StudentResponseModel = require('./student-response.model');
const StudentPortfolioModel = require('../portfolio/student-portfolio.model');
const EvaluationMappingModel = require('../evaluation/evaluation-mapping.model');
const PortFolioRoleModel = require('../role/role.model');
const FormModel = require('../form/form.model');
const UserModel = require('../../../lib/models/user');
const CourseScheduleModel = require('../../../lib/models/course_schedule');
const PortfolioModel = require('../portfolio/portfolio.model');
const CourseModel = require('../../../lib/models/digi_course');

const {
    IN_PROGRESS,
    ON_GOING,
    SUBMITTED,
    NOT_STARTED,
    RESUBMIT,
} = require('../../common/utils/enums');
const {
    NotFoundError,
    BadRequestError,
    UpdateFailedError,
} = require('../../common/utils/api_error_util');
const {
    convertToMongoObjectId,
    getFileCategory,
    isIDEquals,
} = require('../../common/utils/common.util');
const { checkDocumentsExists } = require('../../base/base.helper');
const { getS3SignedUrl } = require('../../../service/aws.service');
const {
    APPROVALS,
    EVALUATIONS,
    INFRASTRUCTURE,
    STUDENT_GROUP,
    STUDENT_LIST,
    STUDENT,
    EVALUATOR,
    APPROVER,
} = require('../../common/utils/constants');
const { getStudentStatus } = require('../student/student.helper');

const startForm = async ({
    portfolioId,
    componentId,
    childrenId,
    prepareAndPublishId,
    userId,
    scheduleId,
}) => {
    const query = {
        'student._id': convertToMongoObjectId(userId),
        portfolioId,
        componentId,
        childrenId,
        ...(scheduleId && { scheduleId }),
    };
    const formExists = await checkDocumentsExists({
        Model: StudentResponseModel,
        query: { ...query, status: ON_GOING },
        canThrowError: false,
    });
    if (formExists) {
        throw new BadRequestError('ALREADY_FORM_STARTED');
    }

    let updateDoc = {};
    if (prepareAndPublishId) {
        updateDoc = {
            $set: {
                'prepareAndPublish.$[prepare].status': ON_GOING,
                'prepareAndPublish.$[prepare].formTimestamps.firstStartedAt': new Date(),
            },
        };
    } else {
        updateDoc = {
            $set: {
                status: ON_GOING,
                'formTimestamps.firstStartedAt': new Date(),
            },
        };
    }

    const updateStudentResponse = await StudentResponseModel.findOneAndUpdate(query, updateDoc, {
        projection: { _id: 1, student: 1 },
        arrayFilters: [
            ...(prepareAndPublishId
                ? [{ 'prepare._id': convertToMongoObjectId(prepareAndPublishId) }]
                : []),
        ],
    }).lean();

    return updateStudentResponse;
};

const updateStudentResponse = async ({
    studentResponseId,
    prepareAndPublishId,
    pageId,
    sectionId,
    section,
    userId,
}) => {
    const isPrepare = !!prepareAndPublishId;
    const basePath = isPrepare
        ? 'prepareAndPublish.$[prepare].pages.$[page].elements.$[section].elements'
        : 'pages.$[page].elements.$[section].elements';

    const updateDoc = {
        $set: {
            [basePath]: section.elements,
            'formTimestamps.lastUpdated': new Date(),
            ...(isPrepare && {
                'prepareAndPublish.$[prepare].formTimestamps.lastUpdated': new Date(),
            }),
        },
    };

    const arrayFilters = [
        ...(isPrepare ? [{ 'prepare._id': convertToMongoObjectId(prepareAndPublishId) }] : []),
        { 'page._id': convertToMongoObjectId(pageId) },
        { 'section._id': convertToMongoObjectId(sectionId) },
    ];

    const result = await StudentResponseModel.updateOne(
        {
            _id: studentResponseId,
            'student._id': userId,
            $or: [
                { status: ON_GOING },
                { approvalStatus: RESUBMIT },
                { evaluationStatus: RESUBMIT },
            ],
        },
        updateDoc,
        { arrayFilters },
    );

    if (!result.modifiedCount) {
        throw new UpdateFailedError('FAILED_TO_UPDATE_STUDENT_RESPONSE');
    }
};

const submitForm = async ({ studentResponseId, prepareAndPublishId, pages, userId }) => {
    const response = await StudentResponseModel.findOne(
        { _id: studentResponseId, 'student._id': userId },
        {
            status: 1,
            'prepareAndPublish.status': 1,
            'prepareAndPublish._id': 1,
            approvalStatus: 1,
            evaluationStatus: 1,
            'approvals.status': 1,
            'approvals._id': 1,
        },
    ).lean();

    if (!response) return;

    const isResubmitted =
        response?.approvalStatus === RESUBMIT || response?.evaluationStatus === RESUBMIT;

    const now = new Date();
    const commonHistory = {
        status: isResubmitted ? RESUBMIT : SUBMITTED,
        createdAt: now,
        userId: convertToMongoObjectId(userId),
    };

    const updateDoc = prepareAndPublishId
        ? {
              $set: {
                  'prepareAndPublish.$[prepare].formTimestamps.submittedAt': now,
                  'prepareAndPublish.$[prepare].status': SUBMITTED,
              },
              $push: {
                  submissionHistory: {
                      ...commonHistory,
                      prepareAndPublishId: convertToMongoObjectId(prepareAndPublishId),
                  },
              },
          }
        : {
              $set: {
                  status: SUBMITTED,
                  pages,
                  ...(response?.approvalStatus === RESUBMIT && {
                      approvalStatus: IN_PROGRESS,
                  }),
                  ...(response?.evaluationStatus === RESUBMIT && {
                      evaluationStatus: IN_PROGRESS,
                  }),
                  'formTimestamps.submittedAt': now,
              },
              ...(response?.approvalStatus === RESUBMIT && {
                  $set: {
                      'approvals.$[approval].status': NOT_STARTED,
                  },
              }),
              $push: { submissionHistory: commonHistory },
          };

    const arrayFilters = prepareAndPublishId
        ? [{ 'prepare._id': convertToMongoObjectId(prepareAndPublishId) }]
        : response?.approvalStatus === RESUBMIT
        ? [{ 'approval.status': RESUBMIT }]
        : [];

    await StudentResponseModel.updateOne(
        { _id: studentResponseId, 'student._id': userId },
        updateDoc,
        { arrayFilters },
    );
};

const getStudentResponse = async ({
    portfolioId,
    componentId,
    childrenId,
    scheduleId,
    prepareAndPublishId,
    userId,
    type = STUDENT,
}) => {
    const portfolio = await StudentPortfolioModel.findOne(
        { $or: [{ _id: portfolioId }, { portfolioId }], isDeleted: false },
        {
            'components._id': 1,
            'components.isLogBook': 1,
            'components.children._id': 1,
            'components.children.hasSimilarityCheck': 1,
            'components.timeline': 1,
            'components.children.formId': 1,
            'components.children.formMarks': 1,
            'components.children.marks': 1,
            'components.children.session': 1,
            'components.children.roles.peerReview': 1,
            'components.children.roles.type': 1,
            'components.children.roles.evaluate.isEnabled': 1,
        },
    ).lean();
    if (!portfolio) {
        throw new NotFoundError('PORTFOLIO_NOT_FOUND');
    }

    const component = portfolio?.components?.find((c) => isIDEquals(c._id, componentId));
    const children = component?.children?.find((c) => isIDEquals(c._id, childrenId));

    const hasApprover = component?.children?.some((child) =>
        child.roles.some((role) => role.peerReview),
    );

    const form = await FormModel.findOne(
        { _id: children?.formId, isDeleted: false },
        { title: 1, type: 1, _id: 0 },
    ).lean();

    const studentResponse = await StudentResponseModel.findOne(
        {
            'student._id': userId,
            componentId,
            childrenId,
            ...(scheduleId && { scheduleId }),
        },
        {
            student: 1,
            status: 1,
            totalMarks: 1,
            title: 1,
            type: 1,
            deliveryType: 1,
            scheduleId: 1,
            session: 1,
            ...(prepareAndPublishId
                ? {
                      prepareAndPublish: {
                          $elemMatch: { _id: convertToMongoObjectId(prepareAndPublishId) },
                      },
                  }
                : { pages: 1 }),

            ...(!prepareAndPublishId && { similarityCheckAttachment: 1, isSimilarityCheck: 1 }),
            approvalStatus: 1,
            evaluationStatus: 1,
        },
    ).lean();

    const isResubmitted =
        studentResponse?.approvalStatus === RESUBMIT ||
        studentResponse?.evaluationStatus === RESUBMIT;

    if (!studentResponse) {
        throw new NotFoundError('STUDENT_RESPONSE_NOT_FOUND');
    }

    if (studentResponse?.prepareAndPublish?.length) {
        studentResponse.prepareAndPublish = studentResponse.prepareAndPublish[0];
    }

    if (studentResponse?.pages?.length || studentResponse?.prepareAndPublish?.length) {
        const pages = studentResponse?.prepareAndPublish?.length
            ? studentResponse.prepareAndPublish.pages
            : studentResponse.pages;

        const urlPromises = [];

        for (const page of pages) {
            for (const section of page.elements) {
                for (const question of section.elements) {
                    if (question?.attachment?.key) {
                        urlPromises.push(
                            (async () => {
                                question.attachment.link = await getS3SignedUrl({
                                    bucket: question.attachment.bucket,
                                    key: question.attachment.key,
                                });
                            })(),
                        );
                    }
                }
            }
        }

        await Promise.all(urlPromises);

        if (studentResponse?.prepareAndPublish?.length) {
            studentResponse.prepareAndPublish.pages = pages;
        } else {
            studentResponse.pages = pages;
        }
    }

    if (
        studentResponse?.prepareAndPublish?.length &&
        studentResponse?.prepareAndPublish?.similarityCheckAttachment?.key
    ) {
        const signedUrl = await getS3SignedUrl({
            bucket: studentResponse.prepareAndPublish.similarityCheckAttachment.bucket,
            key: studentResponse.prepareAndPublish.similarityCheckAttachment.key,
        });

        studentResponse.prepareAndPublish.similarityCheckAttachment.link = signedUrl;
    } else if (studentResponse?.similarityCheckAttachment?.key) {
        const signedUrl = await getS3SignedUrl({
            bucket: studentResponse.similarityCheckAttachment.bucket,
            key: studentResponse.similarityCheckAttachment.key,
        });

        studentResponse.similarityCheckAttachment.link = signedUrl;
    }

    if (isResubmitted) {
        studentResponse.status = RESUBMIT;
    }

    let courseSchedule = null;
    if (component?.isLogBook) {
        courseSchedule = await CourseScheduleModel.findOne(
            {
                _id: scheduleId,
            },
            {
                scheduleDate: '$schedule_date',
                start: 1,
                end: 1,
            },
        ).lean();
    }

    const conditions = {
        [STUDENT]: (role) => role?.type === STUDENT,
        [EVALUATOR]: (role) => role?.evaluate?.isEnabled,
        [APPROVER]: (role) => role?.peerReview,
    };

    const roleId = children?.roles?.find((role) => conditions[type](role))?.roleId;

    return {
        ...studentResponse,
        hasSimilarityCheck: children?.hasSimilarityCheck,
        form,
        roleId,
        formMarks: children?.formMarks,
        marks: children?.marks,
        session: component?.isLogBook ? courseSchedule : studentResponse?.session,
        timeline: component?.timeline,
        status: getStudentStatus({
            status: studentResponse.status,
            approvalStatus: studentResponse.approvalStatus,
            evaluationStatus: studentResponse.evaluationStatus,
            hasApproval: hasApprover,
        }),
    };
};

const uploadStudentAttachment = async ({
    studentResponseId,
    attachment,
    pageId,
    sectionId,
    questionId,
    email,
    prepareAndPublishId,
}) => {
    const key = `${attachment.folder}/${attachment.id}/${attachment.fileName}.${attachment.fileType}`;
    const attachmentData = {
        key,
        bucket: attachment.bucket,
        type: getFileCategory({ type: attachment.fileType }),
    };

    if (email) {
        // Case: Evaluator image logic
        const exists = await StudentResponseModel.exists({
            _id: studentResponseId,
            'evaluatorImages.email': email,
        });

        await StudentResponseModel.updateOne(
            { _id: studentResponseId },
            exists
                ? { $set: { 'evaluatorImages.$[elem].attachment': attachmentData } }
                : {
                      $push: {
                          evaluatorImages: { email, attachment: attachmentData },
                      },
                  },
            ...(exists && { arrayFilters: [{ 'elem.email': email }] }),
        );
    } else if (pageId && sectionId && questionId && !prepareAndPublishId) {
        // Case: Attachment inside student response page structure
        await StudentResponseModel.updateOne(
            { _id: studentResponseId },
            {
                $set: {
                    'pages.$[pageId].elements.$[sectionId].elements.$[questionId].attachment':
                        attachmentData,
                },
            },
            {
                arrayFilters: [
                    { 'pageId._id': convertToMongoObjectId(pageId) },
                    { 'sectionId._id': convertToMongoObjectId(sectionId) },
                    { 'questionId._id': convertToMongoObjectId(questionId) },
                ],
            },
        );
    } else if (prepareAndPublishId && pageId && sectionId && questionId) {
        // Case: Attachment inside student response page structure
        await StudentResponseModel.updateOne(
            { _id: studentResponseId },
            {
                $set: {
                    'prepareAndPublish.$[prepare].pages.$[pageId].elements.$[sectionId].elements.$[questionId].attachment':
                        attachmentData,
                },
            },
            {
                arrayFilters: [
                    { 'prepare._id': convertToMongoObjectId(prepareAndPublishId) },
                    { 'pageId._id': convertToMongoObjectId(pageId) },
                    { 'sectionId._id': convertToMongoObjectId(sectionId) },
                    { 'questionId._id': convertToMongoObjectId(questionId) },
                ],
            },
        );
    }

    const signedUrl = await getS3SignedUrl({
        bucket: attachment.bucket,
        key,
    });

    return signedUrl;
};

const deleteStudentAttachment = async ({
    studentResponseId,
    pageId,
    sectionId,
    questionId,
    email,
}) => {
    let attachmentData = null;

    if (email) {
        // Case: Evaluator image logic
        const studentResponse = await StudentResponseModel.findOne(
            { _id: studentResponseId, 'evaluatorImages.email': email },
            { 'evaluatorImages.$': 1 },
        ).lean();

        if (!studentResponse || !studentResponse.evaluatorImages?.[0]?.attachment) {
            throw new NotFoundError('EVALUATOR_ATTACHMENT_NOT_FOUND');
        }

        attachmentData = studentResponse.evaluatorImages[0].attachment;

        // Remove attachment from database
        await StudentResponseModel.updateOne(
            { _id: studentResponseId, 'evaluatorImages.email': email },
            { $unset: { 'evaluatorImages.$.attachment': 1 } },
        );
    } else if (pageId && sectionId && questionId) {
        const studentResponse = await StudentResponseModel.findOne(
            { _id: studentResponseId },
            { pages: 1 },
        ).lean();

        if (!studentResponse) {
            throw new NotFoundError('STUDENT_RESPONSE_NOT_FOUND');
        }

        const page = studentResponse.pages?.find((p) => p._id.toString() === pageId);
        const section = page?.elements?.find((s) => s._id.toString() === sectionId);
        const question = section?.elements?.find((q) => q._id.toString() === questionId);

        if (!question?.attachment) {
            throw new NotFoundError('ATTACHMENT_NOT_FOUND');
        }

        attachmentData = question.attachment;

        await StudentResponseModel.updateOne(
            { _id: studentResponseId },
            {
                $unset: {
                    'pages.$[pageId].elements.$[sectionId].elements.$[questionId].attachment': 1,
                },
            },
            {
                arrayFilters: [
                    { 'pageId._id': convertToMongoObjectId(pageId) },
                    { 'sectionId._id': convertToMongoObjectId(sectionId) },
                    { 'questionId._id': convertToMongoObjectId(questionId) },
                ],
            },
        );
    } else {
        throw new BadRequestError('INVALID_PARAMETERS_FOR_ATTACHMENT_DELETION');
    }

    // Delete from AWS S3
    if (attachmentData?.key && attachmentData?.bucket) {
        await deleteS3Object({
            bucket: attachmentData.bucket,
            key: attachmentData.key,
        });
    }
};

const getEvaluatorAndApproverForStudent = async ({
    componentId,
    childrenId,
    deliveryTypeId,
    scheduleId,
    studentId,
}) => {
    const evaluation = await EvaluationMappingModel.findOne(
        {
            componentId,
            childrenId,
            ...(deliveryTypeId && { deliveryTypeId }),
        },
        {
            groups: 1,
            infrastructures: 1,
            students: {
                $elemMatch: {
                    studentId,
                },
            },
            typeOfEvaluation: 1,
            deliveryTypeSymbol: 1,
            externalUsers: 1,
            portfolioId: 1,
        },
    ).lean();

    if (!evaluation) return [];

    const roleMap = new Map();

    const addRolesToMap = (roles = []) => {
        roles.forEach(({ roleId, approver, evaluator, users = [] }) => {
            const key = roleId.toString();
            if (!roleMap.has(key)) {
                roleMap.set(key, { roleId, approver, evaluator, users: [...users] });
            } else {
                const existingUsers = roleMap.get(key).users;
                const newUsers = users.filter(
                    (u) => !existingUsers.some((e) => e.email === u.email),
                );
                roleMap.get(key).users.push(...newUsers);
            }
        });
    };

    if (evaluation.typeOfEvaluation === INFRASTRUCTURE) {
        evaluation.infrastructures?.forEach((infra) => addRolesToMap(infra.roles ?? []));
    }

    if (evaluation.typeOfEvaluation === STUDENT_GROUP) {
        // Add shared group roles
        evaluation.groups?.forEach((group) => addRolesToMap(group.roles ?? []));

        // Add individual student roles
        evaluation.students?.forEach((student) => {
            if (isIDEquals(student.studentId, studentId)) {
                addRolesToMap(student.roles ?? []);
            }
        });
    }

    if (evaluation.typeOfEvaluation === STUDENT_LIST) {
        evaluation.students?.forEach((student) => {
            if (isIDEquals(student.studentId, studentId)) {
                addRolesToMap(student.roles ?? []);
            }
        });
    }

    const roleIds = [...roleMap.keys()].map(convertToMongoObjectId);
    const rolesInfo = await PortFolioRoleModel.find(
        { _id: { $in: roleIds } },
        { name: 1, type: 1 },
    ).lean();
    const roleInfoMap = new Map(rolesInfo.map((r) => [r._id.toString(), r]));

    const result = [];
    for (const [roleId, value] of roleMap.entries()) {
        const { approver, evaluator, users } = value;
        const roleMeta = roleInfoMap.get(roleId);
        if (roleMeta) {
            result.push({
                roleId,
                approver,
                evaluator,
                name: roleMeta.name,
                type: roleMeta.type,
                users,
            });
        }
    }

    const studentResponse = await StudentResponseModel.findOne(
        {
            'student._id': studentId,
            componentId,
            childrenId,
            ...(scheduleId && { scheduleId }),
        },
        {
            evaluations: 1,
            approvals: 1,
            student: 1,
            submissionHistory: 1,
            approvalStatus: 1,
            evaluationStatus: 1,
        },
    ).lean();

    const portfolio = await PortfolioModel.findOne(
        { _id: evaluation.portfolioId },
        {
            'components._id': 1,
            'components.children._id': 1,
            'components.children.hasMultipleApprover': 1,
            'components.children.hasMultipleEvaluator': 1,
        },
    ).lean();

    const component = portfolio?.components?.find((c) => isIDEquals(c._id, componentId));
    const children = component?.children?.find((c) => isIDEquals(c._id, childrenId));

    result
        .sort((a, b) => (b.approver === true) - (a.approver === true))
        .forEach((role) => {
            const key = role.approver ? APPROVALS : role.evaluator ? EVALUATIONS : null;
            if (!key) return;

            for (const user of role.users) {
                const match = studentResponse?.[key]?.find((a) =>
                    isIDEquals(a.userId, user.userId),
                );
                user.status = match?.status || NOT_STARTED;
                user.updatedAt = match?.updatedAt;
            }

            if (evaluation.externalUsers.length) {
                const externalUsers = evaluation.externalUsers.filter((e) =>
                    isIDEquals(e.roleId, role.roleId),
                );

                for (const externalUser of externalUsers) {
                    const match = studentResponse?.[key]?.find((a) =>
                        isIDEquals(a.email, externalUser.email),
                    );
                    externalUser.status = match?.status || NOT_STARTED;
                    externalUser.updatedAt = match?.updatedAt;
                    externalUser.isExternalUser = true;
                }

                role.users.push(...externalUsers);
            }
            if (role.evaluator) {
                role.status = studentResponse?.evaluationStatus || NOT_STARTED;
                role.hasMultipleEvaluator = children?.hasMultipleEvaluator || false;
            }
            if (role.approver) {
                role.status = studentResponse?.approvalStatus || NOT_STARTED;
                role.hasMultipleApprover = children?.hasMultipleApprover || false;
            }
        });

    return {
        student: studentResponse?.student,
        submissionHistory: studentResponse?.submissionHistory,
        approversAndEvaluators: result,
    };
};

const uploadSimilarityCheckAttachment = async ({
    studentResponseId,
    attachment,
    prepareAndPublishId,
}) => {
    const key = `${attachment.folder}/${attachment.id}/${attachment.fileName}.${attachment.fileType}`;
    const attachmentData = {
        key,
        bucket: attachment.bucket,
        type: getFileCategory({ type: attachment.fileType }),
    };

    if (prepareAndPublishId) {
        await StudentResponseModel.updateOne(
            { _id: studentResponseId },
            {
                $set: {
                    'prepareAndPublish.$[prepare].similarityCheckAttachment': attachmentData,
                },
            },
            {
                arrayFilters: [{ 'prepare._id': convertToMongoObjectId(prepareAndPublishId) }],
            },
        );
    } else {
        await StudentResponseModel.updateOne(
            { _id: studentResponseId },
            { $set: { similarityCheckAttachment: attachmentData } },
        );
    }
};

const deleteSimilarityCheckAttachment = async ({ studentResponseId, prepareAndPublishId }) => {
    const unsetPath = prepareAndPublishId
        ? { 'prepareAndPublish.$[prepare].similarityCheckAttachment': 1 }
        : { similarityCheckAttachment: 1 };

    await StudentResponseModel.updateOne({ _id: studentResponseId }, { $unset: unsetPath });
};

const updateSimilarityCheck = async ({
    studentResponseId,
    prepareAndPublishId,
    isSimilarityCheck,
}) => {
    const isNested = !!prepareAndPublishId;
    const updateDoc = {
        $set: {
            [isNested ? 'prepareAndPublish.$[prepare].isSimilarityCheck' : 'isSimilarityCheck']:
                isSimilarityCheck,
        },
    };
    const arrayFilters = isNested
        ? [{ 'prepare._id': convertToMongoObjectId(prepareAndPublishId) }]
        : [];

    await StudentResponseModel.updateOne({ _id: studentResponseId }, updateDoc, { arrayFilters });
};

const addReview = async ({ studentResponseId, reviewId, text, userId, roleId, externalEmail }) => {
    let name;
    let role;

    if (externalEmail) {
        role = await PortFolioRoleModel.findOne({ _id: roleId }, { name: 1, _id: 0 }).lean();
    } else {
        [name, role] = await Promise.all([
            UserModel.findOne({ _id: userId }, { name: 1, _id: 0 }).lean(),
            PortFolioRoleModel.findOne({ _id: roleId }, { name: 1, _id: 0 }).lean(),
        ]);
    }

    const commonData = {
        ...(!externalEmail && { name: name?.name }),
        role: role?.name,
        createdAt: new Date(),
        ...(externalEmail ? { externalEmail } : { userId }),
    };

    const update = reviewId
        ? { $push: { 'reviews.$.thread': { ...commonData, text } } }
        : { $push: { reviews: { ...commonData, reason: text } } };

    const filter = reviewId
        ? { _id: studentResponseId, 'reviews._id': reviewId }
        : { _id: studentResponseId };

    await StudentResponseModel.updateOne(filter, update);
};

const getReview = async ({ studentResponseId }) => {
    const review = await StudentResponseModel.findOne(
        { _id: studentResponseId },
        { reviews: 1 },
    ).lean();
    return review?.reviews || [];
};

const updateAndDeleteReview = async ({ studentResponseId, reviewId, threadId, text }) => {
    const response = await StudentResponseModel.findOne(
        { _id: studentResponseId, 'reviews._id': reviewId },
        { 'reviews.$': 1 },
    ).lean();

    if (!response) throw new NotFoundError('REVIEW_NOT_FOUND');

    const review = response.reviews[0];
    const hasThread = Array.isArray(review?.thread) && review.thread.length > 0;
    const trimmedText = text?.trim();

    let filter = {};
    let updateDoc = {};
    let options = {};

    if (hasThread) {
        filter = { _id: studentResponseId, 'reviews._id': reviewId };

        if (trimmedText) {
            updateDoc = {
                $set: {
                    'reviews.$[r].thread.$[t].text': trimmedText,
                    'reviews.$[r].thread.$[t].createdAt': new Date(),
                },
            };
            options = {
                arrayFilters: [{ 'r._id': reviewId }, { 't._id': threadId }],
            };
        } else {
            updateDoc = {
                $pull: {
                    'reviews.$[r].thread': { _id: threadId },
                },
            };
            options = {
                arrayFilters: [{ 'r._id': reviewId }],
            };
        }
    } else {
        if (trimmedText) {
            filter = { _id: studentResponseId, 'reviews._id': reviewId };
            updateDoc = {
                $set: {
                    'reviews.$.reason': trimmedText,
                },
            };
        } else {
            filter = { _id: studentResponseId };
            updateDoc = {
                $pull: {
                    reviews: { _id: reviewId },
                },
            };
        }
    }

    await StudentResponseModel.updateOne(filter, updateDoc, options);
};

const updateFacialRecognition = async ({
    componentId,
    childrenId,
    deliveryTypeId,
    attachment,
    roleId,
    email,
}) => {
    const studentResponse = await EvaluationMappingModel.findOne(
        { componentId, childrenId, ...(deliveryTypeId && { deliveryTypeId }) },
        { 'externalUsers.roleId': 1, 'externalUsers.email': 1 },
    ).lean();

    if (!studentResponse) return;

    const externalUser = studentResponse.externalUsers.find(
        (e) => isIDEquals(e.email, email) && isIDEquals(e.roleId, roleId),
    );

    if (!externalUser) return;

    const key = `${attachment.folder}/${attachment.id}/${attachment.fileName}.${attachment.fileType}`;
    const attachmentData = {
        key,
        bucket: attachment.bucket,
        type: getFileCategory({ type: attachment.fileType }),
    };

    await EvaluationMappingModel.updateOne(
        { componentId, childrenId, ...(deliveryTypeId && { deliveryTypeId }) },
        { $set: { 'externalUsers.$[externalUser].facial': attachmentData } },
        { arrayFilters: [{ 'externalUser._id': convertToMongoObjectId(externalUser._id) }] },
    );
};

const getStudentDetails = async ({
    portfolioId,
    componentId,
    childrenId,
    scheduleId,
    studentId,
}) => {
    const studentPortfolio = await StudentPortfolioModel.findOne(
        { $or: [{ _id: portfolioId }, { portfolioId }], isDeleted: false },
        {
            'components._id': 1,
            'components.isLogBook': 1,
            'components.children._id': 1,
            'components.children.formId': 1,
            courseId: 1,
            student: 1,
        },
    ).lean();

    const component = studentPortfolio?.components?.find((c) => isIDEquals(c._id, componentId));
    const children = component?.children?.find((c) => isIDEquals(c._id, childrenId));

    const form = await FormModel.findOne(
        { _id: children?.formId, isDeleted: false },
        { title: 1, type: 1, _id: 0 },
    ).lean();

    const course = await CourseModel.findOne(
        { _id: studentPortfolio?.courseId, isDeleted: false },
        { name: '$course_name', code: '$course_code', _id: 0 },
    ).lean();

    let session = null;
    if (component?.isLogBook) {
        session =
            (await CourseScheduleModel.findOne(
                { _id: scheduleId, isDeleted: false },
                { scheduleDate: '$schedule_date', start: 1, end: 1 },
            ).lean()) || {};
    } else {
        const studentResponse = await StudentResponseModel.findOne(
            {
                'student._id': convertToMongoObjectId(studentId),
                componentId,
                childrenId,
                ...(scheduleId && { scheduleId }),
            },
            { session: 1 },
        ).lean();
        session = studentResponse?.session || {};
    }

    return {
        form,
        course,
        student: studentPortfolio?.student,
        session,
    };
};

module.exports = {
    startForm,
    updateStudentResponse,
    submitForm,
    getStudentResponse,
    uploadStudentAttachment,
    deleteStudentAttachment,
    getEvaluatorAndApproverForStudent,
    uploadSimilarityCheckAttachment,
    deleteSimilarityCheckAttachment,
    updateSimilarityCheck,
    addReview,
    getReview,
    updateAndDeleteReview,
    updateFacialRecognition,
    getStudentDetails,
};
