const EvaluationService = require('./evaluation.service');

const { checkIfTrue, getPaginationValues } = require('../../common/utils/common.util');

const assignEvaluator = async ({
    body: {
        portfolioId,
        componentId,
        childrenId,
        infrastructureId,
        deliveryTypes = [],
        deliveryType = {},
        roles = [],
        typeOfEvaluation,
        students = [],
        groups = [],
        session = {},
        prepareAndPublish = [],
    },
    headers: { user_id: userId } = {},
}) => {
    const result = await EvaluationService.assignEvaluator({
        portfolioId,
        componentId,
        childrenId,
        infrastructureId,
        deliveryTypes,
        deliveryType,
        roles,
        typeOfEvaluation,
        students,
        userId,
        groups,
        session,
        prepareAndPublish,
    });

    return { statusCode: 200, data: result };
};

const getInfrastructuresForAssignEvaluator = async ({
    query: { portfolioId, componentId, childrenId },
}) => {
    const result = await EvaluationService.getInfrastructuresForAssignEvaluator({
        portfolioId,
        componentId,
        childrenId,
    });

    return { statusCode: 200, data: result };
};

const switchEvaluationType = async ({ query: { portfolioId, componentId, childrenId } }) => {
    await EvaluationService.switchEvaluationType({ portfolioId, componentId, childrenId });

    return { statusCode: 200, data: 'EVALUATION_TYPE_SWITCHED_SUCCESSFULLY' };
};

const getEvaluators = async ({ query = {} }) => {
    const { portfolioId, componentId, childrenId, isGeneralUser } = query;

    const { limit, pageNo, skip, search } = getPaginationValues(query);

    const result = await EvaluationService.getEvaluators({
        portfolioId,
        componentId,
        childrenId,
        isGeneralUser: checkIfTrue(isGeneralUser),
        limit,
        pageNo,
        skip,
        search,
    });
    return { statusCode: 200, data: result };
};

const getStudentGroupsForAssignEvaluator = async ({
    query: {
        portfolioId,
        componentId,
        childrenId,
        isStudentGroup,
        deliveryTypeSymbol,
        deliveryTypeId,
    },
}) => {
    const result = await EvaluationService.getStudentGroupsForAssignEvaluator({
        portfolioId,
        componentId,
        childrenId,
        isStudentGroup: checkIfTrue(isStudentGroup),
        deliveryTypeSymbol,
        deliveryTypeId,
    });

    return { statusCode: 200, data: result };
};

const getDeliveryTypeForAssignEvaluator = async ({ query: { portfolioId, componentId } }) => {
    const result = await EvaluationService.getDeliveryTypeForAssignEvaluator({
        portfolioId,
        componentId,
    });

    return { statusCode: 200, data: result };
};

const updateStudentsForPrepareAndPublish = async ({
    body: { portfolioId, componentId, childrenId, studentIds, formId, roleId, pages, session },
    headers: { user_id: userId } = {},
}) => {
    const result = await EvaluationService.updateStudentsForPrepareAndPublish({
        portfolioId,
        componentId,
        childrenId,
        studentIds,
        formId,
        roleId,
        pages,
        session,
        userId,
    });

    return { statusCode: 200, data: result };
};

const getChildrenForPrepareAndPublish = async ({
    query: {
        programId,
        courseId,
        institutionCalendarId,
        term,
        year,
        level,
        curriculumId,
        rotation,
        rotationCount,
    },
    headers: { user_id: userId } = {},
}) => {
    const result = await EvaluationService.getChildrenForPrepareAndPublish({
        programId,
        courseId,
        institutionCalendarId,
        term,
        year,
        level,
        curriculumId,
        rotation,
        rotationCount,
        userId,
    });

    return { statusCode: 200, data: result };
};

const getStudentsForPrepareAndPublish = async ({
    query: { portfolioId, componentId, childrenId },
    headers: { user_id: userId } = {},
}) => {
    const result = await EvaluationService.getStudentsForPrepareAndPublish({
        portfolioId,
        componentId,
        childrenId,
        userId,
    });

    return { statusCode: 200, data: result };
};

const getRolesFromChild = async ({ query }) => {
    const { portfolioId, componentId, childrenId } = query;

    const result = await EvaluationService.getRolesFromChild({
        portfolioId,
        componentId,
        childrenId,
    });

    return { statusCode: 200, data: result };
};

const getEvaluationType = async ({ query: { portfolioId, componentId, childrenId } }) => {
    const result = await EvaluationService.getEvaluationType({
        portfolioId,
        componentId,
        childrenId,
    });

    return { statusCode: 200, data: result };
};

const deleteEvaluation = async ({
    query: {
        portfolioId,
        componentId,
        childrenId,
        typeOfEvaluation,
        deliveryTypeId,
        infrastructureId,
        groupId,
    },
}) => {
    await EvaluationService.deleteEvaluation({
        portfolioId,
        componentId,
        childrenId,
        typeOfEvaluation,
        deliveryTypeId,
        infrastructureId,
        groupId,
    });

    return { statusCode: 200, data: 'EVALUATION_DELETED_SUCCESSFULLY' };
};

const getAccessibleFormSectionsByChild = async ({
    query: { portfolioId, componentId, childrenId, studentId, formId },
    headers: { user_id: userId } = {},
}) => {
    const result = await EvaluationService.getAccessibleFormSectionsByChild({
        portfolioId,
        componentId,
        childrenId,
        formId,
        studentId,
        userId,
    });

    return { statusCode: 200, data: result };
};

module.exports = {
    assignEvaluator,
    getInfrastructuresForAssignEvaluator,
    switchEvaluationType,
    getEvaluators,
    getStudentGroupsForAssignEvaluator,
    getDeliveryTypeForAssignEvaluator,
    updateStudentsForPrepareAndPublish,
    getChildrenForPrepareAndPublish,
    getStudentsForPrepareAndPublish,
    getRolesFromChild,
    getEvaluationType,
    deleteEvaluation,
    getAccessibleFormSectionsByChild,
};
