const Joi = require('joi');
const { DAYS } = require('../utility/constants');
const timePattern = /^(0?[1-9]|1[0-2]):[0-5][0-9]\s?(AM|PM|am|pm)$/;
const dayOfWeekValues = [
    DAYS.MONDAY,
    DAYS.TUESDAY,
    DAYS.WEDNESDAY,
    DAYS.THURSDAY,
    DAYS.FRIDAY,
    DAYS.SATURDAY,
    DAYS.SUNDAY,
];
const deliveryFrequencyValues = ['daily', 'weekly'];
const objectId = Joi.string().alphanum().length(24).required();
const dayOfWeekSchema = Joi.string().valid(...dayOfWeekValues);
const deliveryFrequencySchema = Joi.string().valid(...deliveryFrequencyValues);
const sendTimeSchema = Joi.string().pattern(timePattern);
const conditionalDayValidation = Joi.when('deliveryFrequency', {
    is: 'weekly',
    then: dayOfWeekSchema.required(),
    otherwise: Joi.string().allow('').optional(),
});

const createScheduleValidator = Joi.object()
    .keys({
        userId: Joi.string().trim().required(),
        deliveryFrequency: deliveryFrequencySchema.required(),
        sendTime: sendTimeSchema.required(),
        dayOfWeek: conditionalDayValidation,
        userEmail: Joi.string().trim().required(),
    })
    .unknown(true);

const bulkUpdateSchedulesValidator = Joi.object()
    .keys({
        schedules: Joi.array()
            .items(
                Joi.object()
                    .keys({
                        userId: Joi.string().trim().required(),
                        deliveryFrequency: deliveryFrequencySchema.required(),
                        sendTime: sendTimeSchema.required(),
                        dayOfWeek: conditionalDayValidation,
                        isActive: Joi.boolean().default(true),
                        userEmail: Joi.string().trim().required(),
                    })
                    .unknown(true),
            )
            .min(1)
            .required(),
    })
    .unknown(true);

const deleteScheduleValidator = Joi.object()
    .keys({
        id: objectId.required(),
    })
    .unknown(true);

module.exports = {
    createScheduleValidator,
    bulkUpdateSchedulesValidator,
    deleteScheduleValidator,
};
