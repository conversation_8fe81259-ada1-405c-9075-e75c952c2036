const { Joi } = require('../../common/middlewares/validation');
const { objectIdSchema, objectIdRQSchema } = require('../../../lib/utility/validationSchemas');

const startFormSchema = Joi.object({
    query: Joi.object({
        componentId: objectIdRQSchema,
        childrenId: objectIdRQSchema,
        formId: objectIdRQSchema,
    }),
}).unknown(true);

const updateStudentResponseSchema = Joi.object({
    query: Joi.object({
        studentResponseId: objectIdRQSchema,
    }),
    body: Joi.object({
        pageId: objectIdRQSchema,
        sectionId: objectIdRQSchema,
        section: Joi.object().required(),
    }),
}).unknown(true);

const submitFormSchema = Joi.object({
    query: Joi.object({
        studentResponseId: objectIdRQSchema,
    }),
    body: Joi.object({
        pages: Joi.array().min(1).required(),
    }),
}).unknown(true);

const getStudentResponseSchema = Joi.object({
    query: Joi.object({
        formId: objectIdRQSchema,
        portfolioId: objectIdRQSchema,
        componentId: objectIdRQSchema,
        childrenId: objectIdRQSchema,
    }),
}).unknown(true);

const uploadStudentAttachmentSchema = Joi.object({
    body: Joi.object({
        studentResponseId: objectIdRQSchema,
        pageId: objectIdRQSchema.optional(),
        sectionId: objectIdRQSchema.optional(),
        questionId: objectIdRQSchema.optional(),
        attachment: Joi.string().required(),
        email: Joi.string().optional().allow(''),
    }),
});

const deleteStudentAttachmentSchema = Joi.object({
    query: Joi.object({
        studentResponseId: objectIdRQSchema,
    }),
});

const validateChildIdSchema = Joi.object({
    query: Joi.object({
        componentId: objectIdRQSchema,
        childrenId: objectIdRQSchema,
        deliveryTypeId: objectIdSchema.optional(),
    }),
}).unknown(true);

const uploadSimilarityCheckAttachmentSchema = Joi.object({
    body: Joi.object({
        studentResponseId: objectIdRQSchema,
        attachment: Joi.string().required(),
        prepareAndPublishId: objectIdRQSchema.optional(),
    }),
});

const deleteSimilarityCheckAttachmentSchema = Joi.object({
    query: Joi.object({
        studentResponseId: objectIdRQSchema,
        prepareAndPublishId: objectIdRQSchema.optional(),
    }),
});

const updateSimilarityCheckSchema = Joi.object({
    body: Joi.object({
        studentResponseId: objectIdRQSchema,
        prepareAndPublishId: objectIdRQSchema.optional(),
        isSimilarityCheck: Joi.boolean().required(),
    }),
});

const addReviewSchema = Joi.object({
    body: Joi.object({
        studentResponseId: objectIdRQSchema,
        reviewId: objectIdRQSchema,
        roleId: objectIdRQSchema,
        text: Joi.string().required(),
    }),
});

const getReviewSchema = Joi.object({
    query: Joi.object({
        studentResponseId: objectIdRQSchema,
    }),
});

const updateReviewSchema = Joi.object({
    query: Joi.object({
        studentResponseId: objectIdRQSchema,
        reviewId: objectIdRQSchema,
        roleId: objectIdRQSchema,
        text: Joi.string().optional(),
    }),
});

const addExternalEvaluatorSchema = Joi.object({
    body: Joi.object({
        email: Joi.string().email().required(),
        name: Joi.string().required(),
        roleId: objectIdRQSchema,
        mobile: Joi.number().required(),
        gender: Joi.string().required(),
        componentId: objectIdRQSchema,
        childrenId: objectIdRQSchema,
        deliveryTypeId: objectIdSchema.optional(),
    }),
});

const verifyVerificationCodeSchema = Joi.object({
    body: Joi.object({
        email: Joi.string().email().required(),
        code: Joi.number().required(),
        roleId: objectIdRQSchema,
        componentId: objectIdRQSchema,
        childrenId: objectIdRQSchema,
        deliveryTypeId: objectIdSchema.optional(),
    }),
});

const updateFacialRecognitionSchema = Joi.object({
    body: Joi.object({
        email: Joi.string().email().required(),
        roleId: objectIdRQSchema,
        componentId: objectIdRQSchema,
        childrenId: objectIdRQSchema,
        deliveryTypeId: objectIdSchema.optional(),
    }),
});

const getStudentDetailsSchema = Joi.object({
    query: Joi.object({
        portfolioId: objectIdRQSchema,
        componentId: objectIdRQSchema,
        childrenId: objectIdRQSchema,
        scheduleId: objectIdRQSchema,
        studentId: objectIdRQSchema,
    }),
});

module.exports = {
    startFormSchema,
    updateStudentResponseSchema,
    submitFormSchema,
    getStudentResponseSchema,
    uploadStudentAttachmentSchema,
    deleteStudentAttachmentSchema,
    validateChildIdSchema,
    uploadSimilarityCheckAttachmentSchema,
    deleteSimilarityCheckAttachmentSchema,
    updateSimilarityCheckSchema,
    addReviewSchema,
    getReviewSchema,
    updateReviewSchema,
    addExternalEvaluatorSchema,
    verifyVerificationCodeSchema,
    updateFacialRecognitionSchema,
    getStudentDetailsSchema,
};
