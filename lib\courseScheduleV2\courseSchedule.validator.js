const Joi = require('joi');
const {
    objectIdRQSchema,
    stringRQSchema,
    optionalKey,
    arrayOfObjectIds,
    booleanSchema,
    objectIdSchema,
    arrayOfStringOptional,
    dateRQSchema,
    numberSchema,
    stringSchema,
} = require('../utility/validationSchemas');

const eventListValidation = {
    headers: Joi.object({
        _institution_id: objectIdRQSchema,
    }).unknown(),
    body: Joi.object({
        institutionCalendarId: objectIdRQSchema,
        programId: objectIdRQSchema,
        courseId: objectIdSchema,
        year: optionalKey,
        level: optionalKey,
        term: stringRQSchema,
        withoutSchedule: booleanSchema,
        startDate: dateRQSchema,
        endDate: dateRQSchema,
    }),
};

const courseTopicsValidation = {
    headers: Joi.object({
        _institution_id: objectIdRQSchema,
    }).unknown(),
    query: Joi.object({
        programId: objectIdRQSchema,
        courseId: objectIdRQSchema,
        deliveryType: stringRQSchema,
        deliverySymbol: stringRQSchema,
    }),
};

const availableListValidation = {
    headers: Joi.object({
        _institution_id: objectIdRQSchema,
    }).unknown(),
    query: Joi.object({
        institutionCalendarId: objectIdRQSchema,
        startTime: dateRQSchema,
        endTime: dateRQSchema,
        programId: objectIdRQSchema,
        term: stringSchema,
        year: stringSchema,
        level: stringSchema,
        courseId: objectIdRQSchema,
        sessionId: objectIdRQSchema,
        scheduleId: objectIdSchema,
        selfSchedule: booleanSchema,
    }),
};

const createSingleCourseScheduleValidation = {
    headers: Joi.object({
        _institution_id: objectIdRQSchema,
        user_id: objectIdRQSchema,
    }).unknown(),
    body: Joi.object({
        institutionCalendarId: objectIdRQSchema,
        programName: stringRQSchema,
        courseName: stringRQSchema,
        courseCode: stringRQSchema,
        type: stringSchema,
        programId: objectIdRQSchema,
        courseId: objectIdRQSchema,
        sessionId: objectIdRQSchema,
        term: stringSchema,
        studentGroupId: Joi.array()
            .items(
                Joi.object()
                    .keys({
                        groupId: objectIdRQSchema,
                        gender: stringSchema,
                        groupNo: numberSchema,
                        groupName: stringSchema,
                        sessionGroup: Joi.array()
                            .items(
                                Joi.object().keys({
                                    sessionGroupId: objectIdRQSchema,
                                    groupNo: numberSchema,
                                    groupName: stringSchema,
                                }),
                            )
                            .optional(),
                        mode: stringSchema,
                    })
                    .optional(),
            )
            .optional(),
        rotation: stringSchema,
        scheduleDate: dateRQSchema,
        start: Joi.object().keys({
            hour: numberSchema,
            minute: numberSchema,
            format: stringSchema,
        }),
        end: Joi.object().keys({
            hour: numberSchema,
            minute: numberSchema,
            format: stringSchema,
        }),
        scheduleStartDateAndTime: dateRQSchema,
        scheduleEndDateAndTime: dateRQSchema,
        mode: stringSchema,
        subjects: Joi.array().items(
            Joi.object().keys({
                subjectId: objectIdSchema,
                subjectName: stringSchema,
            }),
        ),
        staffs: Joi.array().items(
            Joi.object().keys({
                staffName: Joi.object().keys({
                    first: optionalKey,
                    last: optionalKey,
                    middle: optionalKey,
                    family: optionalKey,
                }),
                staffId: objectIdSchema,
            }),
        ),
        infraId: objectIdSchema,
        infraName: stringSchema,
        remotePlatform: stringSchema,
        outsideCampus: stringSchema,
        overWriteConflict: booleanSchema,
    }),
};

const deleteScheduleValidation = {
    query: Joi.object({
        scheduleId: objectIdRQSchema,
    }),
};

const courseDeliverySymbolValidation = {
    headers: Joi.object({
        _institution_id: objectIdRQSchema,
    }).unknown(),
    query: Joi.object({
        programId: objectIdRQSchema,
        courseId: objectIdRQSchema,
    }),
};

const editSingleCourseScheduleValidation = {
    headers: Joi.object({
        _institution_id: objectIdRQSchema,
        user_id: objectIdRQSchema,
    }).unknown(),
    body: Joi.object({
        scheduleId: objectIdRQSchema,
        institutionCalendarId: objectIdRQSchema,
        programName: stringRQSchema,
        courseName: stringRQSchema,
        courseCode: stringRQSchema,
        type: stringSchema,
        programId: objectIdRQSchema,
        courseId: objectIdRQSchema,
        sessionId: objectIdRQSchema,
        term: stringSchema,
        studentGroupId: Joi.array()
            .items(
                Joi.object()
                    .keys({
                        groupId: objectIdRQSchema,
                        gender: stringSchema,
                        groupNo: numberSchema,
                        groupName: stringSchema,
                        sessionGroup: Joi.array()
                            .items(
                                Joi.object().keys({
                                    sessionGroupId: objectIdRQSchema,
                                    groupNo: numberSchema,
                                    groupName: stringSchema,
                                }),
                            )
                            .optional(),
                        mode: stringSchema,
                    })
                    .optional(),
            )
            .optional(),
        rotation: stringSchema,
        scheduleDate: dateRQSchema,
        start: Joi.object().keys({
            hour: numberSchema,
            minute: numberSchema,
            format: stringSchema,
        }),
        end: Joi.object().keys({
            hour: numberSchema,
            minute: numberSchema,
            format: stringSchema,
        }),
        scheduleStartDateAndTime: dateRQSchema,
        scheduleEndDateAndTime: dateRQSchema,
        mode: stringSchema,
        subjects: Joi.array().items(
            Joi.object().keys({
                subjectId: objectIdSchema,
                subjectName: stringSchema,
            }),
        ),
        staffs: Joi.array().items(
            Joi.object().keys({
                staffName: Joi.object().keys({
                    first: optionalKey,
                    last: optionalKey,
                    middle: optionalKey,
                    family: optionalKey,
                }),
                staffId: objectIdSchema,
            }),
        ),
        infraId: objectIdSchema,
        infraName: stringSchema,
        remotePlatform: stringSchema,
        outsideCampus: stringSchema,
        overWriteConflict: booleanSchema,
    }),
};

const multiScheduleAnalysisValidation = {
    headers: Joi.object({
        _institution_id: objectIdRQSchema,
        user_id: objectIdRQSchema,
    }).unknown(),
    body: Joi.object({
        institutionCalendarId: objectIdRQSchema,
        programId: objectIdRQSchema,
        courseId: objectIdRQSchema,
        term: stringRQSchema,
        yearNo: stringSchema,
        levelNo: stringSchema,
        studentGroupId: objectIdRQSchema,
        type: stringSchema,
        deliveryId: objectIdSchema,
        deliveryType: stringRQSchema,
        studentGroups: Joi.array()
            .items(
                Joi.object()
                    .keys({
                        group_id: objectIdRQSchema,
                        gender: stringSchema,
                        group_no: numberSchema,
                        group_name: stringSchema,
                        session_group: Joi.array()
                            .items(
                                Joi.object().keys({
                                    session_group_id: objectIdRQSchema,
                                    group_no: numberSchema,
                                    group_name: stringSchema,
                                }),
                            )
                            .optional(),
                        delivery_symbol: stringSchema,
                    })
                    .optional(),
            )
            .optional(),
        mode: stringSchema,
        subjects: Joi.array().items(
            Joi.object().keys({
                _subject_id: objectIdSchema,
                subject_name: stringSchema,
            }),
        ),
        staffs: arrayOfObjectIds,
        infraId: objectIdSchema,
        infraName: stringSchema,
        occurrences: Joi.array()
            .items(
                Joi.object()
                    .keys({
                        day: stringSchema,
                        start: Joi.object().keys({
                            hour: numberSchema,
                            minute: numberSchema,
                            format: stringSchema,
                        }),
                        end: Joi.object().keys({
                            hour: numberSchema,
                            minute: numberSchema,
                            format: stringSchema,
                        }),
                    })
                    .optional(),
            )
            .optional(),
        occurrenceBy: stringSchema,
        occurrenceWeek: arrayOfStringOptional,
        occurrenceStartDate: dateRQSchema,
        occurrenceEndDate: dateRQSchema,
        allowToScheduleOnEvents: booleanSchema,
        scheduleIds: objectIdSchema,
    }),
};

const multiScheduleValidation = {
    headers: Joi.object({
        _institution_id: objectIdRQSchema,
        user_id: objectIdRQSchema,
    }).unknown(),
    body: Joi.object({
        institutionCalendarId: objectIdRQSchema,
        programId: objectIdRQSchema,
        courseId: objectIdRQSchema,
        yearNo: stringSchema,
        levelNo: stringSchema,
        term: stringSchema,
        scheduleIds: arrayOfObjectIds,
    }),
};

const courseSchedulingDetailsValidation = {
    headers: Joi.object({
        _institution_id: objectIdRQSchema,
    }).unknown(),
    query: Joi.object({
        institutionCalendarId: objectIdRQSchema,
        programId: objectIdRQSchema,
        courseId: objectIdRQSchema,
        yearNo: stringSchema,
        levelNo: stringSchema,
        term: stringSchema,
    }),
};

module.exports = {
    eventListValidation,
    deleteScheduleValidation,
    courseTopicsValidation,
    availableListValidation,
    createSingleCourseScheduleValidation,
    courseDeliverySymbolValidation,
    editSingleCourseScheduleValidation,
    multiScheduleAnalysisValidation,
    multiScheduleValidation,
    courseSchedulingDetailsValidation,
};
