const {
    Schema,
    model,
    Types: { ObjectId },
} = require('mongoose');

const { FORM, STUDENT_RESPONSE, SINGLE, MULTIPLE } = require('../../common/utils/constants');
const { NOT_STARTED } = require('../../common/utils/enums');
const { rubric } = require('../rubric/rubric.schema');
const { pagesSchema } = require('../form/form.schema');
const { roleSchema } = require('../portfolio/portfolio.schema');
const constant = require('../../../lib/utility/constants');

const NameSchema = {
    first: String,
    middle: String,
    last: String,
    family: String,
};

const SessionSchema = {
    mode: { type: String, enum: [SINGLE, MULTIPLE] },
    startDate: { type: Date },
    endDate: { type: Date },
    start: {
        hour: { type: Number },
        minute: { type: Number },
        format: { type: String },
    },
    end: {
        hour: { type: Number },
        minute: { type: Number },
        format: { type: String },
    },
    date: { type: Date },
};

const PrepareAndPublishSchema = {
    formId: ObjectId,
    roleId: ObjectId,
    pages: pagesSchema,
    session: SessionSchema,
    userId: ObjectId,
    name: NameSchema,
    email: String,
    employeeId: String,
    gender: String,
    formTimestamps: {
        firstStartedAt: { type: Date },
        submittedAt: { type: Date },
        lastUpdated: { type: Date },
    },
    status: { type: String, default: NOT_STARTED },
    similarityCheckAttachment: {
        key: { type: String, trim: true },
        bucket: { type: String, trim: true },
        type: { type: String, trim: true },
    },
    isSimilarityCheck: { type: Boolean, default: false },
};

const schema = new Schema(
    {
        formId: { type: ObjectId, ref: FORM },
        student: {
            _id: { type: ObjectId, index: true },
            email: { type: String },
            name: {
                first: { type: String, trim: true },
                middle: { type: String, trim: true },
                last: { type: String, trim: true },
            },
            academicNo: { type: String, trim: true },
        },
        title: { type: String, trim: true },
        type: {
            code: { type: String, trim: true },
            name: { type: String, trim: true },
            _id: { type: ObjectId },
        },
        pages: pagesSchema,
        isDeleted: { type: Boolean, default: false },
        status: { type: String, default: NOT_STARTED },
        evaluationStatus: { type: String, default: NOT_STARTED },
        totalMarks: { type: Number },
        awardedMarks: { type: Number },
        portfolioId: { type: ObjectId },
        componentId: { type: ObjectId },
        childrenId: { type: ObjectId },
        parentPortfolioId: { type: ObjectId },
        isSimilarityCheck: { type: Boolean, default: false },
        similarityCheckAttachment: {
            key: { type: String, trim: true },
            bucket: { type: String, trim: true },
            type: { type: String, trim: true },
        },
        formTimestamps: {
            firstStartedAt: { type: Date },
            submittedAt: { type: Date },
            lastUpdated: { type: Date },
        },
        roles: [roleSchema],
        scheduleId: { type: ObjectId },
        reviews: [
            {
                userId: { type: ObjectId },
                externalEmail: { type: String },
                name: {
                    first: { type: String },
                    last: { type: String },
                    middle: { type: String },
                    family: { type: String },
                },
                createdAt: { type: Date },
                role: { type: String },
                reason: { type: String },
                status: { type: String },
                thread: [
                    {
                        name: {
                            first: { type: String },
                            last: { type: String },
                            middle: { type: String },
                            family: { type: String },
                        },
                        role: { type: String },
                        text: { type: String },
                        createdAt: { type: Date },
                    },
                ],
            },
        ],
        approvalStatus: { type: String, default: NOT_STARTED },
        globalRubricTotalPoints: { type: Number },
        globalRubricAwardedPoints: { type: Number },
        approvals: [
            {
                status: { type: String },
                createdAt: { type: Date },
                userId: { type: ObjectId, ref: constant.BY_USER },
                email: { type: String },
                evaluationCount: { type: Number, default: 0 },
                resubmitCount: { type: Number, default: 0 },
                revokeCount: { type: Number, default: 0 },
                submittingCount: { type: Number, default: 0 },
                globalRubricTotalPoints: { type: Number },
                globalRubricAwardedPoints: { type: Number },
                rubrics: [],
                globalRubrics: [],
                updatedAt: { type: Date },
                isExternalUser: { type: Boolean, default: false },
            },
        ],
        evaluations: [
            {
                status: { type: String },
                createdAt: { type: Date },
                userId: { type: ObjectId, ref: constant.BY_USER },
                email: { type: String },
                resubmitCount: { type: Number, default: 0 },
                rejectCount: { type: Number, default: 0 },
                revokeCount: { type: Number, default: 0 },
                totalMarks: { type: Number },
                awardedMarks: { type: Number },
                rubrics: [rubric],
                globalRubrics: [rubric],
                globalRubricTotalPoints: { type: Number },
                globalRubricAwardedPoints: { type: Number },
                updatedAt: { type: Date },
                isExternalUser: { type: Boolean, default: false },
            },
        ],
        prepareAndPublish: [PrepareAndPublishSchema],
        deliveryType: {
            deliveryTypeId: ObjectId,
            deliveryTypeSymbol: String,
            deliveryTypeName: String,
            sessionId: ObjectId,
        },
        session: SessionSchema,
        submissionHistory: [
            {
                status: { type: String },
                createdAt: { type: Date },
            },
        ],
        submissionSession: {
            date: { type: Date },
            end: { hour: { type: Number }, minute: { type: Number }, format: { type: String } },
            timeline: { type: String },
        },
    },
    { timestamps: true },
);

schema.index({
    portfolioId: 1,
    componentId: 1,
    childrenId: 1,
    parentPortfolioId: 1,
    'student._id': 1,
});

module.exports = model(STUDENT_RESPONSE, schema);
