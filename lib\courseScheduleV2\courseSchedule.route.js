const express = require('express');
const route = express.Router();
const catchAsync = require('../utility/catch-async');
const {
    eventList,
    courseTopics,
    availableList,
    createSingleCourseSchedule,
    courseDeliverySymbol,
    multiScheduleAnalysis,
    multiSchedule,
    courseSchedulingDetails,
    deleteSchedule,
    editSingleCourseSchedule,
} = require('./courseSchedule.controller');
const { userPolicyAuthentication, defaultPolicy } = require('../../middleware/policy.middleware');
const { validate } = require('../../middleware/validation');
const {
    eventListValidation,
    deleteScheduleValidation,
    courseTopicsValidation,
    availableListValidation,
    createSingleCourseScheduleValidation,
    courseDeliverySymbolValidation,
    editSingleCourseScheduleValidation,
    multiScheduleAnalysisValidation,
    multiScheduleValidation,
    courseSchedulingDetailsValidation,
} = require('./courseSchedule.validator');

route.get(
    '/eventList',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([
        {
            schema: eventListValidation.headers,
            property: 'headers',
        },
        {
            schema: eventListValidation.query,
            property: 'query',
        },
    ]),
    catchAsync(eventList),
);

route.get(
    '/courseTopics',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([
        {
            schema: courseTopicsValidation.headers,
            property: 'headers',
        },
        {
            schema: courseTopicsValidation.query,
            property: 'query',
        },
    ]),
    catchAsync(courseTopics),
);

route.get(
    '/availableList',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([
        {
            schema: availableListValidation.headers,
            property: 'headers',
        },
        {
            schema: availableListValidation.query,
            property: 'query',
        },
    ]),
    catchAsync(availableList),
);

route.post(
    '/createSingleCourseSchedule',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([
        {
            schema: createSingleCourseScheduleValidation.headers,
            property: 'headers',
        },
        {
            schema: createSingleCourseScheduleValidation.query,
            property: 'body',
        },
    ]),
    catchAsync(createSingleCourseSchedule),
);

route.delete(
    '/deleteSchedule',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([
        {
            schema: deleteScheduleValidation.query,
            property: 'query',
        },
    ]),
    catchAsync(deleteSchedule),
);

route.get(
    '/courseDeliverySymbol',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([
        {
            schema: courseDeliverySymbolValidation.headers,
            property: 'headers',
        },
        {
            schema: courseDeliverySymbolValidation.query,
            property: 'query',
        },
    ]),
    catchAsync(courseDeliverySymbol),
);

route.post(
    '/multiScheduleAnalysis',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([
        {
            schema: multiScheduleAnalysisValidation.headers,
            property: 'headers',
        },
        {
            schema: multiScheduleAnalysisValidation.query,
            property: 'body',
        },
    ]),
    catchAsync(multiScheduleAnalysis),
);

route.post(
    '/multiSchedule',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([
        {
            schema: multiScheduleValidation.headers,
            property: 'headers',
        },
        {
            schema: multiScheduleValidation.query,
            property: 'body',
        },
    ]),
    catchAsync(multiSchedule),
);

route.get(
    '/courseSchedulingDetails',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([
        {
            schema: courseSchedulingDetailsValidation.headers,
            property: 'headers',
        },
        {
            schema: courseSchedulingDetailsValidation.query,
            property: 'query',
        },
    ]),
    catchAsync(courseSchedulingDetails),
);

route.put(
    '/editSingleCourseSchedule',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([
        {
            schema: editSingleCourseScheduleValidation.headers,
            property: 'headers',
        },
        {
            schema: editSingleCourseScheduleValidation.query,
            property: 'query',
        },
    ]),
    catchAsync(editSingleCourseSchedule),
);

module.exports = route;
