const EvaluationModel = require('../evaluation/evaluation-mapping.model');
const PortfolioModel = require('../portfolio/portfolio.model');
const ProgramModel = require('../../../lib/models/digi_programs');
const StudentGroupModel = require('../../../lib/models/student_group');
const CourseScheduleModel = require('../../../lib/models/course_schedule');
const StudentResponseModel = require('../student-response/student-response.model');
const UserModel = require('../../../lib/models/user');
const StudentPortfolioModel = require('../portfolio/student-portfolio.model');
const CourseModel = require('../../../lib/models/digi_course');
const FormModel = require('../form/form.model');

const BaseHelper = require('../../base/base.helper');

const {
    convertToMongoObjectId,
    isIDEquals,
    removeDuplicatesFromArray,
    paginate,
    sortArrayByNestedObject,
    deepClone,
    isNumber,
    convertToString,
} = require('../../common/utils/common.util');
const {
    READY_TO_APPROVE,
    ALL_APPROVED,
    PARTIALLY_APPROVED,
    PARTIALLY_EVALUATE,
    ALL_EVALUATE,
    PUBLISHED,
    APPROVED,
    RESUBMIT,
    SUBMITTED,
    NOT_STARTED,
    REVOKE,
    PENDING,
    NO_SUBMISSION,
    IN_PROGRESS,
    EVALUATED,
    REJECTED,
} = require('../../common/utils/enums');
const {
    EVALUATOR,
    APPROVER,
    REGULAR,
    INFRASTRUCTURE,
    STUDENT_GROUP,
    STUDENT_LIST,
    MALE_LABEL,
    FEMALE_LABEL,
    PRESENT,
    STUDENT,
} = require('../../common/utils/constants');
const { arrayToMap } = require('../../common/utils/array.util');
const {
    getStudentListFromStudentGroup,
} = require('../../../lib/digi_class/course_session/course_session_service');
const {
    formatStudents,
    buildEvaluatorOrApproverQuery,
} = require('../evaluation/evaluation.helper');
const {
    buildStudentResponseQuery,
    getStudentStatusForApprover,
    getPortfolioStatus,
    getAssignedRoleForEvaluator,
    getMatchedRoleForEvaluator,
    isPortfolioGroupMatch,
    getEvaluatedExpectedSubmissionCount,
    getEvaluatedEntries,
    getApprovedEntries,
    getApproveExpectedSubmissionCount,
    getReceivedSubmissions,
    getStudentResponseForComponent,
    getApprovedResubmissionEntries,
    getEvaluateResubmissionEntries,
    getEvaluateRejectedEntries,
    getApprovalInsightQuery,
    getEvaluationInsightQuery,
    updateEvaluationAndApprovalStatusForStudents,
} = require('./evaluator.helper');
const {
    calculateStudentAchievedPoints,
    calculateRubricPoint,
    calculateGlobalRubricPoint,
} = require('../rubric/rubric.helper');
const { getCountFromStudentResponse } = require('../portfolio/portfolio.helper');
const { getFullDateIntoString } = require('../../common/utils/time.util');

const getEvaluatorAndApproverDashboard = async ({
    institutionCalendarId,
    type,
    chipType, // pending, completed, resubmit, reject
    limit = 10,
    pageNo = 1,
    skip,
    userId,
}) => {
    const { data: evaluations } = await BaseHelper.getDocuments({
        Model: EvaluationModel,
        query: buildEvaluatorOrApproverQuery({ institutionCalendarId, userId, type }),
        project: {
            portfolioId: 1,
            componentId: 1,
            childrenId: 1,
            deliveryTypeId: 1,
            typeOfEvaluation: 1,
            'students.studentId': 1,
            groups: 1,
        },
        canThrowError: false,
    });
    if (!evaluations?.length) {
        return {
            courses: [],
            count: {
                totalPortfolios: 0,
                approvalPending: 0,
                evaluated: 0,
                currentPage: pageNo,
                totalPages: 0,
            },
        };
    }

    const portfolioIds = removeDuplicatesFromArray(
        evaluations.map((evaluation) => evaluation.portfolioId.toString()),
    );
    const portfolioQuery = { _id: { $in: portfolioIds }, institutionCalendarId, isDeleted: false };
    const { data: allPortfolios = [] } = await BaseHelper.getDocuments({
        Model: PortfolioModel,
        query: portfolioQuery,
        project: { _id: 1 },
        canThrowError: false,
    });

    const isApprover = type === APPROVER;
    const isEvaluator = type === EVALUATOR;
    let approversAndEvaluators = await getCountFromStudentResponse({
        matchQuery: buildStudentResponseQuery({
            portfolioIds: allPortfolios.map(({ _id }) => _id),
            evaluations,
        }),
        canShowApprovedCount: isApprover,
        canShowApprovedResubmitCount: isApprover,
        canShowEvaluatedCount: isEvaluator,
        canShowEvaluatedResubmitCount: isEvaluator,
        canShowEvaluateRejectCount: isEvaluator,
    });
    if (isApprover) {
        approversAndEvaluators = approversAndEvaluators.filter(
            ({ peerReviewTotal }) => peerReviewTotal,
        );
    }

    const statusBuckets = { pending: [], completed: [], resubmit: [], reject: [] };
    approversAndEvaluators.forEach(({ portfolioId, ...item }) => {
        if (isApprover) {
            if (item.approvedResubmitCount) statusBuckets.resubmit.push(portfolioId);
            if (item.approved) statusBuckets.completed.push(portfolioId);
            else statusBuckets.pending.push(portfolioId);
        } else if (isEvaluator) {
            if (item.evaluatedResubmitCount) statusBuckets.resubmit.push(portfolioId);
            if (item.evaluateRejectCount) statusBuckets.reject.push(portfolioId);
            if (item.evaluated) statusBuckets.completed.push(portfolioId);
            else statusBuckets.pending.push(portfolioId);
        }
    });

    if (chipType) portfolioQuery._id.$in = statusBuckets[chipType];
    const { data: portfolios, totalCount } = await BaseHelper.getDocuments({
        Model: PortfolioModel,
        query: portfolioQuery,
        project: {
            programId: 1,
            courseId: 1,
            term: 1,
            year: 1,
            level: 1,
            rotation: 1,
            rotationCount: 1,
            curriculumName: 1,
        },
        skip,
        limit,
        canThrowError: false,
    });

    const courseIds = [];
    const programIds = [];
    portfolios.forEach(({ courseId, programId }) => {
        programId = programId.toString();
        if (!programIds.includes(programId)) programIds.push(programId);

        courseId = courseId.toString();
        if (!courseIds.includes(courseId)) courseIds.push(courseId);
    });

    const [{ data: programs = [] }, { data: studentGroups = [] }, { data: studentResponses = [] }] =
        await Promise.all([
            BaseHelper.getDocuments({
                Model: ProgramModel,
                query: { _id: { $in: programIds } },
                project: { name: 1, code: 1 },
                canThrowError: false,
            }),
            BaseHelper.getDocuments({
                Model: StudentGroupModel,
                query: {
                    _institution_calendar_id: institutionCalendarId,
                    'groups.courses._course_id': { $in: courseIds },
                },
                project: {
                    'master._program_id': 1,
                    'master.year': 1,
                    'groups.level': 1,
                    'groups.term': 1,
                    'groups.curriculum': 1,
                    'groups.courses.course_name': 1,
                    'groups.courses.course_no': 1,
                    'groups.courses._course_id': 1,
                    'groups.courses.rotation_count': 1,
                    'groups.courses.rotation': 1,
                    'groups.courses.setting.session_setting.groups._student_ids': 1,
                },
                canThrowError: false,
            }),
            BaseHelper.getDocuments({
                Model: StudentResponseModel,
                query: buildStudentResponseQuery({ portfolioIds, evaluations }),
                project: {
                    parentPortfolioId: 1,
                    childrenId: 1,
                    approvalStatus: 1,
                    'deliveryType.deliveryTypeId': 1,
                    'student._id': 1,
                    'roles.peerReview': 1,
                    'approvals.status': 1,
                    'approvals.userId': 1,
                    ...(isEvaluator && { 'evaluations.status': 1, 'evaluations.userId': 1 }),
                    status: 1,
                    _id: 0,
                },
                canThrowError: false,
            }),
        ]);

    const programsMap = arrayToMap({ array: programs, keyFn: (p) => convertToString(p._id) });
    const studentResponsesMap = arrayToMap({
        array: studentResponses,
        keyFn: (sr) => convertToString(sr.parentPortfolioId),
        group: true,
    });

    portfolios.forEach((portfolio) => {
        const studentIds = [];
        const course = {};
        const filteredStudentResponses = studentResponsesMap[convertToString(portfolio._id)] ?? [];
        const program = programsMap[convertToString(portfolio._id)];

        studentGroups.forEach((studentGroup) => {
            const { master, groups } = studentGroup;

            groups.forEach((group) => {
                const isMatchingGroup = isPortfolioGroupMatch({ group, master, portfolio });
                if (!isMatchingGroup) return;

                group.courses.forEach((groupCourse) => {
                    if (!isIDEquals(groupCourse._course_id, portfolio.courseId)) return;
                    if (!Object.keys(course).length) {
                        Object.assign(course, {
                            name: groupCourse.course_name,
                            code: groupCourse.course_no,
                        });
                    }

                    groupCourse?.setting?.forEach((setting) => {
                        setting?.session_setting?.forEach((session) => {
                            session?.groups?.forEach((sessionGroup) => {
                                if (sessionGroup?._student_ids?.length) {
                                    studentIds.push(
                                        ...sessionGroup._student_ids.map((id) =>
                                            convertToString(id),
                                        ),
                                    );
                                }
                            });
                        });
                    });
                });
            });
        });

        // Build count object
        const count = {
            studentCount: removeDuplicatesFromArray(studentIds)?.length || 0,
            approveExpectedSubmissionCount: 0,
            evaluatedExpectedSubmissionCount: 0,
            receivedSubmissionsCount: 0,
            totalSubmissionsExpected: 0,
            approvedEntries: 0,
            approvalPending: 0,
            ...(isEvaluator && {
                evaluatedEntries: 0,
                evaluatedPending: 0,
            }),
        };

        const receivedSubmissions = getReceivedSubmissions({
            studentResponses: filteredStudentResponses,
            isApprover,
        });
        count.approveExpectedSubmissionCount = getApproveExpectedSubmissionCount({
            receivedSubmissions,
        });
        count.receivedSubmissionsCount = receivedSubmissions.length;
        count.totalSubmissionsExpected = filteredStudentResponses.length;
        count.approvedEntries = getApprovedEntries({ userId, isApprover, receivedSubmissions });
        count.approvalPending = count.approveExpectedSubmissionCount - count.approvedEntries; // change

        if (isEvaluator) {
            count.evaluatedExpectedSubmissionCount = getEvaluatedExpectedSubmissionCount({
                receivedSubmissions,
            });
            count.evaluatedRejected = getEvaluateRejectedEntries({
                receivedSubmissions,
                userId,
                isEvaluator,
            });
            count.evaluatedEntries = getEvaluatedEntries({ receivedSubmissions, userId });
            count.evaluatedPending =
                count.evaluatedExpectedSubmissionCount -
                count.evaluatedEntries -
                count.evaluatedRejected;
        }

        // Assign base status and flags
        portfolio.count = count;
        portfolio.status = READY_TO_APPROVE;
        portfolio.canApproveOrEvaluate = false;

        const { status, canApproveOrEvaluate } = getPortfolioStatus({
            type,
            receivedSubmissionsCount: count.receivedSubmissionsCount,
            totalSubmissionsExpected: count.totalSubmissionsExpected,
            approvalPending: count.approvalPending,
            approvedEntries: count.approvedEntries,
            evaluatedEntries: count?.evaluatedEntries,
            evaluatedPending: count?.evaluatedPending,
            hasApprover: portfolio?.components?.some((component) => {
                return component.children.some((child) =>
                    child.roles.some((role) => role.peerReview),
                );
            }),
        });

        portfolio.status = status;
        portfolio.canApproveOrEvaluate = canApproveOrEvaluate;

        // Enrich portfolio with program & course info
        portfolio.program = {
            name: program?.name || '',
            code: program?.code || '',
        };

        portfolio.course = {
            name: course?.name || '',
            code: course?.code || '',
        };

        // Timestamp update
        portfolio.updatedAt = new Date();
    });

    // TODO: Evaluate the count logic
    return {
        courses: portfolios,
        count: {
            totalPortfolios: allPortfolios.length,
            pending: statusBuckets.pending.length,
            completed: statusBuckets.completed.length,
            resubmit: statusBuckets.resubmit.length,
            reject: statusBuckets.reject.length,
            totalCount,
            currentPage: pageNo,
            totalPages: Math.ceil(totalCount / limit),
        },
    };
};

const getListOfComponentsForDashboard = async ({ portfolioId, type, userId }) => {
    const isEvaluator = type === EVALUATOR;
    const isApprover = type === APPROVER;

    const portfolio = await BaseHelper.getDocument({
        Model: PortfolioModel,
        query: { _id: convertToMongoObjectId(portfolioId), status: PUBLISHED },
        project: {
            'components._id': 1,
            'components.name': 1,
            'components.code': 1,
            'components.isLogBook': 1,
            'components.children._id': 1,
            'components.deliveryTypes': 1,
            'components.children.roles.peerReview': 1,
            term: 1,
            year: 1,
            level: 1,
            rotation: 1,
            rotationCount: 1,
            curriculumId: 1,
            curriculumName: 1,
            institutionCalendarId: 1,
            programId: 1,
            courseId: 1,
        },
        canThrowError: false,
    });
    if (!portfolio) return { components: [] };

    const { data: evaluations = [] } = await BaseHelper.getDocuments({
        Model: EvaluationModel,
        query: buildEvaluatorOrApproverQuery({ portfolioId, userId, type }),
        project: {
            componentId: 1,
            childrenId: 1,
            deliveryTypeId: 1,
            typeOfEvaluation: 1,
            'students.studentId': 1,
        },
        canThrowError: false,
    });

    const { data: studentResponses = [] } = await BaseHelper.getDocuments({
        Model: StudentResponseModel,
        query: buildStudentResponseQuery({ portfolioIds: [portfolioId], evaluations }),
        project: {
            _id: 0,
            parentPortfolioId: 1,
            childrenId: 1,
            'deliveryType.deliveryTypeId': 1,
            'student._id': 1,
            componentId: 1,
            status: 1,
            approvalStatus: 1,
            evaluationStatus: 1,
            'roles.peerReview': 1,
            'approvals.status': 1,
            'approvals.userId': 1,
            ...(isEvaluator && { 'evaluations.status': 1, 'evaluations.userId': 1 }),
        },
        canThrowError: false,
    });

    let isLogBook = false;
    const deliveryTypes = [];
    const filteredComponents = portfolio.components.filter((component) => {
        if (component.isLogBook) {
            isLogBook = true;
            deliveryTypes.push(...component.deliveryTypes);
        }

        return component.children.some((child) =>
            evaluations.some(
                (evaluation) =>
                    isIDEquals(evaluation.componentId, component._id) &&
                    isIDEquals(evaluation.childrenId, child._id),
            ),
        );
    });

    evaluations.forEach((evaluation) => {
        const deliveryType = deliveryTypes.find((dt) =>
            isIDEquals(dt.deliveryTypeId, evaluation.deliveryTypeId),
        );
        if (deliveryType) evaluation.deliveryType = deliveryType;
    });

    const studentData = isLogBook
        ? await getStudentListFromStudentGroup({
              programId: portfolio.programId,
              year: portfolio.year,
              level: portfolio.level,
              ...(portfolio.rotation &&
                  portfolio.rotation === 'yes' && { rotation: portfolio.rotation }),
              ...(portfolio.rotationCount && { rotationCount: portfolio.rotationCount }),
              term: portfolio.term,
              courseId: portfolio.courseId,
              institutionCalendarId: portfolio.institutionCalendarId,
          })
        : {};

    const components = filteredComponents.map((component) => {
        const filterEvaluations = evaluations.filter((evaluation) =>
            isIDEquals(evaluation.componentId, component._id),
        );

        const count = {
            studentCount: 0,
            evaluatedExpectedSubmissionCount: 0,
            receivedSubmissionsCount: 0,
            totalSubmissionsExpected: 0,
            approvedEntries: 0,
            approvalPending: 0,
            ...(isEvaluator && {
                evaluatedEntries: 0,
                evaluatedPending: 0,
            }),
        };

        let studentIds = [];

        const hasApprover = component.children.some((child) =>
            child.roles.some((role) => role.peerReview),
        );
        if (component.isLogBook) {
            const { masterGroup: groups } = studentData;
            groups.forEach((group) => {
                if (
                    evaluations.some((e) =>
                        isIDEquals(e.deliveryType?.deliveryTypeSymbol, group.delivery_symbol),
                    )
                ) {
                    group.session_group?.forEach((sessionGroup) => {
                        studentIds.push(
                            ...sessionGroup._student_ids.map((id) => convertToString(id)),
                        );
                    });
                }
            });
            studentIds = removeDuplicatesFromArray(studentIds);
        } else {
            filterEvaluations.forEach((evaluation) => {
                evaluation.students.forEach((student) => {
                    studentIds.push(student.studentId.toString());
                });
            });
            studentIds = removeDuplicatesFromArray(studentIds);
        }

        const studentResponseForComponent = getStudentResponseForComponent({
            studentResponses,
            studentIds,
            componentId: component._id,
        });
        const receivedSubmissions = getReceivedSubmissions({
            studentResponses: studentResponseForComponent,
            isApprover,
        });

        count.receivedSubmissionsCount = receivedSubmissions.length;
        count.totalSubmissionsExpected = studentResponseForComponent?.length || 0;
        count.approvedEntries = getApprovedEntries({ receivedSubmissions, isApprover, userId });
        count.approveExpectedSubmissionCount = getApproveExpectedSubmissionCount({
            receivedSubmissions,
        });
        count.approvalPending = count.approveExpectedSubmissionCount - count.approvedEntries;
        if (isEvaluator) {
            count.evaluatedExpectedSubmissionCount = getEvaluatedExpectedSubmissionCount({
                receivedSubmissions,
            });
            count.evaluatedRejected = getEvaluateRejectedEntries({
                receivedSubmissions,
                userId,
                isEvaluator,
            });

            count.evaluatedEntries = getEvaluatedEntries({ receivedSubmissions, userId });
            count.evaluationPending = hasApprover
                ? count.approvedEntries - count.evaluatedEntries - count.evaluatedRejected
                : count.evaluatedExpectedSubmissionCount -
                  count.evaluatedEntries -
                  count.evaluatedRejected;
        }

        const {
            receivedSubmissionsCount,
            totalSubmissionsExpected,
            approvedEntries,
            approvalPending,
            evaluatedEntries,
            evaluationPending,
        } = count;

        const { status, canApproveOrEvaluate } = getPortfolioStatus({
            type,
            receivedSubmissionsCount,
            totalSubmissionsExpected,
            approvalPending,
            approvedEntries,
            evaluatedEntries,
            evaluationPending,
            hasApprover,
        });

        return {
            _id: component._id,
            name: component.name,
            code: component.code,
            isLogBook: component.isLogBook,
            status,
            canApproveOrEvaluate,
            count,
            updatedAt: new Date(),
        };
    });

    return { components };
};

const getApproverOrEvaluatorComponentsWithCount = async ({ portfolioId, type, userId }) => {
    const portfolio = await BaseHelper.getDocument({
        Model: PortfolioModel,
        query: { _id: convertToMongoObjectId(portfolioId), status: PUBLISHED },
        project: {
            'components._id': 1,
            'components.name': 1,
            'components.code': 1,
            'components.isLogBook': 1,
            'components.hasConsolidateEvaluation': 1,
            'components.hasReflection': 1,
            'components.hasExtraEntries': 1,
            'components.children._id': 1,
            'components.timeline': 1,
            term: 1,
            year: 1,
            level: 1,
            rotation: 1,
            rotationCount: 1,
            curriculumId: 1,
            curriculumName: 1,
        },
        canThrowError: false,
    });
    if (!portfolio) return { components: [] };

    const { data: evaluations = [] } = await BaseHelper.getDocuments({
        Model: EvaluationModel,
        query: buildEvaluatorOrApproverQuery({ portfolioId, userId, type }),
        project: {
            portfolioId: 1,
            componentId: 1,
            childrenId: 1,
            deliveryTypeId: 1,
            typeOfEvaluation: 1,
            'students.studentId': 1,
        },
        canThrowError: false,
    });

    const filteredComponents = portfolio.components.filter((component) => {
        return component.children.some((child) =>
            evaluations.some(
                (evaluation) =>
                    isIDEquals(evaluation.componentId, component._id) &&
                    isIDEquals(evaluation.childrenId, child._id),
            ),
        );
    });

    const components = filteredComponents.map((component) => {
        return {
            _id: component._id,
            name: component.name,
            code: component.code,
            isLogBook: component.isLogBook,
            hasConsolidateEvaluation: component.hasConsolidateEvaluation,
            hasReflection: component.hasReflection,
            hasExtraEntries: component.hasExtraEntries,
            ...(component.timeline && { timeline: component.timeline }),
            count: component.children.length,
        };
    });

    return {
        components,
        term: portfolio.term,
        year: portfolio.year,
        level: portfolio.level,
        rotation: portfolio.rotation,
        ...(portfolio?.rotationCount && { rotationCount: portfolio.rotationCount }),
        curriculumId: portfolio.curriculumId,
        curriculumName: portfolio.curriculumName,
    };
};

const getComponentChildren = async ({ portfolioId, componentId, type, userId }) => {
    const [{ data: evaluations = [] }, portfolio] = await Promise.all([
        BaseHelper.getDocuments({
            Model: EvaluationModel,
            query: buildEvaluatorOrApproverQuery({
                portfolioId,
                componentId,
                userId,
                type,
            }),
            project: { childrenId: 1, deliveryTypeId: 1, 'students.studentId': 1 },
            canThrowError: false,
        }),
        BaseHelper.getDocument({
            Model: PortfolioModel,
            query: { _id: convertToMongoObjectId(portfolioId) },
            project: {
                'components._id': 1,
                'components.children._id': 1,
                'components.children.name': 1,
                'components.children.session': 1,
            },
            errorMessage: 'PORTFOLIO_NOT_FOUND',
        }),
    ]);

    if (!portfolio?.components?.length) return { children: [] };

    const component = portfolio.components.find((c) => isIDEquals(c._id, componentId));
    if (!component?.children?.length) return { children: [] };

    const evaluationChildIds = new Set(evaluations.map((e) => String(e.childrenId)));
    const filteredChildren = component.children.filter((child) =>
        evaluationChildIds.has(String(child._id)),
    );

    const isEvaluator = type === EVALUATOR;
    const isApprover = type === APPROVER;
    const { data: studentResponses = [] } = await BaseHelper.getDocuments({
        Model: StudentResponseModel,
        query: buildStudentResponseQuery({ portfolioIds: [portfolioId], evaluations }),
        project: {
            status: 1,
            childrenId: 1,
            approvalStatus: 1,
            evaluationStatus: 1,
            'approvals.status': 1,
            'approvals.userId': 1,
            'roles.peerReview': 1,
            ...(isEvaluator && { 'evaluations.status': 1, 'evaluations.userId': 1 }),
        },
        canThrowError: false,
    });

    return filteredChildren.map((child) => {
        const count = {};
        let status = NO_SUBMISSION;
        const responses = studentResponses.filter((studentResponse) =>
            isIDEquals(studentResponse.childrenId, child._id),
        );
        const receivedSubmissions = getReceivedSubmissions({
            studentResponses: responses,
            isApprover,
        });
        count.totalStudents = responses.length;
        count.receivedSubmissionsCount = receivedSubmissions.length;
        if (count.receivedSubmissionsCount === 0) status = NO_SUBMISSION;

        if (isApprover) {
            count.all = getApproveExpectedSubmissionCount({ receivedSubmissions });
            count.resubmission = getApprovedResubmissionEntries({
                receivedSubmissions,
                userId,
                isApprover,
            });
            count.completed = getApprovedEntries({ receivedSubmissions, userId, isApprover });
            count.pending = count.all - count.completed;
            count.totalSubmissionsExpected = count.all;

            if (count.pending && !count.completed) status = PENDING;
            else if (count.pending && count.completed) status = PARTIALLY_APPROVED;
            else if (count.all === count.completed) status = ALL_APPROVED;
        } else {
            count.all = receivedSubmissions.length;
            count.evaluateExpectedCount = getEvaluatedExpectedSubmissionCount({
                receivedSubmissions,
            });
            count.resubmission = getEvaluateResubmissionEntries({
                receivedSubmissions,
                isEvaluator,
                userId,
            });
            count.rejected = getEvaluateRejectedEntries({
                receivedSubmissions,
                isEvaluator,
                userId,
            });
            count.completed = getEvaluatedEntries({ receivedSubmissions, userId });
            count.pending = count.evaluateExpectedCount - count.completed - count.rejected;

            if (count.pending && !count.completed) status = PENDING;
            else if (count.pending && count.completed) status = PARTIALLY_EVALUATE;
            else if (count.all === count.completed) status = ALL_EVALUATE;
        }

        return { _id: child._id, name: child.name, session: child.session || {}, count, status };
    });
};

const getApproverOrEvaluatorOverallCount = async ({ portfolioId, componentId, type, userId }) => {
    const { data: evaluations = [] } = await BaseHelper.getDocuments({
        Model: EvaluationModel,
        query: buildEvaluatorOrApproverQuery({ portfolioId, componentId, userId, type }),
        project: { childrenId: 1, deliveryTypeId: 1, 'students.studentId': 1 },
        canThrowError: false,
    });

    const count = {
        all: 0,
        readyForEvaluation: 0,
        completed: 0,
        pending: 0,
        resubmission: 0,
        rejected: 0,
    };
    const studentResponseQuery = buildStudentResponseQuery({
        portfolioIds: [portfolioId],
        evaluations,
        isSubmitted: true,
    });

    if (type === APPROVER) {
        const approvalQuery = getApprovalInsightQuery({ userId, query: studentResponseQuery });
        studentResponseQuery['roles.peerReview'] = true;

        [count.all, count.completed, count.resubmission] = await Promise.all([
            BaseHelper.getDocumentsCount({
                Model: StudentResponseModel,
                query: approvalQuery.all,
            }),
            BaseHelper.getDocumentsCount({
                Model: StudentResponseModel,
                query: approvalQuery.completed,
            }),
            BaseHelper.getDocumentsCount({
                Model: StudentResponseModel,
                query: approvalQuery.resubmit,
            }),
        ]);

        count.pending = count.all - count.completed;
    } else {
        const evaluationQuery = getEvaluationInsightQuery({ userId, query: studentResponseQuery });

        [count.all, count.readyForEvaluation, count.completed, count.resubmission, count.rejected] =
            await Promise.all([
                BaseHelper.getDocumentsCount({
                    Model: StudentResponseModel,
                    query: studentResponseQuery,
                }),
                BaseHelper.getDocumentsCount({
                    Model: StudentResponseModel,
                    query: evaluationQuery.readyForEvaluation,
                }),
                BaseHelper.getDocumentsCount({
                    Model: StudentResponseModel,
                    query: evaluationQuery.completed,
                }),
                BaseHelper.getDocumentsCount({
                    Model: StudentResponseModel,
                    query: evaluationQuery.resubmit,
                }),
                BaseHelper.getDocumentsCount({
                    Model: StudentResponseModel,
                    query: evaluationQuery.rejected,
                }),
            ]);

        count.pending = count.all - count.completed;
    }

    return count;
};

const getScheduleForApproverOrEvaluator = async ({
    portfolioId,
    componentId,
    type,
    chipType,
    userId,
}) => {
    const [{ data: evaluations = [] }, portfolio] = await Promise.all([
        BaseHelper.getDocuments({
            Model: EvaluationModel,
            query: buildEvaluatorOrApproverQuery({
                portfolioId,
                componentId,
                userId,
                type,
            }),
            project: { childrenId: 1, deliveryTypeId: 1, 'students.studentId': 1 },
            canThrowError: false,
        }),
        BaseHelper.getDocument({
            Model: PortfolioModel,
            query: { _id: convertToMongoObjectId(portfolioId) },
            project: {
                institutionCalendarId: 1,
                programId: 1,
                courseId: 1,
                year: 1,
                term: 1,
                level: 1,
                rotation: 1,
                rotationCount: 1,
                'components._id': 1,
                'components.deliveryTypes': 1,
                'components.isLogBook': 1,
                'components.children._id': 1,
                'components.children.name': 1,
                'components.children.formId': 1,
                'components.children.hasSimilarityCheck': 1,
            },
            errorMessage: 'PORTFOLIO_NOT_FOUND',
        }),
    ]);
    if (!evaluations.length) return { schedule: [] };

    const component = portfolio.components.find((c) => isIDEquals(c._id, componentId));
    if (!component?.deliveryTypes?.length || !component.isLogBook) return { schedule: [] };

    component.deliveryTypes = component.deliveryTypes.filter((dt) =>
        evaluations.some((ev) => isIDEquals(ev.deliveryTypeId, dt.deliveryTypeId)),
    );
    const isEvaluator = type === EVALUATOR;

    let studentResponseQuery = buildStudentResponseQuery({
        portfolioIds: [portfolioId],
        evaluations,
    });
    if (chipType) {
        const chipTypeQuery = isEvaluator
            ? getEvaluationInsightQuery({
                  userId,
                  query: studentResponseQuery,
              })
            : getApprovalInsightQuery({ userId, query: studentResponseQuery });

        studentResponseQuery = chipTypeQuery[chipType] ?? studentResponseQuery;
    }

    const [{ data: courseSchedules = [] }, { data: studentResponses = [] }] = await Promise.all([
        BaseHelper.getDocuments({
            Model: CourseScheduleModel,
            query: {
                _institution_calendar_id: portfolio.institutionCalendarId,
                _program_id: portfolio.programId,
                _course_id: portfolio.courseId,
                year_no: portfolio.year,
                term: portfolio.term,
                level_no: portfolio.level,
                isDeleted: false,
                isActive: true,
                type: REGULAR,
            },
            project: { scheduleDate: '$schedule_date', session: 1, start: 1, end: 1 },
            canThrowError: false,
        }),
        BaseHelper.getDocuments({
            Model: StudentResponseModel,
            query: studentResponseQuery,
            project: {
                status: 1,
                scheduleId: 1,
                childrenId: 1,
                'approvals.status': 1,
                'approvals.userId': 1,
                'roles.peerReview': 1,
                'student._id': 1,
                'deliveryType.deliveryTypeId': 1,
                ...(isEvaluator && {
                    'evaluations.status': 1,
                    'evaluations.userId': 1,
                    approvalStatus: 1,
                }),
            },
            canThrowError: false,
        }),
    ]);
    if (!courseSchedules.length) return { schedule: [] };

    const filteredSchedules = courseSchedules
        .filter((schedule) =>
            component.deliveryTypes.some((dt) =>
                isIDEquals(dt.deliveryTypeSymbol, schedule.session.delivery_symbol),
            ),
        )
        .map((schedule) => ({ ...schedule, children: component.children }));

    const formattedSchedules = filteredSchedules.flatMap(({ children, ...rest }) =>
        children.map((child) => ({ ...rest, child })),
    );

    const generateResponseKey = ({ scheduleId, childId, deliveryTypeId }) => {
        return [
            convertToString(scheduleId),
            convertToString(childId),
            convertToString(deliveryTypeId),
        ].join('|');
    };
    const responseMap = new Map();
    studentResponses.forEach((studentResponse) => {
        const key = generateResponseKey({
            scheduleId: studentResponse.scheduleId,
            childId: studentResponse.childrenId,
            deliveryTypeId: studentResponse?.deliveryType?.deliveryTypeId,
        });

        if (!responseMap.has(key)) responseMap.set(key, []);

        responseMap.get(key).push(studentResponse);
    });

    formattedSchedules.forEach((schedule) => {
        schedule.deliveryType = component.deliveryTypes.find((deliveryType) =>
            isIDEquals(deliveryType.deliveryTypeSymbol, schedule.session.delivery_symbol),
        );
        const matchedStudentResponses =
            responseMap.get(
                generateResponseKey({
                    scheduleId: schedule._id,
                    childId: schedule.child._id,
                    deliveryTypeId: schedule.deliveryType?.deliveryTypeId,
                }),
            ) || [];

        schedule.hasApprover = matchedStudentResponses.some(({ roles = [] }) =>
            roles.some(({ peerReview }) => peerReview),
        );
        schedule.count = {
            totalStudents: matchedStudentResponses.length,
            submitted: 0,
            approved: 0,
            evaluated: 0,
            evaluationExpected: 0,
            approvalPending: 0,
            evaluationPending: 0,
        };
        matchedStudentResponses.forEach((studentResponse) => {
            if (studentResponse.status === SUBMITTED) {
                schedule.count.submitted++;

                if (isEvaluator) {
                    if (studentResponse.approvalStatus === APPROVED) schedule.count.approved++;
                    if (!schedule.hasApprover || studentResponse.approvalStatus === APPROVED) {
                        schedule.count.evaluationExpected++;
                    }

                    if (
                        studentResponse.evaluations?.some(
                            (approval) =>
                                approval.status === EVALUATED &&
                                isIDEquals(approval.userId, userId),
                        )
                    ) {
                        schedule.count.evaluated++;
                    }
                } else if (
                    studentResponse.approvals?.some(
                        (approval) =>
                            approval.status === APPROVED && isIDEquals(approval.userId, userId),
                    )
                ) {
                    schedule.count.approved++;
                }
            }
        });

        schedule.count.approvalPending = schedule.count.submitted - schedule.count.approved;
        schedule.count.evaluationPending =
            schedule.count.evaluationExpected - schedule.count.evaluated;
    });

    const getStatus = (schedule) =>
        getPortfolioStatus({
            type,
            evaluatedPending: schedule.count.evaluationPending,
            approvalPending: schedule.count.approvalPending,
            evaluatedEntries: schedule.count.evaluated,
            approvedEntries: schedule.count.approved,
            receivedSubmissionsCount: schedule.count.submitted,
            totalSubmissionsExpected: schedule.count.totalStudents,
            hasApprover: schedule.hasApprover,
        });

    const groupedSchedules = [];
    formattedSchedules.forEach((schedule) => {
        const dateStr = getFullDateIntoString(schedule.scheduleDate);
        let group = groupedSchedules.find((g) => g.dateString === dateStr);

        if (!group) {
            group = {
                dateString: dateStr,
                date: schedule.scheduleDate,
                count: {
                    totalStudents: 0,
                    submitted: 0,
                    approved: 0,
                    evaluated: 0,
                    approvalPending: 0,
                    evaluationPending: 0,
                    evaluationExpected: 0,
                },
                sessions: [],
                ...getStatus(schedule),
            };

            groupedSchedules.push(group);
        }

        group.sessions.push(schedule);

        group.hasApprover = schedule.hasApprover;
        group.count.totalStudents = schedule.count?.totalStudents || 0;
        group.count.submitted += schedule.count?.submitted || 0;
        group.count.approved += schedule.count?.approved || 0;
        group.count.evaluated += schedule.count?.evaluated || 0;
        group.count.approvalPending += schedule.count?.approvalPending || 0;
        group.count.evaluationPending += schedule.count?.evaluationPending || 0;
        group.count.evaluationExpected += schedule.count?.evaluationExpected || 0;
    });
    groupedSchedules.forEach((schedule) => {
        Object.assign(schedule, getStatus(schedule));
    });

    return { schedule: groupedSchedules };
};

const getStudentsForSchedule = async (params) => {
    const {
        portfolioId,
        componentId,
        childrenId,
        scheduleId,
        term,
        year,
        level,
        rotation,
        rotationCount,
        type,
        userId,
        pageNo = 1,
        limit = 10,
    } = params;

    const evaluationQuery = buildEvaluatorOrApproverQuery({
        portfolioId,
        componentId,
        childrenId,
        deliveryTypeId: params.deliveryTypeId,
        userId,
        type,
    });

    const evaluation = await EvaluationModel.findOne(evaluationQuery, {
        typeOfEvaluation: 1,
        students: 1,
        infrastructures: 1,
        groups: 1,
        programId: 1,
        courseId: 1,
        institutionCalendarId: 1,
        deliveryTypeSymbol: 1,
    }).lean();

    const defaultStudentData = {
        students: [],
        count: {
            total: 0,
            maleStudentCount: 0,
            femaleStudentCount: 0,
            currentPage: pageNo,
            totalPages: 0,
        },
    };

    if (!evaluation) return defaultStudentData;

    // --- portfolio + child + roleId
    const portfolio = await PortfolioModel.findOne(
        { _id: convertToMongoObjectId(portfolioId) },
        {
            'components._id': 1,
            'components.timeline': 1,
            'components.deliveryTypes': 1,
            'components.children.session': 1,
            'components.hasConsolidateEvaluation': 1,
            'components.children.formMarks': 1,
            'components.children.hasSimilarityCheck': 1,
            'components.children._id': 1,
            'components.isLogBook': 1,
            'components.children.hasMultipleApprover': 1,
            'components.children.hasMultipleEvaluator': 1,
            'components.children.roles': 1,
        },
    ).lean();

    const component = portfolio.components.find((c) => isIDEquals(c._id, componentId));
    const child = component.children.find((c) => isIDEquals(c._id, childrenId));
    if (!child) return defaultStudentData;

    child.hasApprover = child.roles?.some((role) => role.peerReview);
    const hasMultipleEvaluatorRole = child.roles?.filter((role) => role.evaluator).length > 1;

    const conditions = {
        [STUDENT]: (role) => role?.type === STUDENT,
        [EVALUATOR]: (role) => role?.evaluate?.isEnabled,
        [APPROVER]: (role) => role?.peerReview,
    };
    const roleId = child.roles?.find((role) => conditions[type](role))?.roleId;

    const matchedAssignedRole =
        type === EVALUATOR && hasMultipleEvaluatorRole
            ? getMatchedRoleForEvaluator({ evaluation, type: 'evaluator', userId })
            : null;

    const evaluateData = getAssignedRoleForEvaluator({
        hasMultipleEvaluatorRole,
        child,
        ...(matchedAssignedRole && { roleId: matchedAssignedRole?.roleId }),
    });

    const totalMarks = evaluateData?.isMarksEnabled
        ? evaluateData.marks
        : calculateRubricPoint({ rubrics: evaluateData?.rubrics });

    const courseSchedule =
        component?.isLogBook && scheduleId
            ? await CourseScheduleModel.findOne(
                  { _id: scheduleId },
                  {
                      scheduleDate: '$schedule_date',
                      start: 1,
                      end: 1,
                      'students._id': 1,
                      'students.status': 1,
                  },
              ).lean()
            : null;

    // --- helpers
    const countGender = (list) =>
        list.reduce(
            (acc, curr) => {
                if (curr.gender === MALE_LABEL) acc.maleStudentCount++;
                else if (curr.gender === FEMALE_LABEL) acc.femaleStudentCount++;
                return acc;
            },
            { maleStudentCount: 0, femaleStudentCount: 0 },
        );

    const buildReturn = async ({ students = [], users = [] }) => {
        const genderCounts = countGender(students);
        const paginated = paginate(students, limit, pageNo);

        const responses = await StudentResponseModel.find(
            {
                parentPortfolioId: portfolioId,
                componentId,
                childrenId,
                ...(scheduleId && { scheduleId }),
                'student._id': { $in: paginated.map((s) => convertToMongoObjectId(s.studentId)) },
            },
            {
                status: 1,
                'approvals.userId': 1,
                'approvals.status': 1,
                'student._id': 1,
                'formTimestamps.submittedAt': 1,
                'evaluations.userId': 1,
                'evaluations.status': 1,
                approvalStatus: 1,
                evaluationStatus: 1,
                session: 1,
            },
        ).lean();

        updateEvaluationAndApprovalStatusForStudents({
            students: paginated,
            responses,
            scheduleStudents: courseSchedule?.students || [],
            isApprover: type === APPROVER,
            userId,
            child,
            users,
        });

        return {
            students: paginated,
            child,
            component,
            count: {
                total: students.length,
                ...genderCounts,
                currentPage: pageNo,
                totalPages: Math.ceil(students.length / limit),
            },
            session: {
                date: courseSchedule?.scheduleDate,
                start: courseSchedule?.start || {},
                end: courseSchedule?.end || {},
            },
            totalMarks,
            roleId,
        };
    };

    // --- branching
    if (evaluation.typeOfEvaluation === STUDENT_LIST) {
        const users = [];

        evaluation.students?.forEach((student) => {
            users.push(...student.roles.flatMap((r) => r.users));
        });

        return buildReturn({ students: evaluation.students || [], users });
    }

    const studentData = await getStudentListFromStudentGroup({
        programId: evaluation.programId,
        year,
        level,
        ...(rotation && { rotation }),
        ...(rotationCount && { rotationCount }),
        term,
        courseId: evaluation.courseId,
        institutionCalendarId: evaluation.institutionCalendarId,
    });

    const students = studentData.sgStudentList || [];
    const groups = studentData.masterGroup || [];

    if (evaluation.typeOfEvaluation === INFRASTRUCTURE) {
        const matchedGroups = groups.filter(
            (g) => g.delivery_symbol === evaluation.deliveryTypeSymbol,
        );
        const studentIds = matchedGroups.flatMap((g) =>
            g.session_group.flatMap((sg) => sg._student_ids || []),
        );
        const filtered = students.filter((s) =>
            studentIds.some((id) => isIDEquals(id, s._student_id)),
        );

        const users = [];
        evaluation.infrastructures.forEach((infra) => {
            infra.roles.forEach((role) => {
                users.push(...role.users);
            });
        });

        return buildReturn({
            students: formatStudents({ students: filtered, canCount: false }),
            users,
        });
    }

    if (evaluation.typeOfEvaluation === STUDENT_GROUP) {
        const studentIds = evaluation.groups.length
            ? groups.flatMap((group) =>
                  group.session_group.flatMap((sg) =>
                      evaluation.groups.some(
                          (g) =>
                              isIDEquals(g.groupId, sg._id) &&
                              g.roles.some((r) =>
                                  r.users.some((u) => isIDEquals(u.userId, userId)),
                              ),
                      )
                          ? sg._student_ids
                          : [],
                  ),
              )
            : [];

        const filtered = students.filter((s) =>
            studentIds.some((id) => isIDEquals(id, s._student_id)),
        );

        evaluation.students?.forEach((s) => delete s.roles);

        const combined = evaluation.students?.length
            ? [...formatStudents({ students: filtered, canCount: false }), ...evaluation.students]
            : formatStudents({ students: filtered, canCount: false });

        sortArrayByNestedObject({ array: combined, nestedKey: 'academicNo' });

        const users = [];
        evaluation.groups?.forEach((group) => {
            group.roles.forEach((role) => {
                users.push(...role.users);
            });
        });

        return buildReturn({ students: combined, users });
    }

    return defaultStudentData;
};

const updateApproveOrRejectStatus = async ({
    portfolioId,
    componentId,
    childrenId,
    scheduleIds = [],
    studentIds = [],
    type,
    userId,
    session = {},
    isExternalUser = false,
}) => {
    const studentResponses = await StudentResponseModel.find(
        {
            $or: [
                { parentPortfolioId: convertToMongoObjectId(portfolioId) },
                { portfolioId: convertToMongoObjectId(portfolioId) },
            ],
            ...(componentId && { componentId }),
            ...(childrenId && { childrenId }),
            ...(scheduleIds.length && {
                scheduleId: { $in: scheduleIds.map(convertToMongoObjectId) },
            }),
            ...(studentIds.length && {
                'student._id': { $in: studentIds.map(convertToMongoObjectId) },
            }),
            status: SUBMITTED,
            evaluationStatus: NOT_STARTED,
        },
        {
            _id: 1,
            approvals: 1,
            approvalStatus: 1,
            'deliveryType.deliveryTypeId': 1,
            'student._id': 1,
        },
    ).lean();

    const { components = [] } =
        (await (isExternalUser ? StudentPortfolioModel : PortfolioModel)
            .findOne(
                { _id: portfolioId },
                {
                    'components._id': 1,
                    'components.children._id': 1,
                    'components.children.hasMultipleApprover': 1,
                },
            )
            .lean()) || {};

    const hasMultipleApprover = components
        .find((c) => isIDEquals(c._id, componentId))
        ?.children?.find((c) => isIDEquals(c._id, childrenId))?.hasMultipleApprover;

    let evaluations = [];
    if (hasMultipleApprover) {
        evaluations = await EvaluationModel.find(
            {
                componentId,
                childrenId,
            },
            {
                typeOfEvaluation: 1,
                infrastructures: 1,
                groups: 1,
                'students.studentId': 1,
                'students.roles.approver': 1,
                'students.roles.users.userId': 1,
                deliveryTypeId: 1,
                externalUsers: 1,
            },
        ).lean();
    }

    const bulkUpdates = studentResponses.map((studentResponse) => {
        const approvals = studentResponse.approvals ?? [];
        const approvalIndex = approvals.findIndex(
            (approval) => approval?.email === userId || isIDEquals(approval.userId, userId),
        );
        const basePath = `approvals.${approvalIndex}`;
        const statusField = `${basePath}.status`;
        const approveCountField = `${basePath}.approveCount`;
        const rejectCountField = `${basePath}.rejectCount`;
        const updatedAtField = `${basePath}.updatedAt`;

        const existingApproval = approvals[approvalIndex];
        let approvalStatus = NOT_STARTED;
        if (hasMultipleApprover) {
            const typeOfEvaluation = evaluations[0]?.typeOfEvaluation;
            const approvalClone = deepClone(studentResponse?.approvals || []);
            const userMap = new Map();
            let users = [];
            const externalUsers = [];

            const addApproversToMap = (roles) => {
                roles?.forEach((role) => {
                    if (role.approver) {
                        role.users.forEach((user) => {
                            if (!userMap.has(String(user.userId))) {
                                userMap.set(String(user.userId), user);
                            }
                        });
                    }
                });
            };

            if (typeOfEvaluation === INFRASTRUCTURE || typeOfEvaluation === STUDENT_GROUP) {
                const evaluation = evaluations.find((i) =>
                    isIDEquals(i.deliveryTypeId, studentResponse.deliveryType.deliveryTypeId),
                );

                if (evaluation?.externalUsers?.length) {
                    const externalApprovers = evaluation.externalUsers.filter(
                        (user) => user.approver,
                    );

                    externalUsers.push(...externalApprovers);
                }

                if (typeOfEvaluation === INFRASTRUCTURE) {
                    evaluation?.infrastructures?.forEach((infra) => addApproversToMap(infra.roles));
                } else {
                    evaluation?.groups?.forEach((group) => addApproversToMap(group.roles));
                    evaluation?.students?.forEach((user) => addApproversToMap(user.roles));
                }
            } else if (typeOfEvaluation === STUDENT_LIST) {
                const matchedStudent = evaluations[0]?.students.find((s) =>
                    isIDEquals(s.studentId, studentResponse.student._id),
                );

                if (evaluations[0]?.externalUsers?.length) {
                    const externalApprovers = evaluations[0].externalUsers.filter(
                        (user) => user.approver,
                    );

                    externalUsers.push(...externalApprovers);
                }
                addApproversToMap(matchedStudent?.roles || []);
            }

            users = Array.from(userMap.values());

            const existing = approvalClone.find(
                (approval) => isIDEquals(approval.userId, userId) || approval?.email === userId,
            );
            if (existing) {
                existing.status = type === REVOKE ? NOT_STARTED : type;
            } else {
                approvalClone.push({
                    userId,
                    status: type === REVOKE ? NOT_STARTED : type,
                });
            }

            const allApproved = [...users, ...externalUsers].every((u) => {
                const approval = approvalClone.find(
                    (a) => isIDEquals(a.userId, u.userId) || a?.email === userId,
                );
                return approval?.status === APPROVED;
            });

            if (allApproved && type !== REVOKE) {
                approvalStatus = type === RESUBMIT ? RESUBMIT : APPROVED;
            } else {
                approvalStatus = type === RESUBMIT ? RESUBMIT : IN_PROGRESS;
            }
        } else {
            approvalStatus = type === REVOKE ? NOT_STARTED : type;
        }

        if (approvalIndex !== -1) {
            if (type === REVOKE) {
                let decField = null;
                if (existingApproval?.status === APPROVED) decField = approveCountField;
                else if (existingApproval?.status === RESUBMIT) decField = rejectCountField;

                return {
                    updateOne: {
                        filter: { _id: studentResponse._id },
                        update: {
                            $set: {
                                [statusField]: NOT_STARTED,
                                approvalStatus,
                                [updatedAtField]: new Date(),
                            },
                            $unset: { submissionSession: '' },
                            ...(decField ? { $inc: { [decField]: -1 } } : {}),
                        },
                    },
                };
            }
            return {
                updateOne: {
                    filter: { _id: studentResponse._id },
                    update: {
                        $set: {
                            [statusField]: type,
                            approvalStatus,
                            [updatedAtField]: new Date(),
                            ...(type === RESUBMIT && { submissionSession: session }),
                        },
                        $inc: {
                            [approveCountField]: type === APPROVED ? 1 : 0,
                            [rejectCountField]: type === RESUBMIT ? 1 : 0,
                        },
                    },
                },
            };
        }

        return {
            updateOne: {
                filter: { _id: studentResponse._id },
                update: {
                    $push: {
                        approvals: {
                            ...(isExternalUser ? { email: userId } : { userId }),
                            status: type === REVOKE ? NOT_STARTED : type,
                            approveCount: type === APPROVED ? 1 : 0,
                            rejectCount: type === RESUBMIT ? 1 : 0,
                            revokeCount: 0,
                            updatedAt: new Date(),
                        },
                    },
                    $set: { approvalStatus },
                },
            },
        };
    });

    const filteredBulkUpdates = bulkUpdates.filter(Boolean);

    if (filteredBulkUpdates.length) {
        await StudentResponseModel.bulkWrite(filteredBulkUpdates).catch((err) => {
            throw new Error('APPROVAL_STATUS_UPDATE_FAILED');
        });
    }
};

const updateStudentMarksAndRubrics = async ({
    portfolioId,
    componentId,
    childrenId,
    scheduleIds = [],
    studentIds = [],
    marks = 0,
    rubrics = [],
    globalRubrics = [],
    type,
    userId,
    isExternalUser = false,
    session = {},
}) => {
    const portfolio = await (isExternalUser ? StudentPortfolioModel : PortfolioModel)
        .findOne(
            { _id: portfolioId },
            {
                'components._id': 1,
                'components.isLogBook': 1,
                'components.children._id': 1,
                'components.children.hasMultipleEvaluator': 1,
                'components.children.rubrics': 1,
                'components.children.globalRubrics': 1,
                'components.children.formMarks': 1,
                'components.children.roles.roleId': 1,
                'components.children.roles.evaluate': 1,
            },
        )
        .lean();

    const component = portfolio?.components?.find((c) => isIDEquals(c._id, componentId)) || null;
    const child = component?.children?.find((c) => isIDEquals(c._id, childrenId)) || null;

    if (!child) throw new Error('CHILD_COMPONENT_NOT_FOUND');

    const hasApprover = child?.roles?.some((role) => role.peerReview);
    const hasMultipleEvaluatorRole = child?.roles?.filter((r) => r.evaluate?.isEnabled).length > 1;

    const globalAchievedPoints = globalRubrics.length
        ? calculateGlobalRubricPoint({ rubrics: globalRubrics })
        : {};

    const rubricMarks = rubrics.length ? calculateRubricPoint({ rubrics }) : 0;

    const studentAchievedPoints = rubrics.length
        ? calculateStudentAchievedPoints({
              rubrics,
              totalMarks: rubricMarks,
          })
        : {};

    let evaluation = null;

    if (hasMultipleEvaluatorRole || child?.hasMultipleEvaluator) {
        const projection = {
            typeOfEvaluation: 1,
            'groups.roles.roleId': 1,
            'groups.roles.evaluator': 1,
            'groups.roles.users': 1,
            'infrastructures.roles.roleId': 1,
            'infrastructures.roles.evaluator': 1,
            'infrastructures.roles.users': 1,
            externalUsers: {
                $elemMatch: { evaluator: true },
            },
            ...(studentIds.length
                ? {
                      students: {
                          $elemMatch: {
                              studentId: { $in: studentIds.map(convertToMongoObjectId) },
                          },
                      },
                  }
                : {
                      'students.roles.roleId': 1,
                      'students.roles.evaluator': 1,
                      'students.roles.users': 1,
                      'students.studentId': 1,
                  }),
        };

        evaluation = await EvaluationModel.findOne({ componentId, childrenId }, projection).lean();
    }

    let evaluateData = null;

    if (hasMultipleEvaluatorRole) {
        let matchedAssignedRole = null;
        if (isExternalUser) {
            matchedAssignedRole = evaluation?.externalUsers?.find(
                (user) => user?.evaluator && user?.email === userId,
            )?.roleId;
        } else {
            matchedAssignedRole = evaluation?.students
                ?.find((student) =>
                    student?.roles?.some(
                        (role) =>
                            role?.evaluator &&
                            role.users?.some((user) => isIDEquals(user.userId, userId)),
                    ),
                )
                ?.roles?.find(
                    (role) =>
                        role.evaluator &&
                        role.users?.some((user) => isIDEquals(user.userId, userId)),
                );
        }

        evaluateData = getAssignedRoleForEvaluator({
            hasMultipleEvaluatorRole,
            child,
            roleId: matchedAssignedRole?.roleId,
        });
    } else {
        evaluateData = getAssignedRoleForEvaluator({
            hasMultipleEvaluatorRole,
            child,
        });
    }

    if (rubrics.length) evaluateData.marks = rubricMarks;

    if (!evaluateData) {
        throw new Error('NO_EVALUATOR_ASSIGNED');
    }

    if (evaluateData?.isMarkSelected) {
        if (marks > evaluateData?.marks) {
            throw new Error('MARKS_CANNOT_BE_LESS_THAN_AWARDED_MARKS');
        }
    } else {
        const assignedRubricMarks = evaluateData?.rubrics?.length
            ? calculateRubricPoint({ rubrics: evaluateData?.rubrics })
            : 0;

        if (rubricMarks > assignedRubricMarks) {
            throw new Error('MARKS_CANNOT_BE_LESS_THAN_AWARDED_MARKS');
        }
    }

    const studentResponses = await StudentResponseModel.find(
        {
            componentId,
            childrenId,
            ...(studentIds.length && {
                'student._id': { $in: studentIds.map(convertToMongoObjectId) },
            }),
            ...(scheduleIds.length &&
                component?.isLogBook && {
                    scheduleId: { $in: scheduleIds.map(convertToMongoObjectId) },
                }),
            status: SUBMITTED,
            ...(hasApprover && { approvalStatus: EVALUATED }),
        },
        {
            evaluations: 1,
            awardedMarks: 1,
            evaluationStatus: 1,
            'student._id': 1,
            totalMarks: 1,
        },
    ).lean();

    if (!studentResponses.length) return [];

    const bulkOps = studentResponses
        .map((studentResponse) => {
            const existingEval = studentResponse.evaluations?.find(
                (e) =>
                    String(e?.email)?.toLowerCase() === String(userId)?.toLowerCase() ||
                    isIDEquals(e?.userId, userId),
            );

            const arrayFilters = [
                isExternalUser
                    ? { 'eval.email': userId }
                    : { 'eval.userId': convertToMongoObjectId(userId) },
            ];

            let evaluationStatus = NOT_STARTED;

            if (child?.hasMultipleEvaluator && type === EVALUATED) {
                const evaluationClone = deepClone(studentResponse?.evaluations || []);

                // Update or insert current evaluator
                const existing = evaluationClone.find((evaluation) =>
                    isExternalUser
                        ? String(evaluation.email).toLowerCase() === String(userId).toLowerCase()
                        : isIDEquals(evaluation.userId, userId),
                );

                if (existing) {
                    existing.status = EVALUATED;
                } else {
                    evaluationClone.push(
                        isExternalUser
                            ? { email: userId, status: EVALUATED }
                            : { userId, status: EVALUATED },
                    );
                }

                const userMap = new Map();

                const addApproversToMap = (roles) => {
                    roles?.forEach((role) => {
                        if (role.approver) {
                            role.users.forEach((user) => {
                                const key = isExternalUser
                                    ? String(user.email).toLowerCase()
                                    : String(user.userId);
                                if (!userMap.has(key)) {
                                    userMap.set(key, user);
                                }
                            });
                        }
                    });
                };

                if (
                    evaluation?.typeOfEvaluation === INFRASTRUCTURE ||
                    evaluation?.typeOfEvaluation === STUDENT_GROUP
                ) {
                    if (evaluation?.typeOfEvaluation === INFRASTRUCTURE) {
                        evaluation?.infrastructures?.forEach((infra) =>
                            addApproversToMap(infra.roles),
                        );
                    } else {
                        evaluation?.groups?.forEach((group) => addApproversToMap(group.roles));
                        evaluation?.students?.forEach((student) =>
                            addApproversToMap(student.roles),
                        );
                    }
                } else if (evaluation?.typeOfEvaluation === STUDENT_LIST) {
                    const matchedStudent = evaluation?.students.find((s) =>
                        isIDEquals(s.studentId, studentResponse.student._id),
                    );
                    addApproversToMap(matchedStudent?.roles || []);
                }

                // External users from evaluation object
                if (isExternalUser) {
                    evaluation?.externalUsers
                        ?.filter((ext) => ext.evaluator)
                        ?.forEach((ext) => {
                            const key = String(ext.email).toLowerCase();
                            if (!userMap.has(key)) {
                                userMap.set(key, ext);
                            }
                        });
                }

                const users = Array.from(userMap.values());

                const allApproved = users.every((u) => {
                    const approval = evaluationClone.find(
                        (a) =>
                            String(a?.email)?.toLowerCase() === String(u?.email)?.toLowerCase() ||
                            isIDEquals(a?.userId, u?.userId),
                    );
                    return approval?.status === EVALUATED;
                });

                evaluationStatus = allApproved ? EVALUATED : IN_PROGRESS;
            } else if (!child?.hasMultipleEvaluator && type === EVALUATED) {
                evaluationStatus = EVALUATED;
            }

            if (type === EVALUATED) {
                const newEvaluationCount = existingEval
                    ? (existingEval.evaluationCount || 0) + 1
                    : 1;

                const commonFields = {
                    awardedMarks: evaluateData?.isMarkSelected
                        ? marks
                        : studentAchievedPoints?.awardedMarks ?? 0,
                    totalMarks: evaluateData?.marks,
                    status: EVALUATED,
                    approveCount: 1,
                    rubrics,
                    evaluationCount: newEvaluationCount,
                    resubmitCount: existingEval ? existingEval.resubmitCount || 0 : 0,
                    rejectCount: existingEval ? existingEval.rejectCount || 0 : 0,
                    revokeCount: existingEval ? existingEval.revokeCount || 0 : 0,
                    updatedAt: new Date(),
                    isExternalUser,
                };

                if (globalRubrics.length) {
                    commonFields.globalRubricTotalPoints =
                        globalAchievedPoints?.totalRubricsPoint ?? 0;
                    commonFields.globalRubricAwardedPoints =
                        globalAchievedPoints?.awardedPoints ?? 0;
                    commonFields.globalRubrics = globalRubrics;
                }

                const updatedEvaluations = existingEval
                    ? studentResponse.evaluations.map((ev) =>
                          isIDEquals(ev?.userId, userId) || ev?.email === userId
                              ? { ...ev, ...commonFields }
                              : ev,
                      )
                    : [
                          ...(studentResponse.evaluations || []),
                          {
                              isExternalUser,
                              ...(isExternalUser ? { email: userId } : { userId }),
                              ...commonFields,
                          },
                      ];

                const validEvals = updatedEvaluations.filter(
                    (ev) =>
                        ev.status === EVALUATED &&
                        ev.totalMarks > 0 &&
                        isNumber(ev.awardedMarks) &&
                        isNumber(ev.totalMarks),
                );

                const avgMarksRatio = validEvals.length
                    ? validEvals.reduce((sum, ev) => sum + ev.awardedMarks / ev.totalMarks, 0) /
                      validEvals.length
                    : 0;

                const topLevelAwardedMarks = +(
                    avgMarksRatio * (studentResponse?.totalMarks || 0)
                ).toFixed(2);

                let topLevelGlobalAwardedPoints;
                let topLevelGlobalTotalPoints = studentResponse.globalRubricTotalPoints;

                const evalsWithGlobal = updatedEvaluations.filter(
                    (ev) =>
                        ev.globalRubricTotalPoints && ev.globalRubricAwardedPoints !== undefined,
                );

                if (evalsWithGlobal.length) {
                    const avgGlobalRatio =
                        evalsWithGlobal.reduce(
                            (sum, ev) =>
                                sum + ev.globalRubricAwardedPoints / ev.globalRubricTotalPoints,
                            0,
                        ) / evalsWithGlobal.length;

                    if (!topLevelGlobalTotalPoints) {
                        topLevelGlobalTotalPoints = evalsWithGlobal[0].globalRubricTotalPoints;
                    }

                    topLevelGlobalAwardedPoints = Number(
                        (avgGlobalRatio * topLevelGlobalTotalPoints).toFixed(2),
                    );
                }

                if (existingEval) {
                    return {
                        updateOne: {
                            filter: { _id: studentResponse._id },
                            update: {
                                $set: {
                                    ...Object.fromEntries(
                                        Object.entries(commonFields).map(([k, v]) => [
                                            `evaluations.$[eval].${k}`,
                                            v,
                                        ]),
                                    ),
                                    evaluationStatus,
                                    awardedMarks: topLevelAwardedMarks,
                                    ...(isNumber(topLevelGlobalAwardedPoints) && {
                                        globalRubricAwardedPoints: topLevelGlobalAwardedPoints,
                                    }),
                                    ...(isNumber(topLevelGlobalTotalPoints) && {
                                        globalRubricTotalPoints: topLevelGlobalTotalPoints,
                                    }),
                                },
                            },
                            arrayFilters,
                        },
                    };
                }

                return {
                    updateOne: {
                        filter: { _id: studentResponse._id },
                        update: {
                            $push: {
                                evaluations: {
                                    isExternalUser,
                                    ...(isExternalUser ? { email: userId } : { userId }),
                                    ...commonFields,
                                },
                            },
                            $set: {
                                awardedMarks: topLevelAwardedMarks,
                                ...(isNumber(topLevelGlobalAwardedPoints) && {
                                    globalRubricAwardedPoints: topLevelGlobalAwardedPoints,
                                }),
                                ...(isNumber(topLevelGlobalTotalPoints) && {
                                    globalRubricTotalPoints: topLevelGlobalTotalPoints,
                                }),
                                evaluationStatus,
                            },
                        },
                    },
                };
            }

            if (type === REVOKE && existingEval) {
                const resetFields = {
                    status: NOT_STARTED,
                    awardedMarks: 0,
                    totalMarks: 0,
                    approveCount: 0,
                    rubrics: [],
                    evaluationCount: 0,
                    globalRubricTotalPoints: 0,
                    globalRubricAwardedPoints: 0,
                    globalRubrics: [],
                    revokeCount: (existingEval.revokeCount || 0) + 1,
                    updatedAt: new Date(),
                    isExternalUser,
                };

                return {
                    updateOne: {
                        filter: { _id: studentResponse._id },
                        update: {
                            $set: {
                                ...Object.fromEntries(
                                    Object.entries(resetFields).map(([k, v]) => [
                                        `evaluations.$[eval].${k}`,
                                        v,
                                    ]),
                                ),
                                evaluationStatus:
                                    studentResponse.evaluations.length > 1
                                        ? IN_PROGRESS
                                        : NOT_STARTED,
                            },
                            $unset: { submissionSession: '' },
                        },
                        arrayFilters,
                    },
                };
            }

            if ((type === RESUBMIT || type === REJECTED) && existingEval) {
                return {
                    updateOne: {
                        filter: { _id: studentResponse._id },
                        update: {
                            $set: {
                                'evaluations.$[eval].status': type,
                                evaluationStatus: type,
                                'evaluations.$[eval].updatedAt': new Date(),
                                'evaluations.$[eval].isExternalUser': isExternalUser,
                                ...(type === RESUBMIT && { submissionSession: session }),
                            },
                            ...(type === REJECTED && { $unset: { submissionSession: '' } }),
                            $inc: {
                                [`evaluations.$[eval].${
                                    type === RESUBMIT ? 'resubmitCount' : 'rejectCount'
                                }`]: 1,
                            },
                        },
                        arrayFilters,
                    },
                };
            }

            if ((type === RESUBMIT || type === REJECTED) && !existingEval) {
                return {
                    updateOne: {
                        filter: { _id: studentResponse._id },
                        update: {
                            $push: {
                                evaluations: {
                                    status: type,
                                    awardedMarks: 0,
                                    totalMarks: 0,
                                    approveCount: 0,
                                    rubrics: [],
                                    evaluationCount: 0,
                                    globalRubricTotalPoints: 0,
                                    globalRubricAwardedPoints: 0,
                                    globalRubrics: [],
                                    [type === RESUBMIT ? 'resubmitCount' : 'rejectCount']: 1,
                                    isExternalUser,
                                    ...(isExternalUser ? { email: userId } : { userId }),
                                    updatedAt: new Date(),
                                },
                            },
                            ...(type === REJECTED && { $unset: { submissionSession: '' } }),
                            $set: {
                                evaluationStatus: type,
                                ...(type === RESUBMIT && { submissionSession: session }),
                            },
                        },
                    },
                };
            }

            return null;
        })
        .filter(Boolean);

    if (bulkOps.length) {
        await StudentResponseModel.bulkWrite(bulkOps);
    }

    return true;
};

const getRubricsAndMarksForEvaluator = async ({ portfolioId, componentId, childrenId, userId }) => {
    const userObjectId = convertToMongoObjectId(userId);

    const portfolio = await PortfolioModel.findOne(
        { _id: portfolioId },
        {
            'components._id': 1,
            'components.isLogBook': 1,
            'components.children._id': 1,
            'components.children.hasMultipleEvaluator': 1,
            'components.children.rubrics': 1,
            'components.children.globalRubrics': 1,
            'components.children.formMarks': 1,
            'components.children.roles.roleId': 1,
            'components.children.roles.evaluate': 1,
        },
    ).lean();

    const component = portfolio?.components?.find((c) => isIDEquals(c._id, componentId));
    const child = component?.children?.find((c) => isIDEquals(c._id, childrenId));
    if (!child) throw new Error('CHILD_COMPONENT_NOT_FOUND');

    const hasMultipleEvaluatorRole = child?.roles?.filter((r) => r.evaluate?.isEnabled).length > 1;
    let evaluateData = null;

    if (hasMultipleEvaluatorRole) {
        const evaluation = await EvaluationModel.findOne(
            { componentId, childrenId },
            {
                typeOfEvaluation: 1,
                students: {
                    $elemMatch: {
                        roles: { $elemMatch: { 'users.userId': userObjectId, evaluator: true } },
                    },
                },
                groups: {
                    $elemMatch: {
                        roles: { $elemMatch: { 'users.userId': userObjectId, evaluator: true } },
                    },
                },
                infrastructures: {
                    $elemMatch: {
                        roles: { $elemMatch: { 'users.userId': userObjectId, evaluator: true } },
                    },
                },
            },
        ).lean();

        if (!evaluation) throw new Error('NO_EVALUATOR_ASSIGNED');

        const matchedAssignedRole = getMatchedRoleForEvaluator({
            evaluation,
            type: 'evaluator',
            userId,
        });

        evaluateData = getAssignedRoleForEvaluator({
            hasMultipleEvaluatorRole,
            child,
            roleId: matchedAssignedRole?.roleId,
        });
    } else {
        evaluateData = getAssignedRoleForEvaluator({
            hasMultipleEvaluatorRole,
            child,
        });
    }

    const user = await UserModel.findOne(
        { _id: userObjectId },
        { employeeId: '$user_id', name: 1, gender: 1, _id: 0 },
    ).lean();

    return {
        user: { userId, ...user },
        evaluateData,
    };
};

const getInsightsWithRubricsAndMarks = async ({
    portfolioId,
    componentId,
    childrenId,
    scheduleId,
    studentId,
    userId,
    type = 'evaluator',
    isExternalUser = false,
}) => {
    const portfolio = await StudentPortfolioModel.findOne(
        {
            $or: [{ _id: portfolioId }, { portfolioId }],
            'student._id': convertToMongoObjectId(studentId),
        },
        {
            institutionCalendarId: 1,
            programId: 1,
            courseId: 1,
            term: 1,
            year: 1,
            level: 1,
            rotation: 1,
            rotationCount: 1,
            'components._id': 1,
            'components.isLogBook': 1,
            'components.children._id': 1,
            'components.children.roles.roleId': 1,
            'components.children.roles.evaluate': 1,
            'components.children.formId': 1,
        },
    ).lean();

    if (!portfolio) return [];

    const component = portfolio.components?.find((c) => isIDEquals(c._id, componentId));
    const child = component?.children?.find((c) => isIDEquals(c._id, childrenId));
    const hasMultipleEvaluatorRole = child?.roles?.filter((r) => r.evaluate?.isEnabled).length > 1;

    const course = await CourseModel.findOne(
        { _id: portfolio.courseId },
        { name: '$course_name', code: '$course_code' },
    ).lean();

    const form = await FormModel.findOne({ _id: child?.formId }, { title: 1, type: 1 }).lean();

    const count = {
        totalEntries: 0,
        approvedEntries: 0,
        evaluatedEntries: 0,
        resubmittedEntries: 0,
        rejectedEntries: 0,
        absent: 0,
        present: 0,
        onDuty: 0,
        leave: 0,
        permission: 0,
        pending: 0,
    };

    if (component?.isLogBook) {
        const studentResponses = await StudentResponseModel.find(
            { componentId, childrenId, 'student._id': convertToMongoObjectId(studentId) },
            { approvalStatus: 1, evaluationStatus: 1, status: 1, student: 1 },
        ).lean();

        count.totalEntries = studentResponses.length;

        const courseSchedules = await CourseScheduleModel.find(
            {
                _institution_calendar_id: portfolio.institutionCalendarId,
                _program_id: portfolio.programId,
                _course_id: portfolio.courseId,
                year_no: portfolio.year,
                term: portfolio.term,
                level_no: portfolio.level,
                ...(portfolio.rotation === 'yes' && { rotation_no: portfolio.rotation }),
                ...(portfolio.rotationCount && { rotation_count: portfolio.rotationCount }),
                'students._id': convertToMongoObjectId(studentId),
                type: REGULAR,
                isDeleted: false,
                isActive: true,
            },
            { 'students.$': 1 },
        ).lean();

        studentResponses.forEach(({ approvalStatus, evaluationStatus }) => {
            if (approvalStatus === APPROVED) count.approvedEntries++;
            if (evaluationStatus === EVALUATED) count.evaluatedEntries++;
            if (evaluationStatus === RESUBMIT || approvalStatus === RESUBMIT)
                count.resubmittedEntries++;
            if (evaluationStatus === REJECTED) count.rejectedEntries++;
        });

        courseSchedules.forEach(({ students }) => {
            const student = students.find((s) => isIDEquals(s._id, studentId));
            if (student?.status) count[student.status.toLowerCase()]++;
        });

        if (type === APPROVER) {
            return { count, student: studentResponses[0]?.student, course, form };
        }
    }

    const studentResponse = await StudentResponseModel.findOne(
        {
            componentId,
            childrenId,
            'student._id': convertToMongoObjectId(studentId),
            ...(scheduleId && { scheduleId: convertToMongoObjectId(scheduleId) }),
        },
        { evaluations: 1, student: 1 },
    ).lean();

    const evaluation = studentResponse?.evaluations?.find((e) => isIDEquals(e.userId, userId));

    if (
        (evaluation?.totalMarks || evaluation?.rubrics?.length) &&
        evaluation?.status === EVALUATED
    ) {
        return { evaluation, ...(component?.isLogBook && { count }) };
    }

    let user = null;
    if (!isExternalUser) {
        user = await UserModel.findOne(
            { _id: convertToMongoObjectId(userId) },
            { name: 1, gender: 1, employeeId: '$user_id', _id: 0 },
        ).lean();
    }

    let evaluationData;
    if (hasMultipleEvaluatorRole) {
        const evaluation = await EvaluationModel.findOne(
            { componentId, childrenId },
            {
                typeOfEvaluation: 1,
                ...(isExternalUser && {
                    externalEvaluators: {
                        $elemMatch: {
                            email: userId,
                            evaluator: true,
                        },
                    },
                }),
                ...(!isExternalUser && {
                    students: {
                        $elemMatch: {
                            roles: {
                                $elemMatch: {
                                    'users.userId': convertToMongoObjectId(userId),
                                    evaluator: true,
                                },
                            },
                        },
                    },
                    groups: {
                        $elemMatch: {
                            roles: {
                                $elemMatch: {
                                    'users.userId': convertToMongoObjectId(userId),
                                    evaluator: true,
                                },
                            },
                        },
                    },
                    infrastructures: {
                        $elemMatch: {
                            roles: {
                                $elemMatch: {
                                    'users.userId': convertToMongoObjectId(userId),
                                    evaluator: true,
                                },
                            },
                        },
                    },
                }),
            },
        ).lean();
        if (isExternalUser) {
            const externalEvaluator = evaluation?.externalEvaluators?.find((e) =>
                isIDEquals(e.email, userId),
            );
            user = externalEvaluator;
            evaluationData = getAssignedRoleForEvaluator({
                hasMultipleEvaluatorRole,
                child,
                roleId: externalEvaluator?.roleId,
            });
        } else {
            const matchedAssignedRole = getMatchedRoleForEvaluator({
                evaluation,
                type: EVALUATOR,
                userId,
            });
            evaluationData = getAssignedRoleForEvaluator({
                hasMultipleEvaluatorRole,
                child,
                roleId: matchedAssignedRole?.roleId,
            });
        }
    } else {
        evaluationData = getAssignedRoleForEvaluator({ hasMultipleEvaluatorRole, child });
    }

    return {
        ...(component?.isLogBook && { count }),
        evaluation: {
            globalRubrics: evaluationData?.globalRubrics || [],
            rubrics: evaluationData?.rubrics,
            totalMarks: evaluationData?.marks || 0,
            awardedMarks: 0,
        },
        student: studentResponse?.student,
        course,
        form,
        user,
    };
};

module.exports = {
    getEvaluatorAndApproverDashboard,
    getListOfComponentsForDashboard,
    getApproverOrEvaluatorComponentsWithCount,
    getComponentChildren,
    getApproverOrEvaluatorOverallCount,
    getScheduleForApproverOrEvaluator,
    getStudentsForSchedule,
    updateApproveOrRejectStatus,
    updateStudentMarksAndRubrics,
    getRubricsAndMarksForEvaluator,
    getInsightsWithRubricsAndMarks,
};
