const momentTimezone = require('moment-timezone');

const { TIME_ZONE } = require('../config');

const formatWithTimeZone = (timestamp) => {
    return momentTimezone.tz(timestamp, TIME_ZONE);
};

const getFullDateIntoString = (dateString = new Date().toDateString()) => {
    const date = new Date(dateString);
    return formatWithTimeZone(date).format('YYYY-MM-DD');
};

module.exports = {
    formatWithTimeZone,
    getFullDateIntoString,
};
