const { convertToMongoObjectId } = require('../utility/common');
const {
    PUBLISHED,
    SCHEDULE_TYPES: { REGULAR },
    COMPLETED,
    EVENT_WHOM: { STAFF, STUDENT },
    PRIMARY,
    REJECTED,
    APPROVED,
    TIME_GROUP_BOOKING_TYPE: { ONSITE },
} = require('../utility/constants');
const { clone } = require('../utility/common');
const { nameFormatter } = require('../utility/common_functions');
const institutionCalendarEventSchema = require('../models/calendar_event');
const programCalendarSchema = require('../models/program_calendar');
const courseScheduleSchema = require('../models/course_schedule');
const roleAssignSchema = require('../models/role_assign');
const courseSchema = require('../models/digi_course');
const studentGroupSchema = require('../models/student_group');
const sessionOrderSchema = require('../models/digi_session_order');
const institutionSchema = require('../models/institution');
const userSchema = require('../models/user');
const infraStructureManagementSchema = require('../models/infrastructure_management');
const courseScheduleSettingSchema = require('../models/course_schedule_setting');
const courseScheduleDeliverySchema = require('../models/course_schedule_delivery_settings');
const lmsReviewSchema = require('../models/lms_review');
const lmsStudentSchemas = require('../lmsStudent/lmsStudent.model');

const getInstitutionCalenderEvents = async ({
    institutionCalendarId,
    eventStartTime,
    eventEndTime,
}) => {
    try {
        return await institutionCalendarEventSchema
            .find(
                {
                    _calendar_id: convertToMongoObjectId(institutionCalendarId),
                    $or: [
                        {
                            start_time: {
                                $gte: new Date(eventStartTime),
                                $lte: new Date(eventEndTime),
                            },
                        },
                        {
                            end_time: {
                                $gte: new Date(eventStartTime),
                                $lte: new Date(eventEndTime),
                            },
                        },
                        {
                            // Events that span across the entire occurrence range
                            start_time: { $lte: new Date(eventStartTime) },
                            end_time: { $gte: new Date(eventEndTime) },
                        },
                    ],
                    // isActive: true,
                    isDeleted: false,
                },
                {
                    start_time: 1,
                    end_time: 1,
                    'event_name.first_language': 1,
                    'event_name.second_language': 1,
                    event_type: 1,
                },
            )
            .lean();
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getProgramCalenderEvents = async ({
    institutionCalendarId,
    eventStartTime,
    eventEndTime,
    programId,
    courseId,
    term,
    year,
    level,
}) => {
    try {
        return await programCalendarSchema
            .find(
                {
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    _program_id: convertToMongoObjectId(programId),
                    'level.term': term,
                    'level.year': year,
                    'level.level_no': level,
                    $or: [
                        {
                            'level.events': {
                                $elemMatch: {
                                    event_date: { $gte: eventStartTime },
                                    end_date: { $lte: eventEndTime },
                                },
                            },
                        },
                        {
                            'level.course': {
                                $elemMatch: {
                                    ...(courseId && {
                                        _course_id: convertToMongoObjectId(courseId),
                                    }),
                                    start_date: { $gte: eventStartTime },
                                    end_date: { $lte: eventEndTime },
                                },
                            },
                        },
                        {
                            'level.course.courses_events': {
                                $elemMatch: {
                                    start_date: { $gte: eventStartTime },
                                    end_date: { $lte: eventEndTime },
                                },
                            },
                        },
                    ],
                },
                {
                    'level.events._event_id': 1,
                    'level.events.event_name': 1,
                    'level.events.start_time': 1,
                    'level.events.event_date': 1,
                    'level.events.end_date': 1,
                    'level.events.end_time': 1,
                    'level.course.start_date': 1,
                    'level.course.end_date': 1,
                    'level.course._course_id': 1,
                    'level.course.courses_events._event_id': 1,
                    'level.course.courses_events.event_name': 1,
                    'level.course.courses_events.start_time': 1,
                    'level.course.courses_events.end_time': 1,
                },
            )
            .lean();
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getExtractUniqueEvents = async ({
    programCalenderData,
    institutionCalendarData,
    courseId,
    eventStartTime,
    eventEndTime,
}) => {
    try {
        const uniqueEventIds = new Set();
        const eventList = [];

        const startTime = new Date(eventStartTime);
        const endTime = new Date(eventEndTime);

        const isEventInRange = (eventStart, eventEnd) => {
            const eventStartDate = new Date(eventStart);
            const eventEndDate = new Date(eventEnd);
            return eventStartDate >= startTime || eventEndDate <= endTime;
        };

        const addUniqueEvent = (eventId, eventData) => {
            if (!uniqueEventIds.has(eventId)) {
                uniqueEventIds.add(eventId);
                eventList.push(eventData);
            }
        };

        if (programCalenderData?.length) {
            programCalenderData.forEach((programCalenderElement) => {
                if (!programCalenderElement?.level?.length) return;

                programCalenderElement.level.forEach((levelElement) => {
                    if (levelElement?.events?.length) {
                        levelElement.events.forEach((eventElement) => {
                            if (isEventInRange(eventElement.event_date, eventElement.end_date)) {
                                addUniqueEvent(eventElement._event_id, {
                                    eventId: eventElement._event_id,
                                    eventStartTime: eventElement.start_time,
                                    eventEndTime: eventElement.end_time,
                                    eventName: eventElement.event_name,
                                    eventType: eventElement.event_type,
                                });
                            }
                        });
                    }

                    if (levelElement?.course?.length) {
                        levelElement.course.forEach((courseElement) => {
                            if (courseElement?.courses_events?.length) {
                                courseElement.courses_events.forEach((courseEventElement) => {
                                    if (
                                        isEventInRange(
                                            courseEventElement.start_time,
                                            courseEventElement.end_time,
                                        ) &&
                                        (!courseId ||
                                            String(courseId) === String(courseElement?._course_id))
                                    ) {
                                        addUniqueEvent(courseEventElement._event_id, {
                                            eventId: courseEventElement._event_id,
                                            eventStartTime: courseEventElement.start_time,
                                            eventEndTime: courseEventElement.end_time,
                                            eventName: courseEventElement.event_name,
                                            eventType: courseEventElement.event_type,
                                        });
                                    }
                                });
                            }
                        });
                    }
                });
            });
        }

        if (institutionCalendarData?.length) {
            institutionCalendarData.forEach((institutionCalenderElement) => {
                if (
                    isEventInRange(
                        institutionCalenderElement.start_time,
                        institutionCalenderElement.end_time,
                    )
                ) {
                    addUniqueEvent(institutionCalenderElement._id, {
                        eventId: institutionCalenderElement._id,
                        eventStartTime: institutionCalenderElement.start_time,
                        eventEndTime: institutionCalenderElement.end_time,
                        eventName: `${
                            institutionCalenderElement?.event_name?.first_language || ''
                        }${institutionCalenderElement?.event_name?.second_language || ''}`,
                        eventType: institutionCalenderElement.event_type,
                    });
                }
            });
        }

        return eventList;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getScheduleList = async ({
    _institution_id,
    institutionCalendarId,
    eventStartTime,
    eventEndTime,
    programId,
    courseId,
    term,
    year,
    level,
}) => {
    try {
        return await courseScheduleSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    _program_id: convertToMongoObjectId(programId),
                    ...(courseId && { _course_id: convertToMongoObjectId(courseId) }),
                    term,
                    year_no: year,
                    level_no: level,
                    schedule_date: {
                        $gte: eventStartTime,
                        $lte: eventEndTime,
                    },
                    isActive: true,
                    isDeleted: false,
                },
                {
                    program_name: 1,
                    _program_id: 1,
                    course_name: 1,
                    _course_id: 1,
                    course_code: 1,
                    term: 1,
                    year_no: 1,
                    level_no: 1,
                    'session.session_topic': 1,
                    'session.session_type': 1,
                    'session.delivery_symbol': 1,
                    'session.delivery_no': 1,
                    'subjects.subject_name': 1,
                    'subjects._subject_id': 1,
                    'students._id': 1,
                    'staffs._staff_id': 1,
                    'staffs.staff_name': 1,
                    'student_groups.group_name': 1,
                    'student_groups.session_group.group_name': 1,
                    mode: 1,
                    status: 1,
                    _infra_id: 1,
                    infra_name: 1,
                    scheduleStartDateAndTime: 1,
                    scheduleEndDateAndTime: 1,
                    importedBy: 1,
                },
            )
            .populate({ path: 'students._id', select: { gender: 1 } })
            .populate({ path: 'importedBy', select: { name: 1 } })
            .lean();
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getCourseTopic = async ({
    _institution_id,
    programId,
    courseId,
    deliveryType,
    deliverySymbol,
}) => {
    try {
        const sessionOrderData = await sessionOrderSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _program_id: convertToMongoObjectId(programId),
                    _course_id: convertToMongoObjectId(courseId),
                    'session_flow_data.delivery_type': deliveryType,
                    'session_flow_data.delivery_symbol': deliverySymbol,
                    isActive: true,
                    isDeleted: false,
                },
                {
                    'session_flow_data.delivery_type': 1,
                    'session_flow_data.delivery_symbol': 1,
                    'session_flow_data.delivery_topic': 1,
                    'session_flow_data.delivery_no': 1,
                    'session_flow_data.duration': 1,
                    'session_flow_data.subjects.subject_name': 1,
                    'session_flow_data.subjects._subject_id': 1,
                    'session_flow_data._id': 1,
                },
            )
            .lean();
        const filteredSessionsTopics = sessionOrderData?.session_flow_data
            ? sessionOrderData.session_flow_data
                  .filter(
                      (sessionFlowElement) =>
                          sessionFlowElement.delivery_type === deliveryType &&
                          sessionFlowElement.delivery_symbol === deliverySymbol,
                  )
                  .map((sessionFlowElement) => ({
                      delivery_topic: sessionFlowElement.delivery_topic,
                      deliverySymbol: sessionFlowElement.delivery_symbol,
                      deliveryNumber: sessionFlowElement.delivery_no,
                      subjects: sessionFlowElement.subjects,
                      duration: sessionFlowElement.duration,
                      sessionId: sessionFlowElement._id,
                  }))
            : [];
        return filteredSessionsTopics;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getInstitutionData = async ({ _institution_id }) => {
    try {
        return await institutionSchema
            .findOne(
                {
                    _id: convertToMongoObjectId(_institution_id),
                    isActive: true,
                    isDeleted: false,
                },
                { _id: 1 },
            )
            .lean();
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getStaffData = async () => {
    try {
        return await userSchema
            .find(
                {
                    status: COMPLETED,
                    user_type: STAFF,
                    'academic_allocation.allocation_type': PRIMARY,
                    isActive: true,
                    isDeleted: false,
                },
                { email: 1, name: 1, academic_allocation: 1, employment: 1 },
            )
            .lean();
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getGroupingData = async ({
    _institution_id,
    institutionCalendarId,
    programId,
    year,
    level,
    term,
    courseId,
}) => {
    try {
        console.log({
            _institution_id,
            institutionCalendarId,
            programId,
            year,
            level,
            term,
            courseId,
        });
        return await studentGroupSchema
            .findOne({
                _institution_id: convertToMongoObjectId(_institution_id),
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                'master._program_id': convertToMongoObjectId(programId),
                'master.year': year,
                'groups.level': level,
                'groups.term': term,
                'groups.courses._course_id': convertToMongoObjectId(courseId),
            })
            .lean();
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getInfraStructureFlow = async ({ _institution_id }) => {
    try {
        return await infraStructureManagementSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isDeleted: false,
                    isActive: true,
                },
                {
                    zone: 1,
                    _id: 1,
                    _building_id: 1,
                    building_name: 1,
                    floor_no: 1,
                    room_no: 1,
                    name: 1,
                    delivery_type: 1,
                    timing: 1,
                    subject: 1,
                    outsideCampus: 1,
                },
            )
            .populate({
                path: 'timing._time_id',
                select: {
                    _id: 1,
                    type: 1,
                    start_time: 1,
                    end_time: 1,
                    gender: 1,
                    outsideCampus: 1,
                },
            })
            .lean();
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getCourseSessionOrder = async ({ programId, courseId }) => {
    try {
        return await sessionOrderSchema
            .findOne(
                {
                    _program_id: convertToMongoObjectId(programId),
                    _course_id: convertToMongoObjectId(courseId),
                    isActive: true,
                    isDeleted: false,
                },
                {
                    'session_flow_data._session_id': 1,
                    'session_flow_data.s_no': 1,
                    'session_flow_data._id': 1,
                    'session_flow_data.delivery_type': 1,
                    'session_flow_data._delivery_id': 1,
                    'session_flow_data.delivery_symbol': 1,
                    'session_flow_data.delivery_no': 1,
                    'session_flow_data.delivery_topic': 1,
                    'session_flow_data.subjects': 1,
                    'session_flow_data.duration': 1,
                    'session_flow_data.sessionDocumentDetails': 1,
                    'session_flow_data.additional_session_flow_data': 1,
                },
            )
            .lean();
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getCourseScheduleData = async ({
    _institution_id,
    // institutionCalendarId,
    schedule_date,
    courseScheduleId,
}) => {
    try {
        return await courseScheduleSchema
            .find({
                ...(courseScheduleId && convertToMongoObjectId(courseScheduleId)),
                _institution_id: convertToMongoObjectId(_institution_id),
                schedule_date,
                isActive: true,
                isDeleted: false,
            })
            .lean();
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getSessionScheduleData = async ({
    _institution_id,
    institutionCalendarId,
    sessionId,
    level,
    term,
}) => {
    try {
        return await courseScheduleSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    'session._session_id': convertToMongoObjectId(sessionId),
                    type: REGULAR,
                    isDeleted: false,
                    level_no: level,
                    term,
                },
                {
                    'student_groups.group_id': 1,
                    'student_groups.session_group.session_group_id': 1,
                },
            )
            .lean();
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getRemoteExtraSchedule = async ({ _institution_id }) => {
    try {
        return await courseScheduleSettingSchema
            .findOne({
                _institution_id: convertToMongoObjectId(_institution_id),
                isActive: true,
                isDeleted: false,
            })
            .lean();
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getCourseScheduleDeliverySettingData = async ({
    _institution_id,
    institutionCalendarId,
    programId,
    courseId,
    sessionFlowSessionId,
    sessionFlowDeliveryId,
    level,
    term,
}) => {
    try {
        return await courseScheduleDeliverySchema
            .find(
                {
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    _program_id: convertToMongoObjectId(programId),
                    _course_id: convertToMongoObjectId(courseId),
                    _session_type_id: convertToMongoObjectId(sessionFlowSessionId),
                    _delivery_id: convertToMongoObjectId(sessionFlowDeliveryId),
                    level_no: level,
                    term,
                    isDeleted: false,
                },
                {
                    _id: 1,
                    _course_id: 1,
                    _session_type_id: 1,
                    _delivery_id: 1,
                    topic: 1,
                    term: 1,
                    mode: 1,
                    _infra_id: 1,
                    infra_name: 1,
                    subjects: 1,
                    session: 1,
                    staffs: 1,
                    attendanceTakingStaff: 1,
                },
            )
            .lean();
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const dateFormatter = (year, month, date, hours, minute, format) => {
    const dateFormat = new Date(
        year + '-' + month + '-' + date + ' ' + hours + ':' + minute + ' ' + format,
    );
    return dateFormat;
};

const getStudentGroupStudentId = async ({
    studentGroupId,
    courseId,
    level,
    term,
    studentGroups,
}) => {
    try {
        const studentGroupData = await studentGroupSchema
            .findOne({
                _id: convertToMongoObjectId(studentGroupId),
                'groups.level': level,
                'groups.term': term,
                'groups.courses._course_id': convertToMongoObjectId(courseId),
            })
            .lean();

        const matchingStudentGroups =
            studentGroupData?.groups.find(
                (studentGroupElement) =>
                    studentGroupElement.level === level && studentGroupElement.term === term,
            ) || [];
        let studentId = [];

        const courseData =
            matchingStudentGroups?.courses?.find(
                (courseElement) =>
                    courseElement && courseElement._course_id.toString() === courseId.toString(),
            )?.setting || [];

        studentGroups?.forEach((studentGroupElement) => {
            studentGroupElement.sessionGroup?.forEach((sessionGroupElement) => {
                courseData?.forEach((courseSettingElement) => {
                    courseSettingElement.session_setting?.forEach((sessionSettingElement) => {
                        const matchedGroup = sessionSettingElement.groups?.find(
                            (groupElement) =>
                                String(groupElement?._id) ===
                                String(sessionGroupElement.sessionGroupId),
                        );
                        if (matchedGroup && Array.isArray(matchedGroup._student_ids)) {
                            studentId.push(...matchedGroup._student_ids);
                        }
                    });
                });
            });
        });
        studentId = [...new Set(studentId.map((studentIdElement) => String(studentIdElement)))];
        const studentIdData = [];
        studentId.forEach((studentIdElement) => {
            const matchingStudent = matchingStudentGroups.students?.find(
                (studentElement) =>
                    String(studentElement?._student_id) === String(studentIdElement),
            );
            if (matchingStudent) {
                studentIdData.push({
                    _id: convertToMongoObjectId(studentIdElement),
                    name: matchingStudent.name,
                });
            }
        });
        return studentIdData;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getLevelStudentList = async ({ _institution_id, institutionCalendarId, lmsCheckUserIds }) => {
    try {
        return await lmsReviewSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    _user_id: { $in: lmsCheckUserIds },
                    status: { $ne: REJECTED },
                    isActive: true,
                    isDeleted: false,
                },
                {
                    from: 1,
                    to: 1,
                    _user_id: 1,
                    type: 1,
                    user_type: 1,
                },
            )
            .lean();
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const lmsStudentApprovedList = async ({ lmsStudentCheckUserIds, leaveCheck }) => {
    try {
        const studentLMS = await lmsStudentSchemas
            .find(
                {
                    studentId: { $in: lmsStudentCheckUserIds },
                    approvalStatus: APPROVED,
                    isActive: true,
                    isDeleted: false,
                },
                {
                    studentId: 1,
                    classificationType: 1,
                    'dateAndTimeRange.startDate': 1,
                    'dateAndTimeRange.endDate': 1,
                },
            )
            .lean();
        const studentLMSConvertedData = [
            ...leaveCheck,
            ...(studentLMS?.length
                ? studentLMS.map(({ dateAndTimeRange, studentId, classificationType }) => ({
                      from: dateAndTimeRange.startDate,
                      to: dateAndTimeRange.endDate,
                      user_type: STUDENT,
                      _user_id: studentId,
                      type: classificationType,
                  }))
                : []),
        ];
        return studentLMSConvertedData;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const lmsDataValidation = async ({
    schedulingDate,
    start,
    end,
    lmsData,
    studentDatas,
    staffDatas,
}) => {
    try {
        const scheduleStartDate = dateFormatter(
            schedulingDate.getFullYear(),
            schedulingDate.getMonth() + 1,
            schedulingDate.getDate(),
            start.hour,
            start.minute,
            start.format,
        );
        const scheduleEndDate = dateFormatter(
            schedulingDate.getFullYear(),
            schedulingDate.getMonth() + 1,
            schedulingDate.getDate(),
            end.hour,
            end.minute,
            end.format,
        );
        scheduleStartDate.setMinutes(scheduleStartDate.getMinutes() + 1);
        scheduleEndDate.setMinutes(scheduleEndDate.getMinutes() - 1);

        lmsData?.forEach((lmsElement) => {
            const lmsFrom = dateTimeLocalFormatter(lmsElement.from);
            const lmsTo = dateTimeLocalFormatter(lmsElement.to);

            const isWithinRange =
                (scheduleStartDate <= lmsFrom && scheduleEndDate >= lmsTo) ||
                (scheduleStartDate <= lmsTo && scheduleEndDate >= lmsFrom);

            if (isWithinRange) {
                if (lmsElement.user_type === STAFF) {
                    const staffIndex = staffDatas.findIndex(
                        (staffDataElement) =>
                            staffDataElement._staff_id.toString() ===
                            lmsElement._user_id.toString(),
                    );
                    if (staffIndex !== -1) staffDatas[staffIndex].status = lmsElement.type;
                } else {
                    const studentIndex = studentDatas.findIndex(
                        (studentDataElement) =>
                            studentDataElement._id.toString() === lmsElement._user_id.toString(),
                    );
                    if (studentIndex !== -1) studentDatas[studentIndex].status = lmsElement.type;
                }
            }
        });

        return { studentDatas, staffDatas };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getSessionOrderFlow = async ({ _institution_id, courseId, programId }) => {
    try {
        const sessionFlowOrderData = await sessionOrderSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _course_id: convertToMongoObjectId(courseId),
                    _program_id: convertToMongoObjectId(programId),
                    isActive: true,
                    isDeleted: false,
                },
                {
                    'session_flow_data.delivery_symbol': 1,
                    'session_flow_data.delivery_topic': 1,
                    'session_flow_data._delivery_id': 1,
                    'session_flow_data.delivery_type': 1,
                },
            )
            .lean();
        const deliverySymbols = [];
        sessionFlowOrderData?.session_flow_data.forEach((sessionFlowElement) => {
            const symbol = sessionFlowElement.delivery_symbol;
            const existing = deliverySymbols.find(
                (deliverySymbolElement) => deliverySymbolElement.symbol === symbol,
            );
            if (existing) {
                existing.topics += 1;
            } else {
                deliverySymbols.push({
                    symbol,
                    types: sessionFlowElement.delivery_type,
                    deliveryId: sessionFlowElement._delivery_id,
                    topics: 1,
                });
            }
        });
        return deliverySymbols;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getMonthScheduleCount = async ({
    institutionCalendarId,
    programId,
    courseId,
    term,
    year,
    level,
    eventStartTime,
    eventEndTime,
}) => {
    try {
        const courseScheduleDate = await courseScheduleSchema
            .find(
                {
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    _program_id: convertToMongoObjectId(programId),
                    ...(courseId && { _course_id: convertToMongoObjectId(courseId) }),
                    term,
                    year_no: year,
                    level_no: level,
                    schedule_date: {
                        $gte: eventStartTime,
                        $lte: eventEndTime,
                    },
                },
                {
                    schedule_date: 1,
                },
            )
            .sort({ schedule_date: 1 })
            .lean();

        const scheduleCountByDate = {};
        courseScheduleDate?.forEach((scheduleElement) => {
            const dateKey = new Date(scheduleElement.schedule_date).toISOString().split('T')[0];
            if (scheduleCountByDate[dateKey]) {
                scheduleCountByDate[dateKey]++;
            } else {
                scheduleCountByDate[dateKey] = 1;
            }
        });
        return scheduleCountByDate;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = {
    getInstitutionCalenderEvents,
    getProgramCalenderEvents,
    getScheduleList,
    getExtractUniqueEvents,
    getCourseTopic,
    getInstitutionData,
    getStaffData,
    getGroupingData,
    getInfraStructureFlow,
    getCourseSessionOrder,
    getCourseScheduleData,
    getSessionScheduleData,
    getRemoteExtraSchedule,
    getCourseScheduleDeliverySettingData,
    dateFormatter,
    getStudentGroupStudentId,
    getLevelStudentList,
    lmsDataValidation,
    lmsStudentApprovedList,
    getSessionOrderFlow,
    getMonthScheduleCount,
};
