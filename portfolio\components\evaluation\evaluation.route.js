const router = require('express').Router();
const { validate } = require('../../common/middlewares/validation');
const catchAsync = require('../../../lib/utility/catch-async');
const EvaluationController = require('./evaluation.controller');
const {
    assignEvaluatorSchema,
    getInfrastructuresForAssignEvaluatorSchema,
    switchEvaluationTypeSchema,
    getEvaluatorsSchema,
    getStudentGroupsForAssignEvaluatorSchema,
    getDeliveryTypeForAssignEvaluatorSchema,
    updateStudentsForPrepareAndPublishSchema,
    getChildrenForPrepareAndPublishSchema,
    validateChildIdSchema,
} = require('./evaluation.validation');

router.put(
    '/assign-evaluator',
    validate(assignEvaluatorSchema),
    catchAsync(EvaluationController.assignEvaluator),
);

router.get(
    '/infrastructure',
    validate(getInfrastructuresForAssignEvaluatorSchema),
    catchAsync(EvaluationController.getInfrastructuresForAssignEvaluator),
);

router.put(
    '/switch-evaluation-type',
    validate(switchEvaluationTypeSchema),
    catchAsync(EvaluationController.switchEvaluationType),
);

router.get(
    '/evaluators',
    validate(getEvaluatorsSchema),
    catchAsync(EvaluationController.getEvaluators),
);

router.get(
    '/student-group',
    validate(getStudentGroupsForAssignEvaluatorSchema),
    catchAsync(EvaluationController.getStudentGroupsForAssignEvaluator),
);

router.get(
    '/delivery-type',
    validate(getDeliveryTypeForAssignEvaluatorSchema),
    catchAsync(EvaluationController.getDeliveryTypeForAssignEvaluator),
);

router.put(
    '/prepare-publish',
    validate(updateStudentsForPrepareAndPublishSchema),
    catchAsync(EvaluationController.updateStudentsForPrepareAndPublish),
);

router.get(
    '/prepare-publish',
    validate(getChildrenForPrepareAndPublishSchema),
    catchAsync(EvaluationController.getChildrenForPrepareAndPublish),
);

router.get(
    '/prepare-publish/student',
    validate(getInfrastructuresForAssignEvaluatorSchema),
    catchAsync(EvaluationController.getStudentsForPrepareAndPublish),
);

router.get(
    '/role',
    validate(validateChildIdSchema),
    catchAsync(EvaluationController.getRolesFromChild),
);

router.get(
    '/evaluation-type',
    validate(validateChildIdSchema),
    catchAsync(EvaluationController.getEvaluationType),
);

router.delete(
    '/',
    validate(validateChildIdSchema),
    catchAsync(EvaluationController.deleteEvaluation),
);

router.get(
    '/prepare-publish/form',
    validate(validateChildIdSchema),
    catchAsync(EvaluationController.getAccessibleFormSectionsByChild),
);

module.exports = router;
