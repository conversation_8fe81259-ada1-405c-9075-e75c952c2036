const {
    convertToMongoObjectId,
    isIDEquals,
    convertToString,
} = require('../../common/utils/common.util');
const {
    SUBMITTED,
    NOT_STARTED,
    APPROVED,
    RESUBMIT,
    READY_TO_APPROVE,
    CONTINUE_APPROVAL,
    READY_TO_EVALUATE,
    CONTINUE_EVALUATE,
    ALL_EVALUATE,
    PARTIALLY_APPROVED,
    ALL_APPROVED,
    PARTIALLY_EVALUATE,
    EVALUATED,
    REJECTED,
    APPROVAL_PENDING,
} = require('../../common/utils/enums');
const {
    APPROVER,
    EVALUATOR,
    STUDENT_LIST,
    STUDENT_GROUP,
    INFRASTRUCTURE,
    APPROVAL_STATUS_KEY,
    EVALUATOR_KEY,
    YES,
    PRESENT,
} = require('../../common/utils/constants');
const { filterByConditions } = require('../../common/utils/array.util');

const buildStudentResponseQuery = ({ portfolioIds, evaluations = [], isSubmitted = false }) => {
    const childrenIds = evaluations.map((e) => convertToMongoObjectId(e.childrenId));

    const deliveryTypeIds = evaluations
        .map((e) => e.deliveryTypeId)
        .filter(Boolean)
        .map((id) => convertToMongoObjectId(id));

    const studentIds = evaluations
        .flatMap((e) => e.students || [])
        .map((s) => convertToMongoObjectId(s.studentId));

    const orConditions = [];
    if (deliveryTypeIds.length > 0) {
        orConditions.push({ 'deliveryType.deliveryTypeId': { $in: deliveryTypeIds } });
    }
    if (studentIds.length > 0) {
        orConditions.push({ 'student._id': { $in: studentIds } });
    }

    const query = {
        parentPortfolioId: { $in: portfolioIds },
        childrenId: { $in: childrenIds },
        ...(isSubmitted && { status: SUBMITTED }),
    };

    if (orConditions.length > 0) {
        query.$or = orConditions;
    }

    return query;
};

const getStudentStatusForApprover = ({
    approvals = [],
    hasMultipleApprover = false,
    hasMultipleEvaluator = false,
    userId,
    isApprover,
    evaluations = [],
    approvalStatus = NOT_STARTED,
}) => {
    const init = {
        approvalStatus: NOT_STARTED,
        canApproveOrEvaluate: false,
        evaluationStatus: NOT_STARTED,
        approvedOrEvaluatedBy: null,
    };

    // Approver
    if (isApprover) {
        if (hasMultipleApprover) {
            const approval = approvals.find((a) => isIDEquals(a.userId, userId));

            init.approvalStatus = approval ? approval.status : NOT_STARTED;
            init.canApproveOrEvaluate = true;
        } else {
            const hasApproved = approvals.find(
                (a) =>
                    (a.status === APPROVED || a.status === RESUBMIT) &&
                    !isIDEquals(a.userId, userId),
            );

            if (hasApproved) {
                init.approvalStatus = hasApproved.status;
                init.canApproveOrEvaluate = false;
                init.approvedOrEvaluatedBy = hasApproved.userId;
            }

            const currentUserApproval = approvals.find((a) => isIDEquals(a.userId, userId));
            init.approvalStatus = currentUserApproval ? currentUserApproval.status : NOT_STARTED;
            init.canApproveOrEvaluate = true;
        }
    }

    // Evaluator
    if (!isApprover) {
        init.approvalStatus = approvalStatus;

        if (hasMultipleEvaluator) {
            const evaluation = evaluations.find((a) => isIDEquals(a.userId, userId));

            init.evaluationStatus = evaluation ? evaluation.status : NOT_STARTED;
            init.canApproveOrEvaluate = true;
        } else {
            const hasEvaluated = evaluations.find(
                (a) =>
                    (a.status === EVALUATED || a.status === RESUBMIT || a.status === REJECTED) &&
                    !isIDEquals(a.userId, userId),
            );

            if (hasEvaluated) {
                init.evaluationStatus = hasEvaluated.status;
                init.canApproveOrEvaluate = false;
                init.approvedOrEvaluatedBy = hasEvaluated.userId;
            }

            const currentUserEvaluation = evaluations.find((a) => isIDEquals(a.userId, userId));
            init.evaluationStatus = currentUserEvaluation
                ? currentUserEvaluation.status
                : NOT_STARTED;
            init.canApproveOrEvaluate = true;
        }
    }

    return init;
};

const getPortfolioStatus = ({
    type,
    receivedSubmissionsCount,
    approvedEntries = 0,
    approvalPending = 0,
    totalSubmissionsExpected,
    evaluatedEntries = 0,
    evaluatedPending = 0,
    hasApprover = false,
}) => {
    let status = null;
    let canApproveOrEvaluate = false;

    if (type === APPROVER) {
        if (receivedSubmissionsCount === 0) {
            status = READY_TO_APPROVE;
        }

        if (receivedSubmissionsCount > 0 && approvedEntries === 0) {
            status = READY_TO_APPROVE;
            canApproveOrEvaluate = true;
        }

        if (receivedSubmissionsCount > 0 && approvedEntries > 0) {
            status = CONTINUE_APPROVAL;
            canApproveOrEvaluate = true;
        }

        if (
            receivedSubmissionsCount > 0 &&
            approvedEntries > 0 &&
            approvalPending === 0 &&
            receivedSubmissionsCount !== totalSubmissionsExpected
        ) {
            status = PARTIALLY_APPROVED;
            canApproveOrEvaluate = true;
        }

        if (receivedSubmissionsCount === totalSubmissionsExpected && approvalPending === 0) {
            status = ALL_APPROVED;
            canApproveOrEvaluate = true;
        }
    }

    if (type === EVALUATOR) {
        if (receivedSubmissionsCount === 0) {
            status = READY_TO_EVALUATE;
        }

        if (receivedSubmissionsCount > 0 && evaluatedEntries === 0) {
            status = READY_TO_EVALUATE;
            canApproveOrEvaluate = !hasApprover || approvedEntries > 0; // if has approver and approved entries are greater than 0, then can evaluate
        }

        if (receivedSubmissionsCount > 0 && evaluatedEntries > 0) {
            status = CONTINUE_EVALUATE;
            canApproveOrEvaluate = true;
        }

        if (
            receivedSubmissionsCount > 0 &&
            evaluatedEntries > 0 &&
            evaluatedPending === 0 &&
            approvalPending > 0
        ) {
            status = APPROVAL_PENDING;
            canApproveOrEvaluate = true;
        }

        if (
            receivedSubmissionsCount > 0 &&
            evaluatedEntries > 0 &&
            evaluatedPending === 0 &&
            (receivedSubmissionsCount !== totalSubmissionsExpected ||
                (hasApprover ? approvalPending > 0 : evaluatedPending > 0))
        ) {
            status = PARTIALLY_EVALUATE;
            canApproveOrEvaluate = true;
        }

        if (
            receivedSubmissionsCount === totalSubmissionsExpected &&
            evaluatedEntries === totalSubmissionsExpected
        ) {
            status = ALL_EVALUATE;
            canApproveOrEvaluate = true;
        }
    }

    return { status, canApproveOrEvaluate };
};

const getAssignedRoleForEvaluator = ({ hasMultipleEvaluatorRole = false, child, roleId }) => {
    const matchedRole = hasMultipleEvaluatorRole
        ? child?.roles?.find((role) => isIDEquals(roleId, role.roleId))
        : child?.roles?.find((role) => role.evaluate?.isEnabled);

    let evaluateData = matchedRole?.evaluate;

    if (!evaluateData?.marks && !evaluateData?.rubrics?.length) {
        evaluateData = {
            marks: child?.formMarks,
            rubrics: child?.rubrics,
        };
    }

    if (!evaluateData?.globalRubrics?.length) {
        evaluateData.globalRubrics = child?.globalRubrics;
    }

    return evaluateData;
};

const getMatchedRoleForEvaluator = ({ evaluation = {}, type = EVALUATOR_KEY, userId = '' }) => {
    const matchEvaluatorRole = (collection) => {
        const entry = collection?.find((item) =>
            item.roles?.some(
                (role) => role[type] && role.users?.some((user) => isIDEquals(user.userId, userId)),
            ),
        );
        return entry?.roles?.find(
            (role) => role[type] && role.users?.some((user) => isIDEquals(user.userId, userId)),
        );
    };

    if (evaluation.typeOfEvaluation === STUDENT_LIST) {
        return matchEvaluatorRole(evaluation.students);
    }
    if (evaluation.typeOfEvaluation === STUDENT_GROUP) {
        return matchEvaluatorRole(evaluation.groups) || matchEvaluatorRole(evaluation.students);
    }
    if (evaluation.typeOfEvaluation === INFRASTRUCTURE) {
        return matchEvaluatorRole(evaluation.infrastructures);
    }

    return null;
};

const isPortfolioGroupMatch = ({ portfolio = {}, master = {}, group = {} }) => {
    if (!portfolio || !group) return false;

    // Base fields must match
    const baseMatch =
        isIDEquals(master?._program_id, portfolio?.programId) &&
        isIDEquals(master?.year, portfolio?.year) &&
        group?.term === portfolio?.term &&
        group?.level === portfolio?.level &&
        group?.curriculum === portfolio?.curriculumName;

    if (!baseMatch) return false;

    // If portfolio.rotation !== 'yes', rotation fields are ignored (your original logic)
    const rotationFlag = convertToString(portfolio?.rotation).toLowerCase() === YES;
    if (!rotationFlag) return true;

    // rotation === 'yes' ⇒ rotation & rotation_count must also match
    return (
        group?.rotation === portfolio?.rotation &&
        isIDEquals(group?.rotation_count, portfolio?.rotationCount)
    );
};

const getEvaluatedExpectedSubmissionCount = ({
    receivedSubmissions = [],
    canReturnCount = true,
}) => {
    return filterByConditions({
        array: receivedSubmissions,
        conditions: [
            [
                undefined,
                ({ item: { approvalStatus, roles = [] } = {} }) => {
                    if (!roles.some((role) => role.peerReview)) return true;

                    return approvalStatus === APPROVED;
                },
                { useCallback: true },
            ],
        ],
        canReturnCount,
    });
};

const getEvaluatedEntries = ({ userId, receivedSubmissions = [] }) => {
    return filterByConditions({
        array: receivedSubmissions,
        conditions: [
            [
                undefined,
                ({ item = {} }) =>
                    item.evaluations?.some(
                        (e) => e.status === EVALUATED && isIDEquals(e.userId, userId),
                    ),
                { useCallback: true },
            ],
        ],
        canReturnCount: true,
    });
};

const getApprovedEntries = ({ userId, isApprover, receivedSubmissions = [] }) => {
    return filterByConditions({
        array: receivedSubmissions,
        conditions: isApprover
            ? [
                  [
                      undefined,
                      ({ item = {} }) =>
                          item.approvals?.some(
                              (approval) =>
                                  approval.status === APPROVED &&
                                  isIDEquals(approval.userId, userId),
                          ),
                      { useCallback: true },
                  ],
              ]
            : [[APPROVAL_STATUS_KEY, APPROVED]],
        canReturnCount: true,
    });
};

const getApprovedResubmissionEntries = ({ userId, isApprover, receivedSubmissions = [] }) => {
    return filterByConditions({
        array: receivedSubmissions,
        conditions: isApprover
            ? [
                  [
                      undefined,
                      ({ item = {} }) =>
                          item.approvals?.some(
                              (approval) =>
                                  approval.status === RESUBMIT &&
                                  isIDEquals(approval.userId, userId),
                          ),
                      { useCallback: true },
                  ],
              ]
            : [[APPROVAL_STATUS_KEY, RESUBMIT]],
        canReturnCount: true,
    });
};

const getEvaluateResubmissionEntries = ({ userId, isEvaluator, receivedSubmissions = [] }) => {
    return filterByConditions({
        array: receivedSubmissions,
        conditions: [
            [
                undefined,
                ({ item = {} }) =>
                    item.evaluationStatus === RESUBMIT &&
                    (!isEvaluator ||
                        item.evaluations?.some((approval) => isIDEquals(approval.userId, userId))),
                { useCallback: true },
            ],
        ],
        canReturnCount: true,
    });
};

const getEvaluateRejectedEntries = ({ userId, isEvaluator, receivedSubmissions = [] }) => {
    return filterByConditions({
        array: receivedSubmissions,
        conditions: [
            [
                undefined,
                ({ item = {} }) =>
                    item.evaluationStatus === REJECTED &&
                    (!isEvaluator ||
                        item.evaluations?.some((approval) => isIDEquals(approval.userId, userId))),
                { useCallback: true },
            ],
        ],
        canReturnCount: true,
    });
};

const getApproveExpectedSubmissionCount = ({ receivedSubmissions = [] }) => {
    return filterByConditions({
        array: receivedSubmissions,
        conditions: [
            [
                undefined,
                ({ item = {} }) => item.roles?.some((role) => role.peerReview),
                { useCallback: true },
            ],
        ],
        canReturnCount: true,
    });
};

const getReceivedSubmissions = ({ studentResponses = [], isApprover }) => {
    return filterByConditions({
        array: studentResponses,
        conditions: [
            [
                undefined,
                ({ item = {} }) => {
                    const peerReview = item.roles?.some((role) => role.peerReview);
                    if (peerReview && item.approvalStatus === RESUBMIT) return false;

                    if (isApprover) return item.status === SUBMITTED;

                    return item.evaluationStatus !== RESUBMIT && item.status === SUBMITTED;
                },
                { useCallback: true },
            ],
        ],
    });
};

const getStudentResponseForComponent = ({
    componentId,
    studentResponses = [],
    studentIds = [],
}) => {
    return studentResponses.filter(
        (studentResponse) =>
            studentIds.includes(convertToString(studentResponse.student._id)) &&
            isIDEquals(studentResponse.componentId, componentId),
    );
};

const getApprovalInsightQuery = ({ userId, query = {} }) => {
    const common = { ...query, 'roles.peerReview': true };

    return {
        all: common,
        completed: { ...common, approvals: { $elemMatch: { status: APPROVED, userId } } },
        resubmit: { ...common, approvals: { $elemMatch: { status: RESUBMIT, userId } } },
        pending: { ...common, approvals: { $elemMatch: { status: NOT_STARTED, userId } } },
    };
};

const getEvaluationInsightQuery = ({ userId, query = {} }) => {
    const common = {
        ...query,
        $and: [
            {
                $or: [
                    { 'roles.peerReview': true, approvalStatus: APPROVED },
                    { roles: { $not: { $elemMatch: { peerReview: true } } } },
                ],
            },
        ],
    };

    return {
        readyForEvaluation: common,
        completed: { ...common, evaluations: { $elemMatch: { status: EVALUATED, userId } } },
        resubmit: { ...common, evaluations: { $elemMatch: { status: RESUBMIT, userId } } },
        rejected: { ...common, evaluations: { $elemMatch: { status: REJECTED, userId } } },
        pending: { ...common, evaluations: { $elemMatch: { status: NOT_STARTED, userId } } },
    };
};

const updateEvaluationAndApprovalStatusForStudents = ({
    students = [],
    responses = [],
    scheduleStudents = [],
    isApprover,
    userId,
    child = {},
    users = [],
}) => {
    students.forEach((student) => {
        const response = responses.find((r) => isIDEquals(r.student._id, student.studentId));
        if (scheduleStudents.length) {
            const scheduleStudent = scheduleStudents.find((s) =>
                isIDEquals(s._id, student.studentId),
            );
            student.isPresent = scheduleStudent?.status === PRESENT;
        }

        if (response) {
            const {
                approvalStatus,
                canApproveOrEvaluate,
                evaluationStatus,
                approvedOrEvaluatedBy,
            } = getStudentStatusForApprover({
                approvals: response?.approvals,
                hasMultipleApprover: child.hasMultipleApprover,
                hasMultipleEvaluator: child.hasMultipleEvaluator,
                evaluations: response?.evaluations,
                isApprover,
                userId,
                approvalStatus: response?.approvalStatus,
                evaluationStatus: response?.evaluationStatus,
            });

            student.status = response.status;
            student.approvalStatus = approvalStatus;
            student.canApproveOrEvaluate = canApproveOrEvaluate;
            student.evaluationStatus = evaluationStatus;
            student.responseId = response._id;
            student.submittedAt = response?.formTimestamps?.submittedAt || '-';
            student.session = response?.session;

            if (approvedOrEvaluatedBy) {
                const matchedUser = users.find((u) => isIDEquals(u.userId, approvedOrEvaluatedBy));
                student.approvedOrEvaluatedBy = matchedUser || null;
            }
        }
    });
};

module.exports = {
    buildStudentResponseQuery,
    getStudentStatusForApprover,
    getPortfolioStatus,
    getAssignedRoleForEvaluator,
    getMatchedRoleForEvaluator,
    isPortfolioGroupMatch,
    getEvaluatedExpectedSubmissionCount,
    getEvaluatedEntries,
    getApprovedEntries,
    getApproveExpectedSubmissionCount,
    getReceivedSubmissions,
    getStudentResponseForComponent,
    getApprovedResubmissionEntries,
    getEvaluateResubmissionEntries,
    getEvaluateRejectedEntries,
    getApprovalInsightQuery,
    getEvaluationInsightQuery,
    updateEvaluationAndApprovalStatusForStudents,
};
