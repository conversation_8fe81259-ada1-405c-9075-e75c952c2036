const {
    Schema,
    model,
    Types: { ObjectId },
} = require('mongoose');
const {
    PORTFOLIO_ASSIGN_EVALUATION_MODEL,
    INFRASTRUCTURE,
    STUDENT_GROUP,
    STUDENT_LIST,
    SINGLE,
    MULT<PERSON>LE,
} = require('../../common/utils/constants');

const constant = require('../../../lib/utility/constants');

const NameSchema = {
    first: String,
    middle: String,
    last: String,
    family: String,
};

const UserSchema = {
    _id: false,
    userId: ObjectId,
    name: NameSchema,
    email: String,
    employeeId: String,
    gender: String,
};

const ExternalUserSchema = {
    name: String,
    email: String,
    mobile: String,
    gender: String,
};

const SessionSchema = {
    mode: { type: String, enum: [SINGLE, MULTIPLE] },
    startDate: { type: Date },
    endDate: { type: Date },
    start: {
        hour: { type: Number },
        minute: { type: Number },
        format: { type: String },
    },
    end: {
        hour: { type: Number },
        minute: { type: Number },
        format: { type: String },
    },
    date: { type: Date },
};

const PrepareAndPublishSchema = {
    ...UserSchema,
    session: SessionSchema,
    formId: ObjectId,
    _id: false,
};

const RoleSchema = {
    roleId: { type: ObjectId, required: true },
    users: [UserSchema],
    externalUsers: [ExternalUserSchema],
    prepareAndPublish: [PrepareAndPublishSchema],
    _id: false,
    approver: { type: Boolean, default: false },
    evaluator: { type: Boolean, default: false },
    isAnonymousEntry: { type: Boolean, default: false },
};

const InfrastructureSchema = {
    infrastructureId: { type: ObjectId, required: true },
    roles: [RoleSchema],
    _id: false,
};

const StudentSchema = {
    studentId: { type: ObjectId, required: true },
    academicNo: { type: String, required: true },
    name: NameSchema,
    gender: { type: String, required: true },
    roles: [RoleSchema],
    _id: false,
};

const GroupSchema = {
    groupId: ObjectId,
    groupName: String,
    deliveryTypeSymbol: String,
    masterGroupName: String,
    roles: [RoleSchema],
    _id: false,
};

const schema = new Schema(
    {
        programId: { type: ObjectId, required: true, ref: constant.DIGI_PROGRAM },
        courseId: { type: ObjectId, required: true, ref: constant.DIGI_COURSE },
        institutionCalendarId: { type: ObjectId, required: true },
        portfolioId: { type: ObjectId, required: true },
        componentId: { type: ObjectId, required: true },
        childrenId: { type: ObjectId, required: true },

        createdBy: {
            id: { type: ObjectId, required: true, ref: constant.BY_USER },
        },

        typeOfEvaluation: {
            type: String,
            enum: [INFRASTRUCTURE, STUDENT_GROUP, STUDENT_LIST],
            required: true,
        },

        deliveryTypeId: ObjectId,
        deliveryTypeName: String,
        deliveryTypeSymbol: String,
        sessionId: ObjectId,
        infrastructures: [InfrastructureSchema],
        students: [StudentSchema],
        groups: [GroupSchema],
        externalUsers: [
            {
                name: String,
                email: String,
                mobile: Number,
                gender: String,
                roleId: { type: ObjectId, required: true },
                approver: { type: Boolean, default: false },
                evaluator: { type: Boolean, default: false },
                facial: {
                    key: { type: String, trim: true },
                    bucket: { type: String, trim: true },
                    type: { type: String, trim: true },
                },
            },
        ],
    },
    { timestamps: true },
);

module.exports = model(PORTFOLIO_ASSIGN_EVALUATION_MODEL, schema);
