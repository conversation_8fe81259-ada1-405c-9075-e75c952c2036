const { Joi } = require('../../common/middlewares/validation');

const { objectIdSchema, objectIdRQSchema } = require('../../../lib/utility/validationSchemas');
const {
    EVALUATOR,
    APPROVER,
    CHIP_COMPLETED,
    CHIP_PENDING,
    CHIP_RESUBMIT,
    CHIP_REJECT,
} = require('../../common/utils/constants');
const { REVOKE, RESUBMIT, APPROVED } = require('../../common/utils/enums');

const getEvaluatorAndApproverDashboardSchema = Joi.object({
    query: Joi.object({
        institutionCalendarId: objectIdRQSchema,
        type: Joi.string().valid(EVALUATOR, APPROVER).required(),
        chipType: Joi.string()
            .valid(CHIP_COMPLETED, CHIP_PENDING, CHIP_RESUBMIT, CHIP_REJECT)
            .optional(),
    }),
});

const getListOfComponentsForDashboardSchema = Joi.object({
    query: Joi.object({
        portfolioId: objectIdRQSchema,
        type: Joi.string().valid(EVALUATOR, APPROVER).required(),
    }),
});

const getComponentChildrenSchema = Joi.object({
    query: Joi.object({
        portfolioId: objectIdRQSchema,
        componentId: objectIdRQSchema,
    }),
});

const getScheduleForApproverOrEvaluatorSchema = Joi.object({
    query: Joi.object({
        portfolioId: objectIdRQSchema,
        componentId: objectIdRQSchema,
        type: Joi.string().valid(EVALUATOR, APPROVER).required(),
        chipType: Joi.string()
            .valid(CHIP_COMPLETED, CHIP_PENDING, CHIP_RESUBMIT, CHIP_REJECT)
            .optional(),
    }),
});

const getStudentsForScheduleSchema = Joi.object({
    query: Joi.object({
        portfolioId: objectIdRQSchema,
        componentId: objectIdRQSchema,
        childrenId: objectIdRQSchema,
        scheduleId: objectIdSchema,
        deliveryTypeId: objectIdSchema,
        type: Joi.string().valid(EVALUATOR, APPROVER).required(),
    }),
});

const updateApproveOrRejectStatusSchema = Joi.object({
    body: Joi.object({
        portfolioId: objectIdRQSchema,
        componentId: objectIdSchema,
        childrenId: objectIdSchema,
        type: Joi.string().valid(REVOKE, RESUBMIT, APPROVED).required(),
    }),
});

const updateEvaluatorSchema = Joi.object({
    body: Joi.object({
        portfolioId: objectIdRQSchema,
        componentId: objectIdRQSchema,
        childrenId: objectIdRQSchema,
        type: Joi.string(),
    }),
});

const getRubricsAndMarksForEvaluatorSchema = Joi.object({
    query: Joi.object({
        portfolioId: objectIdRQSchema,
        componentId: objectIdRQSchema,
        childrenId: objectIdRQSchema,
    }),
});

const getInsightsWithRubricsAndMarksSchema = Joi.object({
    query: Joi.object({
        responseId: objectIdRQSchema,
    }),
});

module.exports = {
    getEvaluatorAndApproverDashboardSchema,
    getListOfComponentsForDashboardSchema,
    getComponentChildrenSchema,
    getScheduleForApproverOrEvaluatorSchema,
    getStudentsForScheduleSchema,
    updateApproveOrRejectStatusSchema,
    updateEvaluatorSchema,
    getRubricsAndMarksForEvaluatorSchema,
    getInsightsWithRubricsAndMarksSchema,
};
