const StudentResponseModel = require('../student-response/student-response.model');

const { convertToMongoObjectId } = require('../../common/utils/common.util');
const { APPROVED, EVALUATED, RESUBMIT, REJECTED } = require('../../common/utils/enums');
const { calculateRubricPoint } = require('../rubric/rubric.helper');

const buildPortfolioCourseFilters = ({
    status,
    institutionCalendarId,
    programId,
    courseDetails = [],
}) => {
    const query = {
        ...(institutionCalendarId && {
            institutionCalendarId: convertToMongoObjectId(institutionCalendarId),
        }),
        ...(programId && { programId: convertToMongoObjectId(programId) }),
    };

    return courseDetails.map(
        ({ courseId, level, term, year, rotation, curriculumId, rotationCount }) => ({
            ...query,
            courseId: convertToMongoObjectId(courseId),
            curriculumId: convertToMongoObjectId(curriculumId),
            level,
            term,
            year,
            rotation,
            ...(rotationCount && { rotationCount }),
            ...(status && { status }),
        }),
    );
};

const getCountFromStudentResponse = async ({
    matchQuery = {},
    canShowApprovedCount = true,
    canShowApprovedResubmitCount = false,
    canShowEvaluatedCount = true,
    canShowEvaluatedResubmitCount = false,
    canShowEvaluateRejectCount = false,
}) => {
    const pipeline = [];

    // 1) Match
    pipeline.push({ $match: matchQuery });

    // 2) Add isPeerReview only if any approval-related metrics are requested
    const isApprover = canShowApprovedCount || canShowApprovedResubmitCount;
    if (isApprover) {
        pipeline.push({
            $addFields: {
                isPeerReview: {
                    $gt: [
                        {
                            $size: {
                                $filter: {
                                    input: '$roles',
                                    as: 'r',
                                    cond: { $eq: ['$$r.peerReview', true] },
                                },
                            },
                        },
                        0,
                    ],
                },
            },
        });
    }

    // 3) Group
    const groupStage = { _id: '$parentPortfolioId' };

    // Evaluation metrics (denominator = all docs)
    const isEvaluator =
        canShowEvaluatedCount || canShowEvaluatedResubmitCount || canShowEvaluateRejectCount;
    if (isEvaluator) {
        groupStage.totalEvaluated = { $sum: 1 };

        if (canShowEvaluatedCount) {
            groupStage.evaluatedCount = {
                $sum: { $cond: [{ $eq: ['$evaluationStatus', EVALUATED] }, 1, 0] },
            };
        }

        if (canShowEvaluatedResubmitCount) {
            groupStage.evaluatedResubmitCount = {
                $sum: { $cond: [{ $eq: ['$evaluationStatus', RESUBMIT] }, 1, 0] },
            };
        }

        if (canShowEvaluateRejectCount) {
            groupStage.evaluateRejectCount = {
                $sum: { $cond: [{ $eq: ['$evaluationStatus', REJECTED] }, 1, 0] },
            };
        }
    }

    // Approval metrics (denominator = only peer-review docs)
    if (isApprover) {
        groupStage.peerReviewTotal = {
            $sum: { $cond: [{ $eq: ['$isPeerReview', true] }, 1, 0] },
        };

        if (canShowApprovedCount) {
            groupStage.approvedPeerCount = {
                $sum: {
                    $cond: [
                        {
                            $and: [
                                { $eq: ['$isPeerReview', true] },
                                { $eq: ['$approvalStatus', APPROVED] },
                            ],
                        },
                        1,
                        0,
                    ],
                },
            };
        }

        if (canShowApprovedResubmitCount) {
            groupStage.approvedResubmitCount = {
                $sum: {
                    $cond: [
                        {
                            $and: [
                                { $eq: ['$isPeerReview', true] },
                                { $eq: ['$approvalStatus', RESUBMIT] },
                            ],
                        },
                        1,
                        0,
                    ],
                },
            };
        }
    }

    pipeline.push({ $group: groupStage });

    // 4) Project
    const projectStage = { _id: 0, portfolioId: '$_id' };

    if (isEvaluator) {
        projectStage.totalEvaluated = 1;

        if (canShowEvaluatedCount) {
            projectStage.evaluatedCount = 1;
            projectStage.evaluated = { $eq: ['$evaluatedCount', '$totalEvaluated'] };
        }
        if (canShowEvaluatedResubmitCount) projectStage.evaluatedResubmitCount = 1;
        if (canShowEvaluateRejectCount) projectStage.evaluateRejectCount = 1;
    }

    if (isApprover) {
        projectStage.peerReviewTotal = 1;
        if (canShowApprovedCount) {
            projectStage.approvedPeerCount = 1;
            projectStage.approved = {
                $and: [
                    { $eq: ['$approvedPeerCount', '$peerReviewTotal'] },
                    { $gt: ['$peerReviewTotal', 0] },
                ],
            };
        }
        if (canShowApprovedResubmitCount) projectStage.approvedResubmitCount = 1;
    }

    pipeline.push({ $project: projectStage });

    return StudentResponseModel.aggregate(pipeline);
};

const getTotalMarksForChild = ({ child }) => {
    let totalMarks = 0;
    const hasRoleEvaluation = child.roles?.some(
        (role) =>
            role.evaluate?.isEnabled && (role.evaluate?.rubrics?.length || role.evaluate?.marks),
    );

    if (hasRoleEvaluation) {
        const evaluators = child.roles.filter((role) => role.evaluate?.isEnabled);
        const total = evaluators.reduce((sum, role) => {
            return (
                sum +
                (role.evaluate.isMarkSelected
                    ? role.evaluate.marks ?? 0
                    : calculateRubricPoint({ rubrics: role.evaluate.rubrics }))
            );
        }, 0);
        totalMarks = Math.round(total / evaluators.length);
    } else {
        totalMarks = child.rubrics?.length
            ? calculateRubricPoint({ rubrics: child.rubrics })
            : child.formMarks;
    }

    return totalMarks;
};

module.exports = {
    buildPortfolioCourseFilters,
    getCountFromStudentResponse,
    getTotalMarksForChild,
};
