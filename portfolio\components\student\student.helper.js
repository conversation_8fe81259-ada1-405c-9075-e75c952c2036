const {
    WITH_IN_WEEK,
    WITH_IN_DAY,
    WITH_IN_SCHEDULE,
    OPEN_ALWAYS,
    SINGLE,
    MULTIPLE,
} = require('../../common/utils/constants');

const {
    NOT_STARTED,
    ON_GOING,
    SUBMITTED,
    IN_PROGRESS,
    APPROVAL_PENDING,
    EVALUATION_PENDING,
    APPROVED,
    RESUBMIT,
    REJECTED,
    EVALUATED,
    COMPLETED,
} = require('../../common/utils/enums');
const { isIDEquals } = require('../../common/utils/common.util');

const getSubmissionSessionTimeline = ({ submissionSession = {} }) => {
    const now = new Date();
    if (submissionSession?.timeline === OPEN_ALWAYS) {
        return true;
    }

    if (submissionSession?.date && submissionSession?.end) {
        const submissionSessionDate = new Date(submissionSession.date);

        // Build end time
        let endHour = (submissionSession?.end?.hour || 0) % 12;
        if (submissionSession?.end?.format === 'PM') endHour += 12;
        const endTime = new Date(submissionSessionDate);
        endTime.setHours(endHour, submissionSession?.end?.minute, 0, 0);

        return now >= submissionSessionDate && now <= endTime;
    }
};

const canStudentStartExam = ({ session, submissionSession }) => {
    const now = new Date();

    if (submissionSession?.timeline || (submissionSession?.date && submissionSession?.end)) {
        return getSubmissionSessionTimeline({ submissionSession });
    }

    if (session.mode === SINGLE) {
        // Parse the exam date
        const sessionDate = new Date(session.date);

        // Build start time
        let startHour = (session?.start?.hour || 0) % 12;
        if (session?.start?.format === 'PM') startHour += 12;
        const startTime = new Date(sessionDate);
        startTime.setHours(startHour, session?.start?.minute, 0, 0);

        // Build end time
        let endHour = (session?.end?.hour || 0) % 12;
        if (session?.end?.format === 'PM') endHour += 12;
        const endTime = new Date(sessionDate);
        endTime.setHours(endHour, session?.end?.minute, 0, 0);

        return now >= startTime && now <= endTime;
    }

    if (session.mode === MULTIPLE) {
        const startDate = new Date(session?.startDate);
        const endDate = new Date(session?.endDate);
        return now >= startDate && now <= endDate;
    }

    return false;
};

const canStartExamFromScheduleType = ({ schedule, type = WITH_IN_SCHEDULE, submissionSession }) => {
    if (submissionSession?.timeline || (submissionSession?.date && submissionSession?.end)) {
        return getSubmissionSessionTimeline({ submissionSession });
    }

    if (!schedule?.scheduleStartDateAndTime) return false;

    const now = new Date();
    const startUTC = new Date(schedule.scheduleStartDateAndTime);

    let endUTC = null;

    switch (type) {
        case OPEN_ALWAYS:
            return now >= startUTC;

        case WITH_IN_WEEK:
            endUTC = new Date(startUTC);
            endUTC.setUTCDate(endUTC.getUTCDate() + 7);
            break;

        case WITH_IN_DAY:
            endUTC = new Date(startUTC);
            endUTC.setUTCHours(23, 59, 59, 999);
            break;

        case WITH_IN_SCHEDULE:
            if (!schedule.scheduleEndDateAndTime) return false;
            endUTC = new Date(schedule.scheduleEndDateAndTime);
            break;

        default:
            return false;
    }

    return now >= startUTC && now <= endUTC;
};

const isSessionPast = (session) => {
    const now = new Date();

    if (session.mode === MULTIPLE) {
        const endOfDay = new Date(session.endDate);
        endOfDay.setHours(23, 59, 59, 999);
        return endOfDay < now;
    }

    if (session.mode === SINGLE) {
        const date = new Date(session.date);

        let hours = session.end.hour % 12;
        if (session.end.format.toUpperCase() === 'PM') {
            hours += 12;
        }

        date.setHours(hours, session.end.minute, 0, 0);
        return date < now;
    }
};

const getStudentStatus = ({
    status,
    approvalStatus = NOT_STARTED,
    evaluationStatus = NOT_STARTED,
    hasApproval = false,
}) => {
    if (status === NOT_STARTED) return NOT_STARTED;
    if (status === ON_GOING) return IN_PROGRESS;

    if ([SUBMITTED, RESUBMIT, REJECTED].includes(status)) {
        if (hasApproval && evaluationStatus === NOT_STARTED) {
            if ([IN_PROGRESS, NOT_STARTED].includes(approvalStatus)) return APPROVAL_PENDING;
            if (approvalStatus === APPROVED) return EVALUATION_PENDING;
            if (approvalStatus === RESUBMIT) return RESUBMIT;
        }

        if ([RESUBMIT, REJECTED, EVALUATED].includes(evaluationStatus))
            return evaluationStatus === EVALUATED ? COMPLETED : evaluationStatus;

        return EVALUATION_PENDING;
    }
    return NOT_STARTED;
};

const handleExtraEntries = ({ component, schedules, studentResponses }) => {
    const formattedSchedules = [];

    schedules.forEach((schedule) => {
        const children = component.children.filter((child) =>
            isIDEquals(child.scheduleId, schedule._id),
        );

        children.forEach((child) => {
            const extraEntries = child.extraEntries;
        });
    });
    return formattedSchedules;
};

module.exports = {
    canStudentStartExam,
    canStartExamFromScheduleType,
    isSessionPast,
    getStudentStatus,
    handleExtraEntries,
};
